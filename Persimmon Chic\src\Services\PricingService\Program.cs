using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.PricingService.Models;
using PersimmonChic.PricingService.Services;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllers();

// 配置Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Persimmon Chic Pricing Service",
        Version = "v1",
        Description = "价格服务API - 提供动态定价和渠道价格管理功能",
        Contact = new OpenApiContact
        {
            Name = "Persimmon Chic Team",
            Email = "<EMAIL>"
        }
    });

    // 添加JWT认证配置
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // 包含XML注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// 配置JWT认证
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5000", "http://localhost:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// 配置Redis缓存
var redisConnectionString = builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379";
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConnectionString;
    options.InstanceName = "PersimmonChic.Pricing";
});

// 注册数据访问层
builder.Services.AddScoped<IRepository<ProductPrice>, InMemoryRepository<ProductPrice>>();
builder.Services.AddScoped<IRepository<ChannelPrice>, InMemoryRepository<ChannelPrice>>();
builder.Services.AddScoped<IRepository<UserTierPrice>, InMemoryRepository<UserTierPrice>>();
builder.Services.AddScoped<IRepository<RegionalPrice>, InMemoryRepository<RegionalPrice>>();
builder.Services.AddScoped<IRepository<PriceHistory>, InMemoryRepository<PriceHistory>>();
builder.Services.AddScoped<IRepository<DynamicPricingRule>, InMemoryRepository<DynamicPricingRule>>();
builder.Services.AddScoped<IRepository<PricingStrategy>, InMemoryRepository<PricingStrategy>>();

// 注册业务服务
builder.Services.AddScoped<IPricingService, PersimmonChic.PricingService.Services.PricingService>();
builder.Services.AddScoped<IDynamicPricingService, DynamicPricingService>();
builder.Services.AddScoped<IPriceCacheService, PriceCacheService>();
builder.Services.AddScoped<IPriceNotificationService, PriceNotificationService>();

// 注册HTTP客户端工厂
builder.Services.AddHttpClient();

// 配置日志
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    logging.AddDebug();
    
    if (builder.Environment.IsDevelopment())
    {
        logging.SetMinimumLevel(LogLevel.Debug);
    }
    else
    {
        logging.SetMinimumLevel(LogLevel.Information);
    }
});

// 配置健康检查
builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Pricing Service V1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}

app.UseHttpsRedirection();

app.UseCors();

app.UseAuthentication();
app.UseAuthorization();

// 添加请求日志中间件
app.Use(async (context, next) =>
{
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    var requestId = Guid.NewGuid().ToString("N")[..8];
    
    using (logger.BeginScope(new Dictionary<string, object> { ["RequestId"] = requestId }))
    {
        logger.LogInformation("开始处理请求: {Method} {Path}", 
            context.Request.Method, context.Request.Path);
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            await next();
        }
        finally
        {
            stopwatch.Stop();
            logger.LogInformation("请求处理完成: {Method} {Path} - {StatusCode} - {ElapsedMs}ms",
                context.Request.Method, context.Request.Path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
        }
    }
});

app.MapControllers();

// 配置健康检查端点
app.MapHealthChecks("/health");

// 添加服务信息端点
app.MapGet("/info", () => new
{
    Service = "PersimmonChic.PricingService",
    Version = "1.0.0",
    Environment = app.Environment.EnvironmentName,
    StartTime = DateTime.UtcNow,
    Features = new[]
    {
        "动态定价引擎",
        "渠道价格管理",
        "用户等级定价",
        "地域价格差异",
        "价格缓存优化",
        "价格变更通知",
        "批量价格更新",
        "价格历史追踪"
    }
});

// 初始化默认数据
await InitializeDefaultDataAsync(app.Services);

app.Run();

/// <summary>
/// 初始化默认数据
/// </summary>
static async Task InitializeDefaultDataAsync(IServiceProvider services)
{
    using var scope = services.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    
    try
    {
        logger.LogInformation("开始初始化默认价格数据...");
        
        // 初始化默认商品价格
        var productPriceRepository = scope.ServiceProvider.GetRequiredService<IRepository<ProductPrice>>();
        
        var defaultPrices = new List<ProductPrice>
        {
            new ProductPrice
            {
                Id = 1,
                ProductId = 1,
                BasePrice = 99.99m,
                SalePrice = 89.99m,
                Currency = "CNY",
                IsActive = true
            },
            new ProductPrice
            {
                Id = 2,
                ProductId = 2,
                BasePrice = 199.99m,
                Currency = "CNY",
                IsActive = true
            },
            new ProductPrice
            {
                Id = 3,
                ProductId = 3,
                BasePrice = 299.99m,
                SalePrice = 249.99m,
                Currency = "CNY",
                IsActive = true
            }
        };

        foreach (var price in defaultPrices)
        {
            await productPriceRepository.AddAsync(price);
        }

        // 初始化默认动态价格规则
        var dynamicRuleRepository = scope.ServiceProvider.GetRequiredService<IRepository<DynamicPricingRule>>();
        
        var defaultRules = new List<DynamicPricingRule>
        {
            new DynamicPricingRule
            {
                Id = 1,
                Name = "高需求价格调整",
                Description = "当商品需求较高时提高价格",
                Condition = "high_demand",
                AdjustmentType = PriceAdjustmentType.Percentage,
                AdjustmentValue = 10, // 提高10%
                IsActive = true,
                Priority = 1
            },
            new DynamicPricingRule
            {
                Id = 2,
                Name = "低库存价格调整",
                Description = "当库存较低时提高价格",
                Condition = "low_stock",
                AdjustmentType = PriceAdjustmentType.Percentage,
                AdjustmentValue = 15, // 提高15%
                IsActive = true,
                Priority = 2
            },
            new DynamicPricingRule
            {
                Id = 3,
                Name = "周末促销",
                Description = "周末时段降低价格",
                Condition = "weekend",
                AdjustmentType = PriceAdjustmentType.Percentage,
                AdjustmentValue = -5, // 降低5%
                IsActive = true,
                Priority = 3
            }
        };

        foreach (var rule in defaultRules)
        {
            await dynamicRuleRepository.AddAsync(rule);
        }

        // 初始化默认渠道价格
        var channelPriceRepository = scope.ServiceProvider.GetRequiredService<IRepository<ChannelPrice>>();
        
        var defaultChannelPrices = new List<ChannelPrice>
        {
            new ChannelPrice
            {
                Id = 1,
                ProductId = 1,
                ChannelCode = "online",
                Price = 89.99m,
                Currency = "CNY",
                IsActive = true
            },
            new ChannelPrice
            {
                Id = 2,
                ProductId = 1,
                ChannelCode = "retail",
                Price = 99.99m,
                Currency = "CNY",
                IsActive = true
            }
        };

        foreach (var channelPrice in defaultChannelPrices)
        {
            await channelPriceRepository.AddAsync(channelPrice);
        }
        
        logger.LogInformation("默认价格数据初始化完成");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "初始化默认数据时发生错误");
    }
}
