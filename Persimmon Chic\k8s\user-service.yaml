# User Service部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: user-service
    app.kubernetes.io/component: business-service
    app.kubernetes.io/part-of: persimmon-chic
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: user-service
  template:
    metadata:
      labels:
        app.kubernetes.io/name: user-service
        app.kubernetes.io/component: business-service
        app.kubernetes.io/version: "1.0.0"
    spec:
      containers:
      - name: user-service
        image: persimmonchic/persimmonchic-user-service:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: ASPNETCORE_URLS
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: CONNECTIONSTRINGS__DEFAULTCONNECTION
        - name: ConnectionStrings__Redis
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: CONNECTIONSTRINGS__REDIS
        - name: JwtSettings__SecretKey
          valueFrom:
            secretKeyRef:
              name: persimmon-chic-secrets
              key: JWT_SECRET_KEY
        - name: SQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: persimmon-chic-secrets
              key: SQL_PASSWORD
        envFrom:
        - configMapRef:
            name: persimmon-chic-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      imagePullSecrets:
      - name: persimmon-chic-registry-secret

---
# User Service服务
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: user-service
    app.kubernetes.io/component: business-service
spec:
  selector:
    app.kubernetes.io/name: user-service
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP

---
# User Service水平自动扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: user-service
    app.kubernetes.io/component: business-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
