using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using PersimmonChic.RiskControlService.Services;
using System.Security.Claims;

namespace PersimmonChic.RiskControlService.Hubs;

/// <summary>
/// 风险监控SignalR Hub
/// </summary>
[Authorize]
public class RiskMonitorHub : Hub
{
    private readonly ILogger<RiskMonitorHub> _logger;
    private readonly IRiskControlService _riskControlService;

    public RiskMonitorHub(
        ILogger<RiskMonitorHub> logger,
        IRiskControlService riskControlService)
    {
        _logger = logger;
        _riskControlService = riskControlService;
    }

    /// <summary>
    /// 客户端连接时调用
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var connectionId = Context.ConnectionId;
        
        _logger.LogInformation("用户 {UserId} 连接到风险监控Hub，连接ID: {ConnectionId}", userId, connectionId);
        
        // 将用户加入到监控组
        await Groups.AddToGroupAsync(connectionId, "RiskMonitors");
        
        // 发送欢迎消息
        await Clients.Caller.SendAsync("Connected", new
        {
            Message = "已连接到风险监控系统",
            Timestamp = DateTime.UtcNow,
            ConnectionId = connectionId
        });

        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时调用
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var connectionId = Context.ConnectionId;
        
        _logger.LogInformation("用户 {UserId} 断开风险监控Hub连接，连接ID: {ConnectionId}", userId, connectionId);
        
        if (exception != null)
        {
            _logger.LogError(exception, "用户 {UserId} 连接异常断开", userId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 订阅用户风险监控
    /// </summary>
    /// <param name="userId">要监控的用户ID</param>
    public async Task SubscribeToUserRiskMonitoring(int userId)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            _logger.LogInformation("用户 {CurrentUserId} 订阅用户 {UserId} 的风险监控", currentUserId, userId);
            
            // 将连接加入到特定用户的监控组
            await Groups.AddToGroupAsync(Context.ConnectionId, $"UserRisk_{userId}");
            
            // 获取用户当前风险档案
            var profileResponse = await _riskControlService.GetUserRiskProfileAsync(userId);
            if (profileResponse.Success && profileResponse.Data != null)
            {
                await Clients.Caller.SendAsync("UserRiskProfile", new
                {
                    UserId = userId,
                    Profile = profileResponse.Data,
                    Timestamp = DateTime.UtcNow
                });
            }

            await Clients.Caller.SendAsync("SubscriptionConfirmed", new
            {
                UserId = userId,
                Message = $"已订阅用户 {userId} 的风险监控",
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅用户风险监控时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "订阅风险监控失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 取消订阅用户风险监控
    /// </summary>
    /// <param name="userId">要取消监控的用户ID</param>
    public async Task UnsubscribeFromUserRiskMonitoring(int userId)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            _logger.LogInformation("用户 {CurrentUserId} 取消订阅用户 {UserId} 的风险监控", currentUserId, userId);
            
            // 从特定用户的监控组中移除连接
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"UserRisk_{userId}");
            
            await Clients.Caller.SendAsync("UnsubscriptionConfirmed", new
            {
                UserId = userId,
                Message = $"已取消订阅用户 {userId} 的风险监控",
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅用户风险监控时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "取消订阅风险监控失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取实时风险指标
    /// </summary>
    public async Task GetRealTimeMetrics()
    {
        try
        {
            var metricNames = new List<string>
            {
                "active_users",
                "high_risk_events",
                "login_attempts",
                "blocked_attempts"
            };

            var metricsResponse = await _riskControlService.GetRiskMetricsAsync(metricNames, 60);
            
            await Clients.Caller.SendAsync("RealTimeMetrics", new
            {
                Metrics = metricsResponse.Data,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实时风险指标时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "获取实时指标失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 请求用户风险评估
    /// </summary>
    /// <param name="userId">用户ID</param>
    public async Task RequestUserRiskAssessment(int userId)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            _logger.LogInformation("用户 {CurrentUserId} 请求用户 {UserId} 的风险评估", currentUserId, userId);
            
            // 获取用户风险档案
            var profileResponse = await _riskControlService.GetUserRiskProfileAsync(userId);
            
            // 获取最近的异常登录记录
            var anomalousLoginsResponse = await _riskControlService.GetAnomalousLoginRecordsAsync(
                userId, DateTime.UtcNow.AddDays(-7), DateTime.UtcNow);

            await Clients.Caller.SendAsync("UserRiskAssessment", new
            {
                UserId = userId,
                RiskProfile = profileResponse.Data,
                AnomalousLogins = anomalousLoginsResponse.Data,
                AssessmentTime = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "请求用户风险评估时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "获取风险评估失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 发送测试告警
    /// </summary>
    /// <param name="message">告警消息</param>
    public async Task SendTestAlert(string message)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 发送测试告警: {Message}", currentUserId, message);
            
            await Clients.All.SendAsync("TestAlert", new
            {
                Message = $"测试告警: {message}",
                SenderId = currentUserId,
                Timestamp = DateTime.UtcNow,
                Type = "test"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送测试告警时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "发送测试告警失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 加入管理员组
    /// </summary>
    public async Task JoinAdminGroup()
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            
            // 这里应该检查用户是否有管理员权限
            // 简化实现，实际应该验证用户角色
            if (IsAdmin())
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, "Administrators");
                
                await Clients.Caller.SendAsync("AdminGroupJoined", new
                {
                    Message = "已加入管理员组",
                    Timestamp = DateTime.UtcNow
                });
                
                _logger.LogInformation("用户 {UserId} 加入管理员组", currentUserId);
            }
            else
            {
                await Clients.Caller.SendAsync("Error", new
                {
                    Message = "权限不足，无法加入管理员组",
                    Timestamp = DateTime.UtcNow
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加入管理员组时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "加入管理员组失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 离开管理员组
    /// </summary>
    public async Task LeaveAdminGroup()
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "Administrators");
            
            await Clients.Caller.SendAsync("AdminGroupLeft", new
            {
                Message = "已离开管理员组",
                Timestamp = DateTime.UtcNow
            });
            
            var currentUserId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 离开管理员组", currentUserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "离开管理员组时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "离开管理员组失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取连接状态
    /// </summary>
    public async Task GetConnectionStatus()
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            
            await Clients.Caller.SendAsync("ConnectionStatus", new
            {
                ConnectionId = Context.ConnectionId,
                UserId = currentUserId,
                ConnectedAt = DateTime.UtcNow,
                IsAuthenticated = Context.User?.Identity?.IsAuthenticated ?? false,
                UserAgent = Context.GetHttpContext()?.Request.Headers["User-Agent"].ToString()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取连接状态时发生错误");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "获取连接状态失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    // 私有辅助方法
    private string? GetCurrentUserId()
    {
        return Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    private bool IsAdmin()
    {
        // 简化的管理员权限检查
        // 实际应该检查用户角色或权限
        return Context.User?.IsInRole("Admin") ?? false;
    }
}
