# Persimmon Chic Gateway Service Dockerfile
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制共享项目文件
COPY ["src/Shared/Models/PersimmonChic.Shared.Models.csproj", "src/Shared/Models/"]
COPY ["src/Shared/Contracts/PersimmonChic.Shared.Contracts.csproj", "src/Shared/Contracts/"]
COPY ["src/Shared/Common/PersimmonChic.Shared.Common.csproj", "src/Shared/Common/"]

# 复制Gateway项目文件
COPY ["src/Gateway/PersimmonChic.Gateway.csproj", "src/Gateway/"]

# 还原依赖
RUN dotnet restore "src/Gateway/PersimmonChic.Gateway.csproj"

# 复制所有源代码
COPY . .

# 构建项目
WORKDIR "/src/src/Gateway"
RUN dotnet build "PersimmonChic.Gateway.csproj" -c Release -o /app/build

# 发布阶段
FROM build AS publish
RUN dotnet publish "PersimmonChic.Gateway.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 运行时阶段
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app

# 安装curl用于健康检查
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# 复制发布的文件
COPY --from=publish /app/publish .

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

# 暴露端口
EXPOSE 80
EXPOSE 443

# 设置环境变量
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# 启动应用
ENTRYPOINT ["dotnet", "PersimmonChic.Gateway.dll"]
