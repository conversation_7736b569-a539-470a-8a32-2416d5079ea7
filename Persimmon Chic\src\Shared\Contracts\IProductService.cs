using PersimmonChic.Shared.Models;

namespace PersimmonChic.Shared.Contracts;

/// <summary>
/// 商品服务接口
/// </summary>
public interface IProductService
{
    /// <summary>
    /// 根据ID获取商品信息
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <returns>商品信息</returns>
    Task<ApiResponse<Product>> GetProductByIdAsync(int productId);

    /// <summary>
    /// 创建商品
    /// </summary>
    /// <param name="product">商品信息</param>
    /// <returns>创建结果</returns>
    Task<ApiResponse<Product>> CreateProductAsync(Product product);

    /// <summary>
    /// 更新商品信息
    /// </summary>
    /// <param name="product">商品信息</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<Product>> UpdateProductAsync(Product product);

    /// <summary>
    /// 删除商品
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <returns>删除结果</returns>
    Task<ApiResponse<bool>> DeleteProductAsync(int productId);

    /// <summary>
    /// 搜索商品
    /// </summary>
    /// <param name="request">搜索请求</param>
    /// <returns>商品列表</returns>
    Task<ApiResponse<PagedResponse<Product>>> SearchProductsAsync(ProductSearchRequest request);

    /// <summary>
    /// 获取商品列表（分页）
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>商品列表</returns>
    Task<ApiResponse<PagedResponse<Product>>> GetProductsAsync(PagedRequest request);

    /// <summary>
    /// 根据分类获取商品
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <param name="request">分页请求</param>
    /// <returns>商品列表</returns>
    Task<ApiResponse<PagedResponse<Product>>> GetProductsByCategoryAsync(int categoryId, PagedRequest request);

    /// <summary>
    /// 获取热门商品
    /// </summary>
    /// <param name="count">数量</param>
    /// <returns>商品列表</returns>
    Task<ApiResponse<List<Product>>> GetPopularProductsAsync(int count = 10);

    /// <summary>
    /// 获取推荐商品
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">数量</param>
    /// <returns>商品列表</returns>
    Task<ApiResponse<List<Product>>> GetRecommendedProductsAsync(int? userId = null, int count = 10);

    /// <summary>
    /// 更新商品库存
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="quantity">库存数量</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<bool>> UpdateStockAsync(int productId, int quantity);

    /// <summary>
    /// 批量更新商品库存
    /// </summary>
    /// <param name="stockUpdates">库存更新列表</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<bool>> BatchUpdateStockAsync(Dictionary<int, int> stockUpdates);

    /// <summary>
    /// 增加商品浏览量
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<bool>> IncrementViewCountAsync(int productId);

    /// <summary>
    /// 获取商品统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetProductStatisticsAsync();
}

/// <summary>
/// 商品分类服务接口
/// </summary>
public interface ICategoryService
{
    /// <summary>
    /// 根据ID获取分类信息
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <returns>分类信息</returns>
    Task<ApiResponse<Category>> GetCategoryByIdAsync(int categoryId);

    /// <summary>
    /// 创建分类
    /// </summary>
    /// <param name="category">分类信息</param>
    /// <returns>创建结果</returns>
    Task<ApiResponse<Category>> CreateCategoryAsync(Category category);

    /// <summary>
    /// 更新分类信息
    /// </summary>
    /// <param name="category">分类信息</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<Category>> UpdateCategoryAsync(Category category);

    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <returns>删除结果</returns>
    Task<ApiResponse<bool>> DeleteCategoryAsync(int categoryId);

    /// <summary>
    /// 获取所有分类（树形结构）
    /// </summary>
    /// <returns>分类树</returns>
    Task<ApiResponse<List<Category>>> GetCategoryTreeAsync();

    /// <summary>
    /// 获取根分类列表
    /// </summary>
    /// <returns>根分类列表</returns>
    Task<ApiResponse<List<Category>>> GetRootCategoriesAsync();

    /// <summary>
    /// 获取子分类列表
    /// </summary>
    /// <param name="parentId">父分类ID</param>
    /// <returns>子分类列表</returns>
    Task<ApiResponse<List<Category>>> GetChildCategoriesAsync(int parentId);

    /// <summary>
    /// 移动分类
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <param name="newParentId">新父分类ID</param>
    /// <returns>移动结果</returns>
    Task<ApiResponse<bool>> MoveCategoryAsync(int categoryId, int? newParentId);

    /// <summary>
    /// 获取分类路径
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <returns>分类路径</returns>
    Task<ApiResponse<List<Category>>> GetCategoryPathAsync(int categoryId);
}
