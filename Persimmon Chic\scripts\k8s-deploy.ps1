# Persimmon Chic Kubernetes 部署脚本
param(
    [string]$Environment = "production",
    [string]$Action = "deploy",
    [string]$Service = "all",
    [switch]$DryRun,
    [switch]$Wait
)

Write-Host "=== Persimmon Chic Kubernetes 部署脚本 ===" -ForegroundColor Green
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host "操作: $Action" -ForegroundColor Yellow
Write-Host "服务: $Service" -ForegroundColor Yellow
Write-Host ""

# 检查kubectl是否可用
try {
    $kubectlVersion = & kubectl version --client --short 2>$null
    Write-Host "检测到 kubectl: $kubectlVersion" -ForegroundColor Green
}
catch {
    Write-Host "错误: 未找到 kubectl 命令，请先安装 Kubernetes CLI" -ForegroundColor Red
    exit 1
}

# 检查集群连接
try {
    $clusterInfo = & kubectl cluster-info --request-timeout=5s 2>$null
    Write-Host "Kubernetes 集群连接正常" -ForegroundColor Green
}
catch {
    Write-Host "错误: 无法连接到 Kubernetes 集群" -ForegroundColor Red
    exit 1
}

# 设置命名空间
$namespace = if ($Environment -eq "development") { "persimmon-chic-dev" } else { "persimmon-chic" }
Write-Host "使用命名空间: $namespace" -ForegroundColor Cyan
Write-Host ""

# 部署配置文件列表
$deploymentFiles = @{
    "namespace" = @{
        "Name" = "命名空间"
        "Files" = @("k8s/namespace.yaml")
        "Order" = 1
    }
    "secrets" = @{
        "Name" = "密钥配置"
        "Files" = @("k8s/secrets.yaml")
        "Order" = 2
    }
    "configmap" = @{
        "Name" = "配置映射"
        "Files" = @("k8s/configmap.yaml")
        "Order" = 3
    }
    "infrastructure" = @{
        "Name" = "基础设施"
        "Files" = @("k8s/infrastructure.yaml")
        "Order" = 4
    }
    "gateway" = @{
        "Name" = "API网关"
        "Files" = @("k8s/gateway.yaml")
        "Order" = 5
    }
    "user-service" = @{
        "Name" = "用户服务"
        "Files" = @("k8s/user-service.yaml")
        "Order" = 6
    }
    "business-services" = @{
        "Name" = "业务服务"
        "Files" = @(
            "k8s/risk-control-service.yaml",
            "k8s/pricing-service.yaml",
            "k8s/recommendation-service.yaml",
            "k8s/search-service.yaml",
            "k8s/customer-service-bot.yaml"
        )
        "Order" = 7
    }
}

function Deploy-KubernetesResource {
    param(
        [string]$ResourceKey,
        [hashtable]$ResourceConfig
    )
    
    $resourceName = $ResourceConfig.Name
    $files = $ResourceConfig.Files
    
    Write-Host "部署资源: $resourceName" -ForegroundColor Yellow
    
    $success = $true
    
    foreach ($file in $files) {
        if (-not (Test-Path $file)) {
            Write-Host "  警告: 配置文件不存在: $file" -ForegroundColor Yellow
            continue
        }
        
        Write-Host "  应用配置: $file" -ForegroundColor Cyan
        
        try {
            $kubectlArgs = @("apply", "-f", $file)
            
            if ($DryRun) {
                $kubectlArgs += "--dry-run=client"
                Write-Host "    (模拟运行)" -ForegroundColor Gray
            }
            
            $result = & kubectl @kubectlArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "    ✓ 应用成功" -ForegroundColor Green
            } else {
                Write-Host "    ✗ 应用失败" -ForegroundColor Red
                $success = $false
            }
        }
        catch {
            Write-Host "    ✗ 应用异常: $($_.Exception.Message)" -ForegroundColor Red
            $success = $false
        }
    }
    
    return $success
}

function Remove-KubernetesResource {
    param(
        [string]$ResourceKey,
        [hashtable]$ResourceConfig
    )
    
    $resourceName = $ResourceConfig.Name
    $files = $ResourceConfig.Files
    
    Write-Host "删除资源: $resourceName" -ForegroundColor Yellow
    
    $success = $true
    
    # 反向删除文件
    $reversedFiles = $files | Sort-Object -Descending
    
    foreach ($file in $reversedFiles) {
        if (-not (Test-Path $file)) {
            Write-Host "  跳过: 配置文件不存在: $file" -ForegroundColor Gray
            continue
        }
        
        Write-Host "  删除配置: $file" -ForegroundColor Cyan
        
        try {
            $kubectlArgs = @("delete", "-f", $file, "--ignore-not-found=true")
            
            if ($DryRun) {
                $kubectlArgs += "--dry-run=client"
                Write-Host "    (模拟运行)" -ForegroundColor Gray
            }
            
            $result = & kubectl @kubectlArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "    ✓ 删除成功" -ForegroundColor Green
            } else {
                Write-Host "    ✗ 删除失败" -ForegroundColor Red
                $success = $false
            }
        }
        catch {
            Write-Host "    ✗ 删除异常: $($_.Exception.Message)" -ForegroundColor Red
            $success = $false
        }
    }
    
    return $success
}

function Wait-ForDeployment {
    param(
        [string]$DeploymentName,
        [string]$Namespace,
        [int]$TimeoutSeconds = 300
    )
    
    Write-Host "等待部署完成: $DeploymentName" -ForegroundColor Cyan
    
    try {
        $kubectlArgs = @(
            "rollout", "status", "deployment/$DeploymentName",
            "-n", $Namespace,
            "--timeout=${TimeoutSeconds}s"
        )
        
        & kubectl @kubectlArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 部署完成: $DeploymentName" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ✗ 部署超时: $DeploymentName" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 等待部署异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主部署逻辑
$deployResults = @()

try {
    switch ($Action.ToLower()) {
        "deploy" {
            Write-Host "开始部署到 Kubernetes..." -ForegroundColor Green
            Write-Host ""
            
            if ($Service -eq "all") {
                # 按顺序部署所有资源
                $sortedResources = $deploymentFiles.GetEnumerator() | Sort-Object { $_.Value.Order }
                
                foreach ($resource in $sortedResources) {
                    $result = Deploy-KubernetesResource -ResourceKey $resource.Key -ResourceConfig $resource.Value
                    $deployResults += @{
                        "Resource" = $resource.Value.Name
                        "Success" = $result
                    }
                    Write-Host ""
                }
            } else {
                if ($deploymentFiles.ContainsKey($Service)) {
                    $result = Deploy-KubernetesResource -ResourceKey $Service -ResourceConfig $deploymentFiles[$Service]
                    $deployResults += @{
                        "Resource" = $deploymentFiles[$Service].Name
                        "Success" = $result
                    }
                } else {
                    Write-Host "错误: 未找到服务 '$Service'" -ForegroundColor Red
                    Write-Host "可用服务: $($deploymentFiles.Keys -join ', ')" -ForegroundColor Yellow
                    exit 1
                }
            }
            
            # 等待部署完成
            if ($Wait -and -not $DryRun) {
                Write-Host "等待所有部署完成..." -ForegroundColor Cyan
                Write-Host ""
                
                $deployments = @("gateway", "user-service")
                
                foreach ($deployment in $deployments) {
                    Wait-ForDeployment -DeploymentName $deployment -Namespace $namespace
                }
            }
        }
        
        "delete" {
            Write-Host "开始从 Kubernetes 删除..." -ForegroundColor Yellow
            Write-Host ""
            
            if ($Service -eq "all") {
                # 按逆序删除所有资源
                $sortedResources = $deploymentFiles.GetEnumerator() | Sort-Object { $_.Value.Order } -Descending
                
                foreach ($resource in $sortedResources) {
                    $result = Remove-KubernetesResource -ResourceKey $resource.Key -ResourceConfig $resource.Value
                    $deployResults += @{
                        "Resource" = $resource.Value.Name
                        "Success" = $result
                    }
                    Write-Host ""
                }
            } else {
                if ($deploymentFiles.ContainsKey($Service)) {
                    $result = Remove-KubernetesResource -ResourceKey $Service -ResourceConfig $deploymentFiles[$Service]
                    $deployResults += @{
                        "Resource" = $deploymentFiles[$Service].Name
                        "Success" = $result
                    }
                } else {
                    Write-Host "错误: 未找到服务 '$Service'" -ForegroundColor Red
                    exit 1
                }
            }
        }
        
        "status" {
            Write-Host "检查部署状态..." -ForegroundColor Cyan
            Write-Host ""
            
            # 检查命名空间
            & kubectl get namespace $namespace 2>$null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "命名空间状态:" -ForegroundColor Yellow
                & kubectl get all -n $namespace
                
                Write-Host ""
                Write-Host "Pod 详细状态:" -ForegroundColor Yellow
                & kubectl get pods -n $namespace -o wide
                
                Write-Host ""
                Write-Host "服务状态:" -ForegroundColor Yellow
                & kubectl get services -n $namespace
            } else {
                Write-Host "命名空间 '$namespace' 不存在" -ForegroundColor Red
            }
        }
        
        "logs" {
            Write-Host "查看服务日志..." -ForegroundColor Cyan
            
            if ($Service -and $Service -ne "all") {
                & kubectl logs -n $namespace -l "app.kubernetes.io/name=$Service" --tail=100 -f
            } else {
                Write-Host "请指定要查看日志的服务名称" -ForegroundColor Yellow
                Write-Host "可用服务: gateway, user-service, redis, sqlserver" -ForegroundColor Yellow
            }
        }
        
        default {
            Write-Host "错误: 未知操作 '$Action'" -ForegroundColor Red
            Write-Host ""
            Write-Host "可用操作:" -ForegroundColor Yellow
            Write-Host "  deploy  - 部署到集群" -ForegroundColor White
            Write-Host "  delete  - 从集群删除" -ForegroundColor White
            Write-Host "  status  - 查看部署状态" -ForegroundColor White
            Write-Host "  logs    - 查看服务日志" -ForegroundColor White
            exit 1
        }
    }
}
catch {
    Write-Host "执行过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 显示部署总结
if ($deployResults.Count -gt 0) {
    Write-Host ""
    Write-Host "=== 部署总结 ===" -ForegroundColor Green
    Write-Host ""
    
    $successCount = 0
    $totalCount = $deployResults.Count
    
    foreach ($result in $deployResults) {
        $status = if ($result.Success) { "✓" } else { "✗" }
        $color = if ($result.Success) { "Green" } else { "Red" }
        
        Write-Host "$status $($result.Resource)" -ForegroundColor $color
        
        if ($result.Success) {
            $successCount++
        }
    }
    
    Write-Host ""
    $overallStatus = if ($successCount -eq $totalCount) { "✓ 全部成功" } else { "⚠ 部分失败" }
    $overallColor = if ($successCount -eq $totalCount) { "Green" } else { "Yellow" }
    
    Write-Host "部署结果: $overallStatus ($successCount/$totalCount)" -ForegroundColor $overallColor
}

Write-Host ""
Write-Host "=== 使用提示 ===" -ForegroundColor Cyan
Write-Host "1. 查看部署状态: .\k8s-deploy.ps1 -Action status"
Write-Host "2. 查看服务日志: .\k8s-deploy.ps1 -Action logs -Service gateway"
Write-Host "3. 删除部署: .\k8s-deploy.ps1 -Action delete"
Write-Host "4. 模拟部署: .\k8s-deploy.ps1 -Action deploy -DryRun"
Write-Host ""
