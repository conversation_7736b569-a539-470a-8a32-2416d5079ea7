using System.ComponentModel.DataAnnotations;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.RiskControlService.Models;

/// <summary>
/// 用户行为事件
/// </summary>
public class UserBehaviorEvent
{
    public int Id { get; set; }
    
    [Required]
    public int UserId { get; set; }
    
    [Required]
    public string EventType { get; set; } = string.Empty;
    
    [Required]
    public string IpAddress { get; set; } = string.Empty;
    
    [Required]
    public string UserAgent { get; set; } = string.Empty;
    
    public string? DeviceFingerprint { get; set; }
    
    public string? Location { get; set; }
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 风险评估结果
/// </summary>
public class RiskAssessmentResult
{
    public int Id { get; set; }
    
    [Required]
    public int UserId { get; set; }
    
    [Required]
    public string EventType { get; set; } = string.Empty;
    
    [Range(0, 100)]
    public double RiskScore { get; set; }
    
    [Required]
    public RiskLevel RiskLevel { get; set; }
    
    public List<string> RiskFactors { get; set; } = new();
    
    public string? RecommendedAction { get; set; }
    
    public DateTime AssessmentTime { get; set; } = DateTime.UtcNow;
    
    public bool IsBlocked { get; set; }
    
    public string? BlockReason { get; set; }
}

/// <summary>
/// 设备指纹信息
/// </summary>
public class DeviceFingerprint
{
    public int Id { get; set; }
    
    [Required]
    public string FingerprintHash { get; set; } = string.Empty;
    
    public int UserId { get; set; }
    
    public string? DeviceType { get; set; }
    
    public string? OperatingSystem { get; set; }
    
    public string? Browser { get; set; }
    
    public string? ScreenResolution { get; set; }
    
    public string? TimeZone { get; set; }
    
    public string? Language { get; set; }
    
    public DateTime FirstSeen { get; set; } = DateTime.UtcNow;
    
    public DateTime LastSeen { get; set; } = DateTime.UtcNow;
    
    public int LoginCount { get; set; }
    
    public bool IsTrusted { get; set; }
    
    public bool IsBlacklisted { get; set; }
}

/// <summary>
/// 异常登录记录
/// </summary>
public class AnomalousLoginRecord
{
    public int Id { get; set; }
    
    [Required]
    public int UserId { get; set; }
    
    [Required]
    public string IpAddress { get; set; } = string.Empty;
    
    public string? Location { get; set; }
    
    public string? DeviceFingerprint { get; set; }
    
    [Required]
    public AnomalyType AnomalyType { get; set; }
    
    public double AnomalyScore { get; set; }
    
    public string? Description { get; set; }
    
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsResolved { get; set; }
    
    public string? Resolution { get; set; }
    
    public DateTime? ResolvedAt { get; set; }
}

/// <summary>
/// 风险规则配置
/// </summary>
public class RiskRule
{
    public int Id { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    public string EventType { get; set; } = string.Empty;
    
    [Required]
    public string Condition { get; set; } = string.Empty;
    
    [Range(0, 100)]
    public double RiskWeight { get; set; }
    
    public bool IsEnabled { get; set; } = true;
    
    public int Priority { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
}

/// <summary>
/// 实时风险监控指标
/// </summary>
public class RiskMetrics
{
    public string MetricName { get; set; } = string.Empty;
    
    public double Value { get; set; }
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, string> Tags { get; set; } = new();
}

/// <summary>
/// 风险等级枚举
/// </summary>
public enum RiskLevel
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// 异常类型枚举
/// </summary>
public enum AnomalyType
{
    UnusualLocation = 1,
    UnusualDevice = 2,
    UnusualTime = 3,
    HighFrequency = 4,
    SuspiciousIP = 5,
    BehaviorPattern = 6
}

/// <summary>
/// 风控动作类型
/// </summary>
public enum RiskAction
{
    Allow = 1,
    Challenge = 2,
    Block = 3,
    Monitor = 4
}

/// <summary>
/// 风控决策请求
/// </summary>
public class RiskDecisionRequest
{
    [Required]
    public int UserId { get; set; }
    
    [Required]
    public string EventType { get; set; } = string.Empty;
    
    [Required]
    public string IpAddress { get; set; } = string.Empty;
    
    [Required]
    public string UserAgent { get; set; } = string.Empty;
    
    public string? DeviceFingerprint { get; set; }
    
    public string? Location { get; set; }
    
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 风控决策响应
/// </summary>
public class RiskDecisionResponse
{
    public bool IsAllowed { get; set; }
    
    public RiskAction Action { get; set; }
    
    public double RiskScore { get; set; }
    
    public RiskLevel RiskLevel { get; set; }
    
    public List<string> RiskFactors { get; set; } = new();
    
    public string? Message { get; set; }
    
    public string? ChallengeType { get; set; }
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 用户风险档案
/// </summary>
public class UserRiskProfile
{
    public int UserId { get; set; }
    
    public double BaseRiskScore { get; set; }
    
    public RiskLevel CurrentRiskLevel { get; set; }
    
    public List<string> TrustedDevices { get; set; } = new();
    
    public List<string> TrustedIPs { get; set; } = new();
    
    public List<string> TrustedLocations { get; set; } = new();
    
    public DateTime LastLoginTime { get; set; }
    
    public string? LastLoginIP { get; set; }
    
    public string? LastLoginLocation { get; set; }
    
    public int FailedLoginAttempts { get; set; }
    
    public DateTime? LastFailedLoginTime { get; set; }
    
    public bool IsLocked { get; set; }
    
    public DateTime? LockedUntil { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
