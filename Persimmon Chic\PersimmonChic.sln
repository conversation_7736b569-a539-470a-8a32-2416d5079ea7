Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33829.357
MinimumVisualStudioVersion = 10.0.40219.1

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Client", "Client", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Gateway", "Gateway", "{C3D4E5F6-G7H8-9012-CDEF-345678901234}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{D4E5F6G7-H8I9-0123-DEF0-456789012345}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "BusinessServices", "BusinessServices", "{E5F6G7H8-I9J0-1234-EF01-************}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PlatformServices", "PlatformServices", "{F6G7H8I9-J0K1-2345-F012-678901234567}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{G7H8I9J0-K1L2-3456-0123-************}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{H8I9J0K1-L2M3-4567-1234-************}"
EndProject

# Client Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.Client", "src\Client\PersimmonChic.Client\PersimmonChic.Client.csproj", "{11111111-1111-1111-1111-111111111111}"
EndProject

# Gateway Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.Gateway", "src\Gateway\PersimmonChic.Gateway.csproj", "{22222222-2222-2222-2222-222222222222}"
EndProject

# Business Service Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.UserService", "src\Services\UserService\PersimmonChic.UserService.csproj", "{33333333-3333-3333-3333-333333333333}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.RiskControlService", "src\Services\RiskControlService\PersimmonChic.RiskControlService.csproj", "{44444444-4444-4444-4444-444444444444}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.PricingService", "src\Services\PricingService\PersimmonChic.PricingService.csproj", "{55555555-5555-5555-5555-555555555555}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.RecommendationService", "src\Services\RecommendationService\PersimmonChic.RecommendationService.csproj", "{66666666-6666-6666-6666-666666666666}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.SearchService", "src\Services\SearchService\PersimmonChic.SearchService.csproj", "{77777777-7777-7777-7777-777777777777}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.CustomerServiceBot", "src\Services\CustomerServiceBot\PersimmonChic.CustomerServiceBot.csproj", "{*************-8888-8888-************}"
EndProject



# Infrastructure Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.Infrastructure.DataAccess", "src\Infrastructure\DataAccess\PersimmonChic.Infrastructure.DataAccess.csproj", "{*************-9999-9999-************}"
EndProject

# Shared Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.Shared.Contracts", "src\Shared\Contracts\PersimmonChic.Shared.Contracts.csproj", "{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.Shared.Models", "src\Shared\Models\PersimmonChic.Shared.Models.csproj", "{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PersimmonChic.Shared.Common", "src\Shared\Common\PersimmonChic.Shared.Common.csproj", "{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.Build.0 = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|Any CPU.Build.0 = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|Any CPU.Build.0 = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|Any CPU.Build.0 = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|Any CPU.Build.0 = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|Any CPU.Build.0 = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-8888-8888-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-8888-8888-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-8888-8888-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-8888-8888-************}.Release|Any CPU.Build.0 = Release|Any CPU


		{*************-9999-9999-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-9999-9999-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-9999-9999-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-9999-9999-************}.Release|Any CPU.Build.0 = Release|Any CPU

		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|Any CPU.Build.0 = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{C3D4E5F6-G7H8-9012-CDEF-345678901234} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{D4E5F6G7-H8I9-0123-DEF0-456789012345} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{E5F6G7H8-I9J0-1234-EF01-************} = {D4E5F6G7-H8I9-0123-DEF0-456789012345}
		{F6G7H8I9-J0K1-2345-F012-678901234567} = {D4E5F6G7-H8I9-0123-DEF0-456789012345}
		{G7H8I9J0-K1L2-3456-0123-************} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{H8I9J0K1-L2M3-4567-1234-************} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{11111111-1111-1111-1111-111111111111} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{22222222-2222-2222-2222-222222222222} = {C3D4E5F6-G7H8-9012-CDEF-345678901234}
		{33333333-3333-3333-3333-333333333333} = {E5F6G7H8-I9J0-1234-EF01-************}
		{44444444-4444-4444-4444-444444444444} = {E5F6G7H8-I9J0-1234-EF01-************}
		{55555555-5555-5555-5555-555555555555} = {E5F6G7H8-I9J0-1234-EF01-************}
		{66666666-6666-6666-6666-666666666666} = {E5F6G7H8-I9J0-1234-EF01-************}
		{77777777-7777-7777-7777-777777777777} = {E5F6G7H8-I9J0-1234-EF01-************}
		{*************-8888-8888-************} = {E5F6G7H8-I9J0-1234-EF01-************}


		{*************-9999-9999-************} = {G7H8I9J0-K1L2-3456-0123-************}

		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB} = {H8I9J0K1-L2M3-4567-1234-************}
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC} = {H8I9J0K1-L2M3-4567-1234-************}
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD} = {H8I9J0K1-L2M3-4567-1234-************}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9012-123456789012}
	EndGlobalSection
EndGlobal
