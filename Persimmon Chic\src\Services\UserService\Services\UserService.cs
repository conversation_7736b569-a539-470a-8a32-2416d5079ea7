using Microsoft.IdentityModel.Tokens;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.Shared.Contracts;
using PersimmonChic.Shared.Models;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using BCrypt.Net;

namespace PersimmonChic.UserService.Services;

/// <summary>
/// 用户服务实现
/// </summary>
public class UserService : IUserService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UserService> _logger;
    private readonly IConfiguration _configuration;

    public UserService(IUnitOfWork unitOfWork, ILogger<UserService> logger, IConfiguration configuration)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request)
    {
        try
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == request.Username);
            if (user == null)
            {
                return ApiResponse<LoginResponse>.ErrorResult("用户名或密码错误");
            }

            if (!user.IsActive)
            {
                return ApiResponse<LoginResponse>.ErrorResult("账户已被禁用");
            }

            if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                return ApiResponse<LoginResponse>.ErrorResult("用户名或密码错误");
            }

            var token = GenerateJwtToken(user);
            var response = new LoginResponse
            {
                Success = true,
                Token = token,
                User = user,
                Message = "登录成功",
                ExpiresAt = DateTime.UtcNow.AddHours(24)
            };

            _logger.LogInformation("User {Username} logged in successfully", user.Username);
            return ApiResponse<LoginResponse>.SuccessResult(response, "登录成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user {Username}", request.Username);
            return ApiResponse<LoginResponse>.ErrorResult("登录失败，请稍后重试");
        }
    }

    public async Task<ApiResponse<User>> RegisterAsync(RegisterRequest request)
    {
        try
        {
            // 检查用户名是否已存在
            var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == request.Username);
            if (existingUser != null)
            {
                return ApiResponse<User>.ErrorResult("用户名已存在");
            }

            // 检查邮箱是否已存在
            var existingEmail = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == request.Email);
            if (existingEmail != null)
            {
                return ApiResponse<User>.ErrorResult("邮箱已被注册");
            }

            var user = new User
            {
                Username = request.Username,
                Email = request.Email,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                FirstName = request.FirstName,
                LastName = request.LastName,
                PhoneNumber = request.PhoneNumber,
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Role = UserRole.Customer
            };

            await _unitOfWork.Users.AddAsync(user);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("New user {Username} registered successfully", user.Username);
            return ApiResponse<User>.SuccessResult(user, "注册成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration for user {Username}", request.Username);
            return ApiResponse<User>.ErrorResult("注册失败，请稍后重试");
        }
    }

    public async Task<ApiResponse<User>> GetUserByIdAsync(int userId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<User>.ErrorResult("用户不存在");
            }

            return ApiResponse<User>.SuccessResult(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by ID {UserId}", userId);
            return ApiResponse<User>.ErrorResult("获取用户信息失败");
        }
    }

    public async Task<ApiResponse<User>> GetUserByUsernameAsync(string username)
    {
        try
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == username);
            if (user == null)
            {
                return ApiResponse<User>.ErrorResult("用户不存在");
            }

            return ApiResponse<User>.SuccessResult(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by username {Username}", username);
            return ApiResponse<User>.ErrorResult("获取用户信息失败");
        }
    }

    public async Task<ApiResponse<User>> UpdateUserAsync(User user)
    {
        try
        {
            var existingUser = await _unitOfWork.Users.GetByIdAsync(user.Id);
            if (existingUser == null)
            {
                return ApiResponse<User>.ErrorResult("用户不存在");
            }

            // 更新用户信息（不包括密码和敏感信息）
            existingUser.FirstName = user.FirstName;
            existingUser.LastName = user.LastName;
            existingUser.Email = user.Email;
            existingUser.PhoneNumber = user.PhoneNumber;
            existingUser.Avatar = user.Avatar;
            existingUser.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(existingUser);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("User {UserId} updated successfully", user.Id);
            return ApiResponse<User>.SuccessResult(existingUser, "用户信息更新成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", user.Id);
            return ApiResponse<User>.ErrorResult("更新用户信息失败");
        }
    }

    public async Task<ApiResponse<bool>> DeleteUserAsync(int userId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<bool>.ErrorResult("用户不存在");
            }

            // 软删除：设置为非活跃状态
            user.IsActive = false;
            user.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("User {UserId} deleted successfully", userId);
            return ApiResponse<bool>.SuccessResult(true, "用户删除成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", userId);
            return ApiResponse<bool>.ErrorResult("删除用户失败");
        }
    }

    public async Task<ApiResponse<PagedResponse<User>>> GetUsersAsync(PagedRequest request)
    {
        try
        {
            var result = await _unitOfWork.Users.GetPagedAsync<DateTime>(
                request.Page,
                request.PageSize,
                u => string.IsNullOrEmpty(request.SearchKeyword) || 
                     u.Username.Contains(request.SearchKeyword) || 
                     u.Email.Contains(request.SearchKeyword) ||
                     u.FirstName.Contains(request.SearchKeyword) ||
                     u.LastName.Contains(request.SearchKeyword),
                u => u.CreatedAt,
                !request.SortDescending);

            return ApiResponse<PagedResponse<User>>.SuccessResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users list");
            return ApiResponse<PagedResponse<User>>.ErrorResult("获取用户列表失败");
        }
    }

    public async Task<ApiResponse<bool>> ChangePasswordAsync(int userId, string oldPassword, string newPassword)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<bool>.ErrorResult("用户不存在");
            }

            if (!BCrypt.Net.BCrypt.Verify(oldPassword, user.PasswordHash))
            {
                return ApiResponse<bool>.ErrorResult("原密码错误");
            }

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("Password changed for user {UserId}", userId);
            return ApiResponse<bool>.SuccessResult(true, "密码修改成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user {UserId}", userId);
            return ApiResponse<bool>.ErrorResult("密码修改失败");
        }
    }

    public async Task<ApiResponse<bool>> ResetPasswordAsync(string email)
    {
        try
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == email);
            if (user == null)
            {
                // 为了安全，即使用户不存在也返回成功
                return ApiResponse<bool>.SuccessResult(true, "如果邮箱存在，重置密码邮件已发送");
            }

            // 生成临时密码
            var tempPassword = GenerateRandomPassword();
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(tempPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.CommitAsync();

            // TODO: 发送邮件（这里只是模拟）
            _logger.LogInformation("Password reset for user {Email}, temp password: {TempPassword}", 
                email, tempPassword);

            return ApiResponse<bool>.SuccessResult(true, "重置密码邮件已发送");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password for email {Email}", email);
            return ApiResponse<bool>.ErrorResult("密码重置失败");
        }
    }

    public async Task<ApiResponse<User>> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(GetJwtSecret());

            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            var jwtToken = (JwtSecurityToken)validatedToken;
            var userId = int.Parse(jwtToken.Claims.First(x => x.Type == "id").Value);

            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null || !user.IsActive)
            {
                return ApiResponse<User>.ErrorResult("用户不存在或已被禁用");
            }

            return ApiResponse<User>.SuccessResult(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return ApiResponse<User>.ErrorResult("令牌验证失败");
        }
    }

    public async Task<ApiResponse<LoginResponse>> RefreshTokenAsync(string token)
    {
        try
        {
            var userResponse = await ValidateTokenAsync(token);
            if (!userResponse.Success || userResponse.Data == null)
            {
                return ApiResponse<LoginResponse>.ErrorResult("令牌无效");
            }

            var newToken = GenerateJwtToken(userResponse.Data);
            var response = new LoginResponse
            {
                Success = true,
                Token = newToken,
                User = userResponse.Data,
                Message = "令牌刷新成功",
                ExpiresAt = DateTime.UtcNow.AddHours(24)
            };

            return ApiResponse<LoginResponse>.SuccessResult(response, "令牌刷新成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing token");
            return ApiResponse<LoginResponse>.ErrorResult("令牌刷新失败");
        }
    }

    public Task<ApiResponse<bool>> LogoutAsync(string token)
    {
        // 简单实现：客户端删除令牌即可
        // 在实际应用中，可以维护一个黑名单
        return Task.FromResult(ApiResponse<bool>.SuccessResult(true, "登出成功"));
    }

    public async Task<ApiResponse<bool>> ActivateUserAsync(int userId, string activationCode)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<bool>.ErrorResult("用户不存在");
            }

            // 简单的激活码验证（实际应用中应该更复杂）
            if (activationCode != $"ACTIVATE_{userId}")
            {
                return ApiResponse<bool>.ErrorResult("激活码无效");
            }

            user.IsActive = true;
            user.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("User {UserId} activated successfully", userId);
            return ApiResponse<bool>.SuccessResult(true, "账户激活成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating user {UserId}", userId);
            return ApiResponse<bool>.ErrorResult("账户激活失败");
        }
    }

    public async Task<ApiResponse<bool>> DeactivateUserAsync(int userId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<bool>.ErrorResult("用户不存在");
            }

            user.IsActive = false;
            user.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("User {UserId} deactivated successfully", userId);
            return ApiResponse<bool>.SuccessResult(true, "账户已禁用");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating user {UserId}", userId);
            return ApiResponse<bool>.ErrorResult("账户禁用失败");
        }
    }

    public async Task<ApiResponse<Dictionary<string, object>>> GetUserStatisticsAsync()
    {
        try
        {
            var totalUsers = await _unitOfWork.Users.CountAsync();
            var activeUsers = await _unitOfWork.Users.CountAsync(u => u.IsActive);
            var newUsersThisMonth = await _unitOfWork.Users.CountAsync(u => u.CreatedAt >= DateTime.UtcNow.AddDays(-30));

            var statistics = new Dictionary<string, object>
            {
                ["TotalUsers"] = totalUsers,
                ["ActiveUsers"] = activeUsers,
                ["InactiveUsers"] = totalUsers - activeUsers,
                ["NewUsersThisMonth"] = newUsersThisMonth,
                ["UserGrowthRate"] = totalUsers > 0 ? (double)newUsersThisMonth / totalUsers * 100 : 0
            };

            return ApiResponse<Dictionary<string, object>>.SuccessResult(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user statistics");
            return ApiResponse<Dictionary<string, object>>.ErrorResult("获取用户统计失败");
        }
    }

    private string GenerateJwtToken(User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(GetJwtSecret());
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim("id", user.Id.ToString()),
                new Claim("username", user.Username),
                new Claim("email", user.Email),
                new Claim("role", user.Role.ToString())
            }),
            Expires = DateTime.UtcNow.AddHours(24),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    private string GetJwtSecret()
    {
        return _configuration["JwtSettings:SecretKey"] ?? "PersimmonChic_Super_Secret_Key_2024_Demo_Application";
    }

    private static string GenerateRandomPassword(int length = 12)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }
}
