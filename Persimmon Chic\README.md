# Persimmon Chic 🍂

一个基于微服务架构的现代电商平台演示应用，展示 AntdUI 组件库在实际业务场景中的应用。

## 项目概述

Persimmon Chic 是一个完整的微服务架构演示项目，包含：
- 🏗️ **微服务架构**: API Gateway + 多个业务服务
- 🎨 **现代UI**: 基于 AntdUI 的 WinForms 客户端
- 🔐 **安全认证**: JWT 令牌认证机制
- 📊 **服务治理**: 服务发现、健康检查、负载均衡
- 🛡️ **风险控制**: 实时行为分析和机器学习风险评估 🆕
- 💰 **动态定价**: 智能定价引擎和多维度价格策略 🆕
- 🚀 **易于部署**: 一键启动演示脚本

## 技术栈

- **前端**: AntdUI + WinForms (.NET 9.0)
- **后端**: ASP.NET Core Web API (.NET 9.0)
- **认证**: JWT Token
- **数据**: 内存数据库 (演示用)
- **文档**: Swagger/OpenAPI
- **工具**: PowerShell 脚本

## 快速开始

### 环境要求

- .NET 9.0 SDK
- Windows 10/11
- PowerShell 5.0+
- Visual Studio 2022 或 VS Code (可选)

### 一键启动

1. 克隆项目到本地
2. 打开 PowerShell，导航到项目根目录
3. 运行启动脚本：

```powershell
# 启动基础版微服务 (API Gateway + User Service)
.\scripts\start-demo.ps1

# 启动增强版微服务 (包含风控和定价服务) 🆕
.\scripts\start-enhanced.ps1
```

脚本会自动：
- ✅ 检查环境依赖
- 🔨 构建所有项目
- 🚀 启动微服务
- 🌐 打开浏览器访问 API 文档

### 增强版功能 🆕

增强版包含以下新增服务：

#### 🛡️ 风险控制服务 (端口 5004)
- **实时行为分析**: 监控用户行为模式
- **机器学习风险评估**: 基于 ML.NET 的智能风险评分
- **设备指纹识别**: 设备唯一性识别和验证
- **异常登录检测**: 自动识别可疑登录行为
- **实时告警**: SignalR 实时推送风险事件

#### 💰 动态定价服务 (端口 5005)
- **智能定价引擎**: 基于需求、库存、时间的动态调整
- **多维度定价**: 渠道、用户等级、地域等差异化定价
- **高性能缓存**: Redis 分布式缓存优化
- **价格变更通知**: 实时价格变更通知系统
- **批量价格管理**: 支持大批量价格更新

### 手动启动

如果需要手动启动各个服务：

```bash
# 启动 API Gateway (端口 5000)
dotnet run --project src/Gateway/PersimmonChic.Gateway.csproj

# 启动用户服务 (端口 5001)
dotnet run --project src/Services/UserService/PersimmonChic.UserService.csproj

# 启动客户端应用
dotnet run --project src/Client/PersimmonChic.Client/PersimmonChic.Client.csproj
```

## 项目结构

```
Persimmon Chic/
├── src/                          # 源代码目录
│   ├── Client/                   # 客户端应用
│   │   └── PersimmonChic.Client/ # AntdUI WinForms 应用
│   ├── Gateway/                  # API 网关
│   │   └── PersimmonChic.Gateway/
│   ├── Services/                 # 微服务
│   │   ├── UserService/          # 用户服务
│   │   ├── ProductService/       # 商品服务 [规划中]
│   │   └── OrderService/         # 订单服务 [规划中]
│   ├── Infrastructure/           # 基础设施
│   │   ├── DataAccess/           # 数据访问层
│   │   └── ServiceDiscovery/     # 服务发现 [规划中]
│   └── Shared/                   # 共享组件
│       ├── Models/               # 数据模型
│       ├── Contracts/            # 服务契约
│       └── Common/               # 通用工具
├── docs/                         # 文档目录
│   ├── Architecture.md           # 架构设计文档
│   └── API.md                    # API 文档
├── scripts/                      # 脚本目录
│   └── start-demo.ps1            # 演示启动脚本
└── README.md                     # 项目说明
```

## 核心功能

### 🔐 用户认证
- 用户注册和登录
- JWT 令牌认证
- 角色权限管理
- 密码安全加密

### 👥 用户管理
- 用户信息管理
- 用户列表查询
- 用户状态管理
- 权限分配

### 🛡️ API 网关
- 统一入口管理
- 请求路由转发
- 认证授权检查
- 限流和熔断
- 日志记录

### 📊 服务监控
- 健康状态检查
- 服务发现机制
- 性能指标监控
- 错误日志记录

## API 接口

### 认证接口
- `POST /api/users/login` - 用户登录
- `POST /api/users/register` - 用户注册

### 用户管理
- `GET /api/users/{id}` - 获取用户信息
- `POST /api/users/list` - 获取用户列表
- `PUT /api/users` - 更新用户信息

### 系统接口
- `GET /health` - 健康检查
- `GET /api/gateway/health` - 服务状态
- `GET /services` - 服务列表

## 测试账户

系统预置了以下测试账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | 123456 | 管理员 | 系统管理员账户 |
| user1 | 123456 | 用户 | 普通用户账户 |
| user2 | 123456 | 用户 | 普通用户账户 |

## 开发指南

### 添加新服务

1. 在 `src/Services/` 下创建新的服务项目
2. 实现服务接口和业务逻辑
3. 在 API Gateway 中添加路由配置
4. 更新服务发现配置

### 扩展客户端功能

1. 在 `src/Client/` 中添加新的窗体或控件
2. 使用 AntdUI 组件构建界面
3. 通过 ApiService 调用后端接口
4. 处理数据绑定和用户交互

### 数据模型扩展

1. 在 `src/Shared/Models/` 中定义新的数据模型
2. 在相关服务中实现业务逻辑
3. 更新 API 契约和文档

## 部署说明

### 开发环境
- 使用 `start-demo.ps1` 脚本一键启动
- 所有服务运行在本地不同端口
- 使用内存数据库，重启后数据重置

### 生产环境 [规划中]
- Docker 容器化部署
- Kubernetes 集群编排
- 外部数据库 (MySQL/PostgreSQL)
- Redis 分布式缓存
- Nginx 负载均衡

## 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- [AntdUI](https://github.com/AntdUI/AntdUI) - 优秀的 WinForms UI 组件库
- [ASP.NET Core](https://docs.microsoft.com/aspnet/core/) - 高性能 Web 框架
- [.NET](https://dotnet.microsoft.com/) - 跨平台开发框架

## 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 Email: [<EMAIL>]
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/persimmon-chic/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-username/persimmon-chic/discussions)

---

**Persimmon Chic** - 让微服务架构变得简单优雅 🍂
