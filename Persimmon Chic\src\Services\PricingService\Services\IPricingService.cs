using PersimmonChic.PricingService.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.PricingService.Services;

/// <summary>
/// 价格服务接口
/// </summary>
public interface IPricingService
{
    /// <summary>
    /// 计算商品价格
    /// </summary>
    /// <param name="request">价格计算请求</param>
    /// <returns>价格计算响应</returns>
    Task<ApiResponse<PriceCalculationResponse>> CalculatePriceAsync(PriceCalculationRequest request);
    
    /// <summary>
    /// 获取商品基础价格
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="currency">货币代码</param>
    /// <returns>商品价格</returns>
    Task<ApiResponse<ProductPrice?>> GetProductPriceAsync(int productId, string currency = "CNY");
    
    /// <summary>
    /// 更新商品价格
    /// </summary>
    /// <param name="productPrice">商品价格</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdateProductPriceAsync(ProductPrice productPrice);
    
    /// <summary>
    /// 批量更新商品价格
    /// </summary>
    /// <param name="request">批量更新请求</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> BulkUpdatePricesAsync(BulkPriceUpdateRequest request);
    
    /// <summary>
    /// 获取渠道价格
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="channelCode">渠道代码</param>
    /// <returns>渠道价格</returns>
    Task<ApiResponse<ChannelPrice?>> GetChannelPriceAsync(int productId, string channelCode);
    
    /// <summary>
    /// 设置渠道价格
    /// </summary>
    /// <param name="channelPrice">渠道价格</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> SetChannelPriceAsync(ChannelPrice channelPrice);
    
    /// <summary>
    /// 获取用户等级价格
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="userTier">用户等级</param>
    /// <returns>用户等级价格</returns>
    Task<ApiResponse<UserTierPrice?>> GetUserTierPriceAsync(int productId, UserTier userTier);
    
    /// <summary>
    /// 设置用户等级价格
    /// </summary>
    /// <param name="userTierPrice">用户等级价格</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> SetUserTierPriceAsync(UserTierPrice userTierPrice);
    
    /// <summary>
    /// 获取地域价格
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="regionCode">地域代码</param>
    /// <returns>地域价格</returns>
    Task<ApiResponse<RegionalPrice?>> GetRegionalPriceAsync(int productId, string regionCode);
    
    /// <summary>
    /// 设置地域价格
    /// </summary>
    /// <param name="regionalPrice">地域价格</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> SetRegionalPriceAsync(RegionalPrice regionalPrice);
    
    /// <summary>
    /// 获取价格历史
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>价格历史列表</returns>
    Task<ApiResponse<List<PriceHistory>>> GetPriceHistoryAsync(int productId, DateTime? startDate = null, DateTime? endDate = null);
}

/// <summary>
/// 动态价格服务接口
/// </summary>
public interface IDynamicPricingService
{
    /// <summary>
    /// 应用动态价格规则
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="basePrice">基础价格</param>
    /// <param name="context">上下文信息</param>
    /// <returns>调整后的价格</returns>
    Task<ApiResponse<decimal>> ApplyDynamicPricingAsync(int productId, decimal basePrice, Dictionary<string, object> context);
    
    /// <summary>
    /// 获取动态价格规则
    /// </summary>
    /// <param name="productId">商品ID（可选）</param>
    /// <returns>动态价格规则列表</returns>
    Task<ApiResponse<List<DynamicPricingRule>>> GetDynamicPricingRulesAsync(int? productId = null);
    
    /// <summary>
    /// 创建动态价格规则
    /// </summary>
    /// <param name="rule">动态价格规则</param>
    /// <returns>创建的规则</returns>
    Task<ApiResponse<DynamicPricingRule>> CreateDynamicPricingRuleAsync(DynamicPricingRule rule);
    
    /// <summary>
    /// 更新动态价格规则
    /// </summary>
    /// <param name="rule">动态价格规则</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdateDynamicPricingRuleAsync(DynamicPricingRule rule);
    
    /// <summary>
    /// 删除动态价格规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> DeleteDynamicPricingRuleAsync(int ruleId);
}

/// <summary>
/// 价格策略服务接口
/// </summary>
public interface IPricingStrategyService
{
    /// <summary>
    /// 获取价格策略
    /// </summary>
    /// <param name="strategyId">策略ID</param>
    /// <returns>价格策略</returns>
    Task<ApiResponse<PricingStrategy?>> GetPricingStrategyAsync(int strategyId);
    
    /// <summary>
    /// 获取所有价格策略
    /// </summary>
    /// <returns>价格策略列表</returns>
    Task<ApiResponse<List<PricingStrategy>>> GetAllPricingStrategiesAsync();
    
    /// <summary>
    /// 创建价格策略
    /// </summary>
    /// <param name="strategy">价格策略</param>
    /// <returns>创建的策略</returns>
    Task<ApiResponse<PricingStrategy>> CreatePricingStrategyAsync(PricingStrategy strategy);
    
    /// <summary>
    /// 更新价格策略
    /// </summary>
    /// <param name="strategy">价格策略</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdatePricingStrategyAsync(PricingStrategy strategy);
    
    /// <summary>
    /// 删除价格策略
    /// </summary>
    /// <param name="strategyId">策略ID</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> DeletePricingStrategyAsync(int strategyId);
    
    /// <summary>
    /// 应用价格策略
    /// </summary>
    /// <param name="strategyId">策略ID</param>
    /// <param name="productIds">商品ID列表</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> ApplyPricingStrategyAsync(int strategyId, List<int> productIds);
}

/// <summary>
/// 价格缓存服务接口
/// </summary>
public interface IPriceCacheService
{
    /// <summary>
    /// 获取缓存的价格
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>缓存的价格</returns>
    Task<decimal?> GetCachedPriceAsync(string cacheKey);
    
    /// <summary>
    /// 设置价格缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="price">价格</param>
    /// <param name="expiration">过期时间</param>
    /// <returns>操作结果</returns>
    Task<bool> SetPriceCacheAsync(string cacheKey, decimal price, TimeSpan? expiration = null);
    
    /// <summary>
    /// 删除价格缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>操作结果</returns>
    Task<bool> RemovePriceCacheAsync(string cacheKey);
    
    /// <summary>
    /// 清除商品相关的所有价格缓存
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <returns>操作结果</returns>
    Task<bool> ClearProductPriceCacheAsync(int productId);
    
    /// <summary>
    /// 生成价格缓存键
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="context">上下文信息</param>
    /// <returns>缓存键</returns>
    string GeneratePriceCacheKey(int productId, Dictionary<string, object> context);
}

/// <summary>
/// 价格通知服务接口
/// </summary>
public interface IPriceNotificationService
{
    /// <summary>
    /// 发送价格变更通知
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="oldPrice">旧价格</param>
    /// <param name="newPrice">新价格</param>
    /// <param name="reason">变更原因</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> SendPriceChangeNotificationAsync(int productId, decimal oldPrice, decimal newPrice, string reason);
    
    /// <summary>
    /// 订阅价格变更通知
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="subscriberId">订阅者ID</param>
    /// <param name="notificationMethod">通知方式</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> SubscribePriceChangeNotificationAsync(int productId, string subscriberId, string notificationMethod);
    
    /// <summary>
    /// 取消订阅价格变更通知
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="subscriberId">订阅者ID</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UnsubscribePriceChangeNotificationAsync(int productId, string subscriberId);
}
