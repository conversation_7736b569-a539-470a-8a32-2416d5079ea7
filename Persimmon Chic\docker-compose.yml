version: '3.8'

services:
  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: persimmon-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3
    networks:
      - persimmon-network

  # SQL Server数据库
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: persimmon-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=PersimmonChic123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P PersimmonChic123! -Q 'SELECT 1'"]
      interval: 30s
      timeout: 3s
      retries: 3
    networks:
      - persimmon-network

  # API Gateway
  gateway:
    build:
      context: .
      dockerfile: src/Gateway/Dockerfile
    container_name: persimmon-gateway
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - Services__UserService__BaseUrl=http://user-service:80
      - Services__RiskControlService__BaseUrl=http://risk-control-service:80
      - Services__PricingService__BaseUrl=http://pricing-service:80
      - Services__RecommendationService__BaseUrl=http://recommendation-service:80
      - Services__SearchService__BaseUrl=http://search-service:80
      - Services__CustomerServiceBot__BaseUrl=http://customer-service-bot:80
    depends_on:
      redis:
        condition: service_healthy
      user-service:
        condition: service_healthy
    networks:
      - persimmon-network
    restart: unless-stopped

  # 用户服务
  user-service:
    build:
      context: .
      dockerfile: src/Services/UserService/Dockerfile
    container_name: persimmon-user-service
    ports:
      - "5001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=PersimmonChic_Users;User Id=sa;Password=PersimmonChic123!;TrustServerCertificate=true
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=PersimmonChic_Super_Secret_Key_2024_Demo_Application
      - JwtSettings__Issuer=PersimmonChic
      - JwtSettings__Audience=PersimmonChic-Users
    depends_on:
      redis:
        condition: service_healthy
      sqlserver:
        condition: service_healthy
    networks:
      - persimmon-network
    restart: unless-stopped

  # 风险控制服务
  risk-control-service:
    build:
      context: .
      dockerfile: src/Services/RiskControlService/Dockerfile
    container_name: persimmon-risk-control-service
    ports:
      - "5004:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=PersimmonChic_RiskControl;User Id=sa;Password=PersimmonChic123!;TrustServerCertificate=true
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=PersimmonChic_Super_Secret_Key_2024_Demo_Application
    depends_on:
      redis:
        condition: service_healthy
      sqlserver:
        condition: service_healthy
    networks:
      - persimmon-network
    restart: unless-stopped

  # 动态定价服务
  pricing-service:
    build:
      context: .
      dockerfile: src/Services/PricingService/Dockerfile
    container_name: persimmon-pricing-service
    ports:
      - "5005:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=PersimmonChic_Pricing;User Id=sa;Password=PersimmonChic123!;TrustServerCertificate=true
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=PersimmonChic_Super_Secret_Key_2024_Demo_Application
    depends_on:
      redis:
        condition: service_healthy
      sqlserver:
        condition: service_healthy
    networks:
      - persimmon-network
    restart: unless-stopped

  # 推荐服务
  recommendation-service:
    build:
      context: .
      dockerfile: src/Services/RecommendationService/Dockerfile
    container_name: persimmon-recommendation-service
    ports:
      - "5006:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=PersimmonChic_Super_Secret_Key_2024_Demo_Application
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - persimmon-network
    restart: unless-stopped

  # 搜索服务
  search-service:
    build:
      context: .
      dockerfile: src/Services/SearchService/Dockerfile
    container_name: persimmon-search-service
    ports:
      - "5007:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=PersimmonChic_Super_Secret_Key_2024_Demo_Application
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - persimmon-network
    restart: unless-stopped

  # AI客服机器人服务
  customer-service-bot:
    build:
      context: .
      dockerfile: src/Services/CustomerServiceBot/Dockerfile
    container_name: persimmon-customer-service-bot
    ports:
      - "5008:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=PersimmonChic_Super_Secret_Key_2024_Demo_Application
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - persimmon-network
    restart: unless-stopped

networks:
  persimmon-network:
    driver: bridge
    name: persimmon-network

volumes:
  redis_data:
    name: persimmon-redis-data
  sqlserver_data:
    name: persimmon-sqlserver-data
