using Microsoft.Extensions.Logging;
using PersimmonChic.Shared.Models;
using System.Text;
using System.Text.Json;

namespace PersimmonChic.Shared.Common;

/// <summary>
/// HTTP客户端工厂
/// </summary>
public class HttpClientFactory
{
    private readonly ServiceRegistry _serviceRegistry;
    private readonly LoadBalancer _loadBalancer;
    private readonly ILogger<HttpClientFactory> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public HttpClientFactory(ServiceRegistry serviceRegistry, LoadBalancer loadBalancer, ILogger<HttpClientFactory> logger)
    {
        _serviceRegistry = serviceRegistry;
        _loadBalancer = loadBalancer;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// 创建HTTP客户端
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <returns>HTTP客户端</returns>
    public HttpClient? CreateClient(string serviceName)
    {
        var instance = _loadBalancer.SelectInstance(serviceName);
        if (instance == null)
        {
            _logger.LogWarning("No healthy instance found for service {ServiceName}", serviceName);
            return null;
        }

        var client = new HttpClient
        {
            BaseAddress = new Uri(instance.BaseUrl),
            Timeout = TimeSpan.FromSeconds(30)
        };

        client.DefaultRequestHeaders.Add("User-Agent", "PersimmonChic-Client/1.0");
        client.DefaultRequestHeaders.Add("X-Service-Version", instance.Version);

        return client;
    }

    /// <summary>
    /// 发送GET请求
    /// </summary>
    /// <typeparam name="T">响应类型</typeparam>
    /// <param name="serviceName">服务名称</param>
    /// <param name="endpoint">端点</param>
    /// <param name="headers">请求头</param>
    /// <returns>响应结果</returns>
    public async Task<ApiResponse<T>> GetAsync<T>(string serviceName, string endpoint, Dictionary<string, string>? headers = null)
    {
        using var client = CreateClient(serviceName);
        if (client == null)
        {
            return ApiResponse<T>.ErrorResult($"Service {serviceName} is not available");
        }

        try
        {
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }
            }

            var response = await client.GetAsync(endpoint);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ApiResponse<T>>(content, _jsonOptions);
                return result ?? ApiResponse<T>.ErrorResult("Failed to deserialize response");
            }
            else
            {
                _logger.LogWarning("GET request to {ServiceName}{Endpoint} failed with status {StatusCode}: {Content}",
                    serviceName, endpoint, response.StatusCode, content);
                return ApiResponse<T>.ErrorResult($"Request failed with status {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while sending GET request to {ServiceName}{Endpoint}",
                serviceName, endpoint);
            return ApiResponse<T>.ErrorResult($"Request failed: {ex.Message}");
        }
    }

    /// <summary>
    /// 发送POST请求
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="serviceName">服务名称</param>
    /// <param name="endpoint">端点</param>
    /// <param name="request">请求数据</param>
    /// <param name="headers">请求头</param>
    /// <returns>响应结果</returns>
    public async Task<ApiResponse<TResponse>> PostAsync<TRequest, TResponse>(
        string serviceName, 
        string endpoint, 
        TRequest request, 
        Dictionary<string, string>? headers = null)
    {
        using var client = CreateClient(serviceName);
        if (client == null)
        {
            return ApiResponse<TResponse>.ErrorResult($"Service {serviceName} is not available");
        }

        try
        {
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }
            }

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(endpoint, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ApiResponse<TResponse>>(responseContent, _jsonOptions);
                return result ?? ApiResponse<TResponse>.ErrorResult("Failed to deserialize response");
            }
            else
            {
                _logger.LogWarning("POST request to {ServiceName}{Endpoint} failed with status {StatusCode}: {Content}",
                    serviceName, endpoint, response.StatusCode, responseContent);
                return ApiResponse<TResponse>.ErrorResult($"Request failed with status {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while sending POST request to {ServiceName}{Endpoint}",
                serviceName, endpoint);
            return ApiResponse<TResponse>.ErrorResult($"Request failed: {ex.Message}");
        }
    }

    /// <summary>
    /// 发送PUT请求
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="serviceName">服务名称</param>
    /// <param name="endpoint">端点</param>
    /// <param name="request">请求数据</param>
    /// <param name="headers">请求头</param>
    /// <returns>响应结果</returns>
    public async Task<ApiResponse<TResponse>> PutAsync<TRequest, TResponse>(
        string serviceName, 
        string endpoint, 
        TRequest request, 
        Dictionary<string, string>? headers = null)
    {
        using var client = CreateClient(serviceName);
        if (client == null)
        {
            return ApiResponse<TResponse>.ErrorResult($"Service {serviceName} is not available");
        }

        try
        {
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }
            }

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PutAsync(endpoint, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ApiResponse<TResponse>>(responseContent, _jsonOptions);
                return result ?? ApiResponse<TResponse>.ErrorResult("Failed to deserialize response");
            }
            else
            {
                _logger.LogWarning("PUT request to {ServiceName}{Endpoint} failed with status {StatusCode}: {Content}",
                    serviceName, endpoint, response.StatusCode, responseContent);
                return ApiResponse<TResponse>.ErrorResult($"Request failed with status {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while sending PUT request to {ServiceName}{Endpoint}",
                serviceName, endpoint);
            return ApiResponse<TResponse>.ErrorResult($"Request failed: {ex.Message}");
        }
    }

    /// <summary>
    /// 发送DELETE请求
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="endpoint">端点</param>
    /// <param name="headers">请求头</param>
    /// <returns>响应结果</returns>
    public async Task<ApiResponse<bool>> DeleteAsync(string serviceName, string endpoint, Dictionary<string, string>? headers = null)
    {
        using var client = CreateClient(serviceName);
        if (client == null)
        {
            return ApiResponse<bool>.ErrorResult($"Service {serviceName} is not available");
        }

        try
        {
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }
            }

            var response = await client.DeleteAsync(endpoint);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ApiResponse<bool>>(content, _jsonOptions);
                return result ?? ApiResponse<bool>.ErrorResult("Failed to deserialize response");
            }
            else
            {
                _logger.LogWarning("DELETE request to {ServiceName}{Endpoint} failed with status {StatusCode}: {Content}",
                    serviceName, endpoint, response.StatusCode, content);
                return ApiResponse<bool>.ErrorResult($"Request failed with status {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while sending DELETE request to {ServiceName}{Endpoint}",
                serviceName, endpoint);
            return ApiResponse<bool>.ErrorResult($"Request failed: {ex.Message}");
        }
    }
}
