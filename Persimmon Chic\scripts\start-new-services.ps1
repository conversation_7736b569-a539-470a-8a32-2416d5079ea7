# Persimmon Chic 新微服务启动脚本
# 启动推荐服务、搜索服务和AI客服机器人服务

param(
    [string]$ServiceName = "all",
    [string]$Configuration = "Debug"
)

Write-Host "=== Persimmon Chic 新微服务启动 ===" -ForegroundColor Green
Write-Host ""

# 服务配置
$services = @{
    "RecommendationService" = @{
        "Name" = "推荐服务"
        "Port" = 5006
        "Path" = "src\Services\RecommendationService"
        "Project" = "PersimmonChic.RecommendationService.csproj"
    }
    "SearchService" = @{
        "Name" = "搜索服务"
        "Port" = 5007
        "Path" = "src\Services\SearchService"
        "Project" = "PersimmonChic.SearchService.csproj"
    }
    "CustomerServiceBot" = @{
        "Name" = "AI客服机器人"
        "Port" = 5008
        "Path" = "src\Services\CustomerServiceBot"
        "Project" = "PersimmonChic.CustomerServiceBot.csproj"
    }
}

function Start-SingleService {
    param(
        [string]$Key,
        [hashtable]$Config
    )
    
    $serviceName = $Config.Name
    $port = $Config.Port
    $path = $Config.Path
    $project = $Config.Project
    
    Write-Host "启动 $serviceName (端口: $port)" -ForegroundColor Yellow
    
    # 检查项目文件
    $projectFile = Join-Path $path $project
    if (-not (Test-Path $projectFile)) {
        Write-Host "错误: 项目文件不存在: $projectFile" -ForegroundColor Red
        return $false
    }
    
    try {
        # 构建项目
        Write-Host "  构建项目..." -ForegroundColor Cyan
        $buildCmd = "dotnet build `"$projectFile`" --configuration $Configuration"
        Invoke-Expression $buildCmd
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "  构建失败" -ForegroundColor Red
            return $false
        }
        
        Write-Host "  构建成功" -ForegroundColor Green
        
        # 启动服务
        Write-Host "  启动服务..." -ForegroundColor Cyan
        $runCmd = "dotnet run --project `"$projectFile`" --configuration $Configuration --urls `"http://localhost:$port`""
        
        # 在新窗口中启动
        Start-Process powershell -ArgumentList "-NoExit", "-Command", $runCmd
        
        Write-Host "  服务已在新窗口中启动" -ForegroundColor Green
        Write-Host "  访问地址: http://localhost:$port" -ForegroundColor Green
        Write-Host ""
        
        return $true
    }
    catch {
        Write-Host "  启动失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主逻辑
if ($ServiceName -eq "all") {
    Write-Host "启动所有新微服务..." -ForegroundColor Green
    Write-Host ""
    
    $successCount = 0
    foreach ($key in $services.Keys) {
        if (Start-SingleService -Key $key -Config $services[$key]) {
            $successCount++
            Start-Sleep -Seconds 2  # 延迟启动
        }
    }
    
    Write-Host "启动完成: $successCount/$($services.Count) 个服务" -ForegroundColor Green
}
else {
    if ($services.ContainsKey($ServiceName)) {
        Start-SingleService -Key $ServiceName -Config $services[$ServiceName]
    }
    else {
        Write-Host "错误: 未知服务 '$ServiceName'" -ForegroundColor Red
        Write-Host "可用服务: $($services.Keys -join ', ')" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=== 服务信息 ===" -ForegroundColor Cyan
foreach ($key in $services.Keys) {
    $service = $services[$key]
    Write-Host "$($service.Name): http://localhost:$($service.Port)" -ForegroundColor White
}

Write-Host ""
Write-Host "使用 .\test-services.ps1 测试服务功能" -ForegroundColor Yellow
