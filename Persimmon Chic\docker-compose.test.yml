version: '3.8'

services:
  # Redis缓存服务 - 测试用
  redis-test:
    image: redis:7-alpine
    container_name: persimmon-redis-test
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 5s
    networks:
      - persimmon-test-network

  # Gateway服务 - 测试用
  gateway-test:
    build:
      context: .
      dockerfile: src/Gateway/Dockerfile
    container_name: persimmon-gateway-test
    ports:
      - "5100:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis-test:6379
      - Logging__LogLevel__Default=Debug
    depends_on:
      redis-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - persimmon-test-network
    restart: unless-stopped

  # UserService - 测试用
  user-service-test:
    build:
      context: .
      dockerfile: src/Services/UserService/Dockerfile
    container_name: persimmon-user-service-test
    ports:
      - "5101:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis-test:6379
      - Logging__LogLevel__Default=Debug
      - JwtSettings__SecretKey=TestSecretKey2024
    depends_on:
      redis-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - persimmon-test-network
    restart: unless-stopped

  # RecommendationService - 测试用
  recommendation-service-test:
    build:
      context: .
      dockerfile: src/Services/RecommendationService/Dockerfile
    container_name: persimmon-recommendation-service-test
    ports:
      - "5106:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis-test:6379
      - Logging__LogLevel__Default=Debug
    depends_on:
      redis-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - persimmon-test-network
    restart: unless-stopped

networks:
  persimmon-test-network:
    driver: bridge
    name: persimmon-test-network

volumes:
  redis-test-data:
    name: persimmon-redis-test-data
