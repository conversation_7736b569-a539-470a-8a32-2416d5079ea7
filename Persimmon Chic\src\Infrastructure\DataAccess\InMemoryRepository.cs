using Microsoft.Extensions.Logging;
using PersimmonChic.Shared.Models;
using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Reflection;

namespace PersimmonChic.Infrastructure.DataAccess;

/// <summary>
/// 内存仓储实现（用于演示）
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class InMemoryRepository<T> : IRepository<T> where T : class
{
    private readonly ConcurrentDictionary<object, T> _data = new();
    private readonly ILogger<InMemoryRepository<T>> _logger;
    private int _nextId = 1;

    public InMemoryRepository(ILogger<InMemoryRepository<T>> logger)
    {
        _logger = logger;
    }

    public Task<T?> GetByIdAsync(object id)
    {
        _data.TryGetValue(id, out var entity);
        return Task.FromResult(entity);
    }

    public Task<List<T>> GetAllAsync()
    {
        return Task.FromResult(_data.Values.ToList());
    }

    public Task<List<T>> FindAsync(Expression<Func<T, bool>> predicate)
    {
        var compiled = predicate.Compile();
        var results = _data.Values.Where(compiled).ToList();
        return Task.FromResult(results);
    }

    public Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate)
    {
        var compiled = predicate.Compile();
        var result = _data.Values.FirstOrDefault(compiled);
        return Task.FromResult(result);
    }

    public Task<PagedResponse<T>> GetPagedAsync<TKey>(
        int page, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, TKey>>? orderBy = null,
        bool ascending = true)
    {
        var query = _data.Values.AsQueryable();

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        var totalCount = query.Count();

        if (orderBy != null)
        {
            query = ascending ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
        }

        var items = query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        var response = new PagedResponse<T>
        {
            Items = items,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize
        };

        return Task.FromResult(response);
    }

    public Task<T> AddAsync(T entity)
    {
        var id = GetNextId(entity);
        SetEntityId(entity, id);
        _data[id] = entity;
        
        _logger.LogDebug("Added entity {EntityType} with ID {Id}", typeof(T).Name, id);
        return Task.FromResult(entity);
    }

    public Task<List<T>> AddRangeAsync(IEnumerable<T> entities)
    {
        var addedEntities = new List<T>();
        foreach (var entity in entities)
        {
            var id = GetNextId(entity);
            SetEntityId(entity, id);
            _data[id] = entity;
            addedEntities.Add(entity);
        }

        _logger.LogDebug("Added {Count} entities of type {EntityType}", addedEntities.Count, typeof(T).Name);
        return Task.FromResult(addedEntities);
    }

    public Task<T> UpdateAsync(T entity)
    {
        var id = GetEntityId(entity);
        if (id != null && _data.ContainsKey(id))
        {
            _data[id] = entity;
            SetUpdatedAt(entity);
            _logger.LogDebug("Updated entity {EntityType} with ID {Id}", typeof(T).Name, id);
        }
        else
        {
            _logger.LogWarning("Attempted to update non-existent entity {EntityType} with ID {Id}", typeof(T).Name, id);
        }

        return Task.FromResult(entity);
    }

    public Task<bool> DeleteAsync(T entity)
    {
        var id = GetEntityId(entity);
        if (id != null)
        {
            var removed = _data.TryRemove(id, out _);
            if (removed)
            {
                _logger.LogDebug("Deleted entity {EntityType} with ID {Id}", typeof(T).Name, id);
            }
            return Task.FromResult(removed);
        }

        return Task.FromResult(false);
    }

    public Task<bool> DeleteByIdAsync(object id)
    {
        var removed = _data.TryRemove(id, out _);
        if (removed)
        {
            _logger.LogDebug("Deleted entity {EntityType} with ID {Id}", typeof(T).Name, id);
        }
        return Task.FromResult(removed);
    }

    public Task<int> DeleteAsync(Expression<Func<T, bool>> predicate)
    {
        var compiled = predicate.Compile();
        var toDelete = _data.Values.Where(compiled).ToList();
        
        int deletedCount = 0;
        foreach (var entity in toDelete)
        {
            var id = GetEntityId(entity);
            if (id != null && _data.TryRemove(id, out _))
            {
                deletedCount++;
            }
        }

        _logger.LogDebug("Deleted {Count} entities of type {EntityType}", deletedCount, typeof(T).Name);
        return Task.FromResult(deletedCount);
    }

    public Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate)
    {
        var compiled = predicate.Compile();
        var exists = _data.Values.Any(compiled);
        return Task.FromResult(exists);
    }

    public Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null)
    {
        if (predicate == null)
        {
            return Task.FromResult(_data.Count);
        }

        var compiled = predicate.Compile();
        var count = _data.Values.Count(compiled);
        return Task.FromResult(count);
    }

    public Task<int> SaveChangesAsync()
    {
        // 内存实现不需要显式保存
        return Task.FromResult(0);
    }

    private object GetNextId(T entity)
    {
        // 简单的ID生成策略
        var idProperty = GetIdProperty();
        if (idProperty?.PropertyType == typeof(int))
        {
            return Interlocked.Increment(ref _nextId);
        }
        else if (idProperty?.PropertyType == typeof(string))
        {
            return Guid.NewGuid().ToString();
        }
        else
        {
            return Guid.NewGuid();
        }
    }

    private object? GetEntityId(T entity)
    {
        var idProperty = GetIdProperty();
        return idProperty?.GetValue(entity);
    }

    private void SetEntityId(T entity, object id)
    {
        var idProperty = GetIdProperty();
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(entity, id);
        }
    }

    private void SetUpdatedAt(T entity)
    {
        var updatedAtProperty = typeof(T).GetProperty("UpdatedAt");
        if (updatedAtProperty != null && updatedAtProperty.PropertyType == typeof(DateTime?) && updatedAtProperty.CanWrite)
        {
            updatedAtProperty.SetValue(entity, DateTime.UtcNow);
        }
    }

    private PropertyInfo? GetIdProperty()
    {
        return typeof(T).GetProperty("Id") ?? typeof(T).GetProperties()
            .FirstOrDefault(p => p.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase));
    }
}
