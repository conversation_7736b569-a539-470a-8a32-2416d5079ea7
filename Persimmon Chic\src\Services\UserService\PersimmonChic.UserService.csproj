<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\Models\PersimmonChic.Shared.Models.csproj" />
    <ProjectReference Include="..\..\Shared\Contracts\PersimmonChic.Shared.Contracts.csproj" />
    <ProjectReference Include="..\..\Shared\Common\PersimmonChic.Shared.Common.csproj" />
    <ProjectReference Include="..\..\Infrastructure\DataAccess\PersimmonChic.Infrastructure.DataAccess.csproj" />
  </ItemGroup>

</Project>
