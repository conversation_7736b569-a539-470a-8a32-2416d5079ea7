using Microsoft.Extensions.Logging;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.SearchService.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.SearchService.Services;

/// <summary>
/// 搜索服务主要实现
/// </summary>
public class SearchService : ISearchService
{
    private readonly ILogger<SearchService> _logger;
    private readonly ISemanticSearchEngine _semanticEngine;
    private readonly IKeywordSearchEngine _keywordEngine;
    private readonly IHybridSearchEngine _hybridEngine;
    private readonly ISearchAnalyticsService _analyticsService;
    private readonly ISearchCacheService _cacheService;
    private readonly IRepository<SearchQueryLog> _queryLogRepository;

    public SearchService(
        ILogger<SearchService> logger,
        ISemanticSearchEngine semanticEngine,
        IKeywordSearchEngine keywordEngine,
        IHybridSearchEngine hybridEngine,
        ISearchAnalyticsService analyticsService,
        ISearchCacheService cacheService,
        IRepository<SearchQueryLog> queryLogRepository)
    {
        _logger = logger;
        _semanticEngine = semanticEngine;
        _keywordEngine = keywordEngine;
        _hybridEngine = hybridEngine;
        _analyticsService = analyticsService;
        _cacheService = cacheService;
        _queryLogRepository = queryLogRepository;
    }

    public async Task<ApiResponse<SearchResponse>> SearchAsync(SearchRequest request)
    {
        try
        {
            _logger.LogInformation("开始搜索，查询: {Query}, 类型: {SearchType}, 页码: {Page}", 
                request.Query, request.SearchType, request.Page);

            var startTime = DateTime.UtcNow;
            var searchId = Guid.NewGuid().ToString("N")[..8];

            // 1. 检查缓存
            var cacheKey = _cacheService.GenerateSearchCacheKey(request);
            var cachedResult = await _cacheService.GetCachedSearchResultAsync(cacheKey);
            
            if (cachedResult != null)
            {
                _logger.LogDebug("从缓存获取搜索结果，查询: {Query}", request.Query);
                return ApiResponse<SearchResponse>.SuccessResult(cachedResult);
            }

            // 2. 根据搜索类型执行搜索
            var searchResults = await ExecuteSearchByTypeAsync(request);
            
            if (!searchResults.Success)
            {
                return ApiResponse<SearchResponse>.ErrorResult(searchResults.Message);
            }

            // 3. 应用分页
            var pagedResults = ApplyPagination(searchResults.Data, request.Page, request.PageSize);

            // 4. 生成分面信息
            var facets = request.EnableFacets ? await GenerateFacetsAsync(searchResults.Data, request) : new List<SearchFacet>();

            // 5. 生成搜索建议
            var suggestions = await GetSuggestionsAsync(request.Query);

            // 6. 构建响应
            var elapsedMs = (DateTime.UtcNow - startTime).TotalMilliseconds;
            
            var response = new SearchResponse
            {
                Query = request.Query,
                Results = pagedResults,
                Metadata = new SearchMetadata
                {
                    TotalResults = searchResults.Data.Count,
                    Page = request.Page,
                    PageSize = request.PageSize,
                    TotalPages = (int)Math.Ceiling((double)searchResults.Data.Count / request.PageSize),
                    SearchTimeMs = (long)elapsedMs,
                    SearchId = searchId,
                    SearchType = request.SearchType,
                    Algorithm = GetAlgorithmName(request.SearchType)
                },
                Facets = facets,
                Suggestions = suggestions.Success ? suggestions.Data : new List<string>(),
                Analytics = new SearchAnalytics
                {
                    SessionId = request.Context.ContainsKey("session_id") ? request.Context["session_id"].ToString() ?? "" : "",
                    UserId = request.Context.ContainsKey("user_id") ? request.Context["user_id"].ToString() ?? "" : "",
                    UserAgent = request.Context.ContainsKey("user_agent") ? request.Context["user_agent"].ToString() ?? "" : "",
                    IpAddress = request.Context.ContainsKey("ip_address") ? request.Context["ip_address"].ToString() ?? "" : "",
                    CustomData = request.Context
                }
            };

            // 7. 缓存结果
            await _cacheService.SetSearchResultCacheAsync(cacheKey, response, TimeSpan.FromMinutes(15));

            // 8. 记录搜索日志
            await LogSearchQueryAsync(new SearchQueryLog
            {
                Query = request.Query,
                UserId = response.Analytics.UserId,
                SessionId = response.Analytics.SessionId,
                SearchType = request.SearchType,
                ResultCount = response.Results.Count,
                ResponseTimeMs = (long)elapsedMs,
                HasResults = response.Results.Any(),
                Filters = request.Filters,
                Context = request.Context
            });

            // 9. 记录分析事件
            await _analyticsService.RecordSearchEventAsync("search", request.Query, response.Analytics.UserId, 
                new Dictionary<string, object>
                {
                    ["search_type"] = request.SearchType.ToString(),
                    ["result_count"] = response.Results.Count,
                    ["response_time_ms"] = elapsedMs,
                    ["page"] = request.Page
                });

            _logger.LogInformation("搜索完成，查询: {Query}, 结果数: {Count}, 耗时: {ElapsedMs}ms", 
                request.Query, response.Results.Count, elapsedMs);

            return ApiResponse<SearchResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索时发生错误");
            return ApiResponse<SearchResponse>.ErrorResult($"搜索失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<AutoCompleteResponse>> AutoCompleteAsync(AutoCompleteRequest request)
    {
        try
        {
            _logger.LogInformation("开始自动补全，查询: {Query}, 最大建议数: {MaxSuggestions}", 
                request.Query, request.MaxSuggestions);

            var startTime = DateTime.UtcNow;

            // 检查缓存
            var cachedResult = await _cacheService.GetCachedAutoCompleteAsync(request.Query);
            if (cachedResult != null)
            {
                return ApiResponse<AutoCompleteResponse>.SuccessResult(cachedResult);
            }

            var suggestions = new List<AutoCompleteSuggestion>();

            // 1. 基于查询历史的建议
            if (request.IncludeHistory)
            {
                var historySuggestions = await GetHistoryBasedSuggestionsAsync(request.Query, request.MaxSuggestions / 2);
                suggestions.AddRange(historySuggestions);
            }

            // 2. 基于热门搜索的建议
            if (request.IncludePopular)
            {
                var popularSuggestions = await GetPopularBasedSuggestionsAsync(request.Query, request.MaxSuggestions / 2);
                suggestions.AddRange(popularSuggestions);
            }

            // 3. 去重并排序
            var uniqueSuggestions = suggestions
                .GroupBy(s => s.Text.ToLower())
                .Select(g => g.OrderByDescending(s => s.Score).First())
                .OrderByDescending(s => s.Score)
                .Take(request.MaxSuggestions)
                .ToList();

            var elapsedMs = (DateTime.UtcNow - startTime).TotalMilliseconds;

            var response = new AutoCompleteResponse
            {
                Query = request.Query,
                Suggestions = uniqueSuggestions,
                ResponseTimeMs = (long)elapsedMs
            };

            // 缓存结果
            await _cacheService.SetAutoCompleteCacheAsync(request.Query, response, TimeSpan.FromMinutes(30));

            _logger.LogInformation("自动补全完成，查询: {Query}, 建议数: {Count}, 耗时: {ElapsedMs}ms", 
                request.Query, response.Suggestions.Count, elapsedMs);

            return ApiResponse<AutoCompleteResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动补全时发生错误");
            return ApiResponse<AutoCompleteResponse>.ErrorResult($"自动补全失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<string>>> GetSuggestionsAsync(string query, int count = 10)
    {
        try
        {
            var suggestions = new List<string>();

            // 基于查询的简单建议生成
            var queryWords = query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            
            // 模拟建议生成
            var baseSuggestions = new[]
            {
                "智能手机", "蓝牙耳机", "无线充电器", "平板电脑", "智能手表",
                "游戏鼠标", "机械键盘", "显示器", "音响", "相机"
            };

            foreach (var baseSuggestion in baseSuggestions)
            {
                if (baseSuggestion.Contains(query, StringComparison.OrdinalIgnoreCase) ||
                    queryWords.Any(word => baseSuggestion.Contains(word, StringComparison.OrdinalIgnoreCase)))
                {
                    suggestions.Add(baseSuggestion);
                }
            }

            // 如果没有匹配的建议，返回热门搜索
            if (!suggestions.Any())
            {
                suggestions.AddRange(baseSuggestions.Take(count));
            }

            return ApiResponse<List<string>>.SuccessResult(suggestions.Take(count).ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索建议时发生错误");
            return ApiResponse<List<string>>.ErrorResult($"获取建议失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> LogSearchQueryAsync(SearchQueryLog queryLog)
    {
        try
        {
            await _queryLogRepository.AddAsync(queryLog);
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录搜索查询日志时发生错误");
            return ApiResponse<bool>.ErrorResult($"记录查询日志失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<string>>> GetTrendingQueriesAsync(int count = 10, int timeRange = 24)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow.AddHours(-timeRange);
            var recentQueries = await _queryLogRepository.FindAsync(q => q.Timestamp >= cutoffTime && q.HasResults);

            var trendingQueries = recentQueries
                .GroupBy(q => q.Query.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(count)
                .Select(g => g.Key)
                .ToList();

            return ApiResponse<List<string>>.SuccessResult(trendingQueries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门搜索词时发生错误");
            return ApiResponse<List<string>>.ErrorResult($"获取热门搜索词失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<Dictionary<string, object>>> GetSearchAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-7);
            var end = endDate ?? DateTime.UtcNow;

            var queries = await _queryLogRepository.FindAsync(q => q.Timestamp >= start && q.Timestamp <= end);

            var analytics = new Dictionary<string, object>
            {
                ["total_searches"] = queries.Count,
                ["unique_queries"] = queries.Select(q => q.Query.ToLower()).Distinct().Count(),
                ["average_response_time"] = queries.Any() ? queries.Average(q => q.ResponseTimeMs) : 0,
                ["success_rate"] = queries.Any() ? (double)queries.Count(q => q.HasResults) / queries.Count : 0,
                ["top_queries"] = queries.GroupBy(q => q.Query.ToLower())
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .Select(g => new { query = g.Key, count = g.Count() })
                    .ToList(),
                ["search_types"] = queries.GroupBy(q => q.SearchType)
                    .Select(g => new { type = g.Key.ToString(), count = g.Count() })
                    .ToList(),
                ["period"] = new { start, end }
            };

            return ApiResponse<Dictionary<string, object>>.SuccessResult(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索分析数据时发生错误");
            return ApiResponse<Dictionary<string, object>>.ErrorResult($"获取分析数据失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private async Task<ApiResponse<List<SearchResult>>> ExecuteSearchByTypeAsync(SearchRequest request)
    {
        return request.SearchType switch
        {
            SearchType.Semantic => await _semanticEngine.SemanticSearchAsync(request.Query, request.PageSize * 2),
            SearchType.Keyword => await _keywordEngine.KeywordSearchAsync(request.Query, request.Filters, request.SortBy, 1, request.PageSize * 2),
            SearchType.Hybrid => await _hybridEngine.HybridSearchAsync(request.Query, 0.6f, 0.4f, request.PageSize * 2),
            SearchType.Fuzzy => await _keywordEngine.FuzzySearchAsync(request.Query, 2, request.PageSize * 2),
            SearchType.Exact => await _keywordEngine.ExactMatchSearchAsync(request.Query, null, request.PageSize * 2),
            _ => await _hybridEngine.HybridSearchAsync(request.Query, 0.6f, 0.4f, request.PageSize * 2)
        };
    }

    private List<SearchResult> ApplyPagination(List<SearchResult> results, int page, int pageSize)
    {
        var skip = (page - 1) * pageSize;
        return results.Skip(skip).Take(pageSize).ToList();
    }

    private async Task<List<SearchFacet>> GenerateFacetsAsync(List<SearchResult> results, SearchRequest request)
    {
        var facets = new List<SearchFacet>();

        // 类别分面
        var categoryFacet = new SearchFacet
        {
            Name = "category",
            DisplayName = "类别",
            Type = FacetType.Terms,
            Values = results
                .GroupBy(r => r.Category)
                .Select(g => new FacetValue
                {
                    Value = g.Key,
                    DisplayValue = g.Key,
                    Count = g.Count(),
                    Selected = request.Categories.Contains(g.Key)
                })
                .OrderByDescending(v => v.Count)
                .Take(10)
                .ToList()
        };
        facets.Add(categoryFacet);

        // 品牌分面
        var brandFacet = new SearchFacet
        {
            Name = "brand",
            DisplayName = "品牌",
            Type = FacetType.Terms,
            Values = results
                .GroupBy(r => r.Brand)
                .Select(g => new FacetValue
                {
                    Value = g.Key,
                    DisplayValue = g.Key,
                    Count = g.Count(),
                    Selected = request.Brands.Contains(g.Key)
                })
                .OrderByDescending(v => v.Count)
                .Take(10)
                .ToList()
        };
        facets.Add(brandFacet);

        // 价格区间分面
        var priceFacet = new SearchFacet
        {
            Name = "price",
            DisplayName = "价格",
            Type = FacetType.Range,
            Values = new List<FacetValue>
            {
                new FacetValue { Value = "0-100", DisplayValue = "0-100元", Count = results.Count(r => r.Price <= 100) },
                new FacetValue { Value = "100-500", DisplayValue = "100-500元", Count = results.Count(r => r.Price > 100 && r.Price <= 500) },
                new FacetValue { Value = "500-1000", DisplayValue = "500-1000元", Count = results.Count(r => r.Price > 500 && r.Price <= 1000) },
                new FacetValue { Value = "1000+", DisplayValue = "1000元以上", Count = results.Count(r => r.Price > 1000) }
            }
        };
        facets.Add(priceFacet);

        return facets;
    }

    private async Task<List<AutoCompleteSuggestion>> GetHistoryBasedSuggestionsAsync(string query, int count)
    {
        try
        {
            var recentQueries = await _queryLogRepository.FindAsync(q => 
                q.Query.Contains(query, StringComparison.OrdinalIgnoreCase) && 
                q.HasResults &&
                q.Timestamp >= DateTime.UtcNow.AddDays(-30));

            return recentQueries
                .GroupBy(q => q.Query.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(count)
                .Select(g => new AutoCompleteSuggestion
                {
                    Text = g.First().Query,
                    DisplayText = g.First().Query,
                    Type = SuggestionType.History,
                    Score = g.Count(),
                    Frequency = g.Count()
                })
                .ToList();
        }
        catch
        {
            return new List<AutoCompleteSuggestion>();
        }
    }

    private async Task<List<AutoCompleteSuggestion>> GetPopularBasedSuggestionsAsync(string query, int count)
    {
        var popularQueries = new[]
        {
            "智能手机", "蓝牙耳机", "无线充电器", "平板电脑", "智能手表",
            "游戏鼠标", "机械键盘", "显示器", "音响", "相机"
        };

        return popularQueries
            .Where(q => q.Contains(query, StringComparison.OrdinalIgnoreCase))
            .Take(count)
            .Select((q, index) => new AutoCompleteSuggestion
            {
                Text = q,
                DisplayText = q,
                Type = SuggestionType.Popular,
                Score = 100 - index,
                Frequency = 100 - index
            })
            .ToList();
    }

    private string GetAlgorithmName(SearchType searchType)
    {
        return searchType switch
        {
            SearchType.Semantic => "SemanticSearch",
            SearchType.Keyword => "KeywordSearch",
            SearchType.Hybrid => "HybridSearch",
            SearchType.Fuzzy => "FuzzySearch",
            SearchType.Exact => "ExactMatch",
            _ => "HybridSearch"
        };
    }
}
