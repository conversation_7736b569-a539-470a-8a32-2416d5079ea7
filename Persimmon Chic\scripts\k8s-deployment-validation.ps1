# Persimmon Chic Kubernetes 部署验证脚本
param(
    [string]$Namespace = "persimmon-chic",
    [int]$TimeoutSeconds = 300
)

Write-Host "=== Persimmon Chic Kubernetes 部署验证 ===" -ForegroundColor Green
Write-Host "命名空间: $Namespace" -ForegroundColor Yellow
Write-Host "超时时间: $TimeoutSeconds 秒" -ForegroundColor Yellow
Write-Host ""

# 验证结果记录
$validationResults = @{
    "KubernetesConnection" = $false
    "NamespaceCreation" = $false
    "ConfigMapApplication" = $false
    "SecretApplication" = $false
    "ServiceDeployment" = $false
    "HealthChecks" = $false
    "NetworkConnectivity" = $false
}

$totalValidations = $validationResults.Count
$passedValidations = 0

function Test-KubernetesConnection {
    Write-Host "🔍 第一步：Kubernetes连接验证" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 检查kubectl是否可用
        $kubectlVersion = & kubectl version --client --short 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ kubectl已安装: $kubectlVersion" -ForegroundColor Green
        } else {
            Write-Host "  ✗ kubectl未安装或不可用" -ForegroundColor Red
            return $false
        }
        
        # 检查集群连接
        $clusterInfo = & kubectl cluster-info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Kubernetes集群连接正常" -ForegroundColor Green
        } else {
            Write-Host "  ✗ 无法连接到Kubernetes集群" -ForegroundColor Red
            return $false
        }
        
        Write-Host "  ✓ Kubernetes连接验证通过" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "  ✗ Kubernetes连接验证失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-NamespaceCreation {
    Write-Host ""
    Write-Host "📦 第二步：命名空间创建验证" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 应用命名空间配置
        Write-Host "  创建命名空间..." -ForegroundColor Yellow
        $namespaceResult = & kubectl apply -f k8s/namespace.yaml 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 命名空间创建成功" -ForegroundColor Green
            
            # 验证命名空间存在
            $namespaceCheck = & kubectl get namespace $Namespace 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✓ 命名空间验证通过" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  ✗ 命名空间验证失败" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "  ✗ 命名空间创建失败" -ForegroundColor Red
            Write-Host "  错误信息: $namespaceResult" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 命名空间创建异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ConfigMapApplication {
    Write-Host ""
    Write-Host "⚙️ 第三步：配置映射应用验证" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 应用ConfigMap
        Write-Host "  应用配置映射..." -ForegroundColor Yellow
        $configMapResult = & kubectl apply -f k8s/configmap.yaml 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 配置映射应用成功" -ForegroundColor Green
            
            # 验证ConfigMap存在
            $configMapCheck = & kubectl get configmap -n $Namespace persimmon-chic-config 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✓ 配置映射验证通过" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  ✗ 配置映射验证失败" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "  ✗ 配置映射应用失败" -ForegroundColor Red
            Write-Host "  错误信息: $configMapResult" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 配置映射应用异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-SecretApplication {
    Write-Host ""
    Write-Host "🔐 第四步：密钥应用验证" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 应用Secret
        Write-Host "  应用密钥配置..." -ForegroundColor Yellow
        $secretResult = & kubectl apply -f k8s/secrets.yaml 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 密钥应用成功" -ForegroundColor Green
            
            # 验证Secret存在
            $secretCheck = & kubectl get secret -n $Namespace persimmon-chic-secrets 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✓ 密钥验证通过" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  ✗ 密钥验证失败" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "  ✗ 密钥应用失败" -ForegroundColor Red
            Write-Host "  错误信息: $secretResult" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 密钥应用异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ServiceDeployment {
    Write-Host ""
    Write-Host "🚀 第五步：服务部署验证" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 部署基础设施服务
        Write-Host "  部署基础设施服务..." -ForegroundColor Yellow
        $infraResult = & kubectl apply -f k8s/infrastructure.yaml 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 基础设施服务部署成功" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ 基础设施服务部署可能有问题" -ForegroundColor Yellow
            Write-Host "  信息: $infraResult" -ForegroundColor Yellow
        }
        
        # 部署用户服务
        Write-Host "  部署用户服务..." -ForegroundColor Yellow
        $userServiceResult = & kubectl apply -f k8s/user-service.yaml 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 用户服务部署成功" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ 用户服务部署可能有问题" -ForegroundColor Yellow
            Write-Host "  信息: $userServiceResult" -ForegroundColor Yellow
        }
        
        # 部署Gateway
        Write-Host "  部署API Gateway..." -ForegroundColor Yellow
        $gatewayResult = & kubectl apply -f k8s/gateway.yaml 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ API Gateway部署成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ✗ API Gateway部署失败" -ForegroundColor Red
            Write-Host "  错误信息: $gatewayResult" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 服务部署异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-HealthChecks {
    Write-Host ""
    Write-Host "✅ 第六步：健康检查验证" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        Write-Host "  等待Pod启动..." -ForegroundColor Yellow
        Start-Sleep -Seconds 30
        
        # 检查Pod状态
        $pods = & kubectl get pods -n $Namespace --no-headers 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Pod状态检查完成" -ForegroundColor Green
            Write-Host "  Pod列表:" -ForegroundColor Gray
            $pods | ForEach-Object { Write-Host "    $_" -ForegroundColor Gray }
            return $true
        } else {
            Write-Host "  ✗ Pod状态检查失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 健康检查异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-NetworkConnectivity {
    Write-Host ""
    Write-Host "🌐 第七步：网络连接验证" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 检查服务
        $services = & kubectl get services -n $Namespace --no-headers 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 服务列表获取成功" -ForegroundColor Green
            Write-Host "  服务列表:" -ForegroundColor Gray
            $services | ForEach-Object { Write-Host "    $_" -ForegroundColor Gray }
            return $true
        } else {
            Write-Host "  ✗ 服务列表获取失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 网络连接验证异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Cleanup-ValidationResources {
    Write-Host ""
    Write-Host "🧹 清理验证资源..." -ForegroundColor Yellow
    
    # 注意：这里不清理资源，因为这是部署验证，不是测试
    Write-Host "  ℹ 保留部署的资源用于后续使用" -ForegroundColor Cyan
    Write-Host "  如需清理，请手动执行: kubectl delete namespace $Namespace" -ForegroundColor Cyan
}

# 执行验证
try {
    Write-Host "开始执行Kubernetes部署验证..." -ForegroundColor Cyan
    Write-Host ""
    
    $validationResults["KubernetesConnection"] = Test-KubernetesConnection
    if ($validationResults["KubernetesConnection"]) { $passedValidations++ }
    
    $validationResults["NamespaceCreation"] = Test-NamespaceCreation
    if ($validationResults["NamespaceCreation"]) { $passedValidations++ }
    
    $validationResults["ConfigMapApplication"] = Test-ConfigMapApplication
    if ($validationResults["ConfigMapApplication"]) { $passedValidations++ }
    
    $validationResults["SecretApplication"] = Test-SecretApplication
    if ($validationResults["SecretApplication"]) { $passedValidations++ }
    
    $validationResults["ServiceDeployment"] = Test-ServiceDeployment
    if ($validationResults["ServiceDeployment"]) { $passedValidations++ }
    
    $validationResults["HealthChecks"] = Test-HealthChecks
    if ($validationResults["HealthChecks"]) { $passedValidations++ }
    
    $validationResults["NetworkConnectivity"] = Test-NetworkConnectivity
    if ($validationResults["NetworkConnectivity"]) { $passedValidations++ }
}
finally {
    # 清理资源
    Cleanup-ValidationResources
}

# 显示验证总结
Write-Host ""
Write-Host "📊 Kubernetes部署验证总结" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

foreach ($validation in $validationResults.GetEnumerator()) {
    $status = if ($validation.Value) { "✓ 通过" } else { "✗ 失败" }
    $color = if ($validation.Value) { "Green" } else { "Red" }
    Write-Host "$($validation.Key): $status" -ForegroundColor $color
}

Write-Host ""
$successRate = [math]::Round(($passedValidations / $totalValidations) * 100, 1)
$overallStatus = if ($passedValidations -eq $totalValidations) { "✓ 全部通过" } else { "⚠ 部分失败" }
$overallColor = if ($passedValidations -eq $totalValidations) { "Green" } else { "Yellow" }

Write-Host "总体结果: $overallStatus ($passedValidations/$totalValidations, $successRate%)" -ForegroundColor $overallColor
Write-Host "结束时间: $(Get-Date)" -ForegroundColor Yellow

# 提供下一步建议
Write-Host ""
Write-Host "🎯 下一步建议:" -ForegroundColor Cyan
if ($passedValidations -eq $totalValidations) {
    Write-Host "  ✅ Kubernetes部署验证完全通过！" -ForegroundColor Green
    Write-Host "  1. 检查Pod状态: kubectl get pods -n $Namespace" -ForegroundColor White
    Write-Host "  2. 查看服务状态: kubectl get services -n $Namespace" -ForegroundColor White
    Write-Host "  3. 测试应用功能: kubectl port-forward -n $Namespace service/gateway-service 8080:80" -ForegroundColor White
} else {
    Write-Host "  1. 检查失败的验证项目" -ForegroundColor White
    Write-Host "  2. 查看Pod日志: kubectl logs -n $Namespace <pod-name>" -ForegroundColor White
    Write-Host "  3. 检查事件: kubectl get events -n $Namespace" -ForegroundColor White
}

Write-Host ""

# 退出代码
exit $(if ($passedValidations -eq $totalValidations) { 0 } else { 1 })
