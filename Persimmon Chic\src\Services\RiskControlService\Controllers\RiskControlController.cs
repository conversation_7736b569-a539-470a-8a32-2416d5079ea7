using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PersimmonChic.RiskControlService.Models;
using PersimmonChic.RiskControlService.Services;
using PersimmonChic.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.RiskControlService.Controllers;

/// <summary>
/// 风控服务控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RiskControlController : ControllerBase
{
    private readonly IRiskControlService _riskControlService;
    private readonly ILogger<RiskControlController> _logger;

    public RiskControlController(
        IRiskControlService riskControlService,
        ILogger<RiskControlController> logger)
    {
        _riskControlService = riskControlService;
        _logger = logger;
    }

    /// <summary>
    /// 评估风险决策
    /// </summary>
    /// <param name="request">风控决策请求</param>
    /// <returns>风控决策响应</returns>
    [HttpPost("evaluate")]
    [ProducesResponseType(typeof(ApiResponse<RiskDecisionResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<RiskDecisionResponse>), 400)]
    public async Task<ActionResult<ApiResponse<RiskDecisionResponse>>> EvaluateRisk(
        [FromBody] RiskDecisionRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<RiskDecisionResponse>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.EvaluateRiskAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评估风险决策时发生错误");
            return StatusCode(500, ApiResponse<RiskDecisionResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 记录用户行为事件
    /// </summary>
    /// <param name="behaviorEvent">用户行为事件</param>
    /// <returns>操作结果</returns>
    [HttpPost("behavior-event")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    public async Task<ActionResult<ApiResponse<bool>>> RecordBehaviorEvent(
        [FromBody] UserBehaviorEvent behaviorEvent)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.RecordBehaviorEventAsync(behaviorEvent);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录用户行为事件时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取用户风险档案
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户风险档案</returns>
    [HttpGet("user-risk-profile/{userId}")]
    [ProducesResponseType(typeof(ApiResponse<UserRiskProfile>), 200)]
    [ProducesResponseType(typeof(ApiResponse<UserRiskProfile>), 404)]
    public async Task<ActionResult<ApiResponse<UserRiskProfile>>> GetUserRiskProfile(
        [FromRoute] int userId)
    {
        try
        {
            if (userId <= 0)
            {
                return BadRequest(ApiResponse<UserRiskProfile>.ErrorResult("用户ID无效"));
            }

            var result = await _riskControlService.GetUserRiskProfileAsync(userId);
            
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(ApiResponse<UserRiskProfile>.ErrorResult("用户风险档案不存在"));
                }
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户风险档案时发生错误");
            return StatusCode(500, ApiResponse<UserRiskProfile>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 更新用户风险档案
    /// </summary>
    /// <param name="profile">用户风险档案</param>
    /// <returns>操作结果</returns>
    [HttpPut("user-risk-profile")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateUserRiskProfile(
        [FromBody] UserRiskProfile profile)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.UpdateUserRiskProfileAsync(profile);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户风险档案时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 检测异常登录
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="userAgent">用户代理</param>
    /// <param name="deviceFingerprint">设备指纹（可选）</param>
    /// <returns>异常检测结果</returns>
    [HttpPost("detect-anomalous-login")]
    [ProducesResponseType(typeof(ApiResponse<List<AnomalousLoginRecord>>), 200)]
    [ProducesResponseType(typeof(ApiResponse<List<AnomalousLoginRecord>>), 400)]
    public async Task<ActionResult<ApiResponse<List<AnomalousLoginRecord>>>> DetectAnomalousLogin(
        [FromQuery, Required] int userId,
        [FromQuery, Required] string ipAddress,
        [FromQuery, Required] string userAgent,
        [FromQuery] string? deviceFingerprint = null)
    {
        try
        {
            if (userId <= 0 || string.IsNullOrWhiteSpace(ipAddress) || string.IsNullOrWhiteSpace(userAgent))
            {
                return BadRequest(ApiResponse<List<AnomalousLoginRecord>>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.DetectAnomalousLoginAsync(
                userId, ipAddress, userAgent, deviceFingerprint);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检测异常登录时发生错误");
            return StatusCode(500, ApiResponse<List<AnomalousLoginRecord>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取风险规则列表
    /// </summary>
    /// <param name="eventType">事件类型（可选）</param>
    /// <returns>风险规则列表</returns>
    [HttpGet("risk-rules")]
    [ProducesResponseType(typeof(ApiResponse<List<RiskRule>>), 200)]
    public async Task<ActionResult<ApiResponse<List<RiskRule>>>> GetRiskRules(
        [FromQuery] string? eventType = null)
    {
        try
        {
            var result = await _riskControlService.GetRiskRulesAsync(eventType);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取风险规则列表时发生错误");
            return StatusCode(500, ApiResponse<List<RiskRule>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 创建风险规则
    /// </summary>
    /// <param name="rule">风险规则</param>
    /// <returns>创建的规则</returns>
    [HttpPost("risk-rules")]
    [ProducesResponseType(typeof(ApiResponse<RiskRule>), 201)]
    [ProducesResponseType(typeof(ApiResponse<RiskRule>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<RiskRule>>> CreateRiskRule(
        [FromBody] RiskRule rule)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<RiskRule>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.CreateRiskRuleAsync(rule);
            
            if (result.Success)
            {
                return CreatedAtAction(nameof(GetRiskRules), new { eventType = rule.EventType }, result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建风险规则时发生错误");
            return StatusCode(500, ApiResponse<RiskRule>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 更新风险规则
    /// </summary>
    /// <param name="rule">风险规则</param>
    /// <returns>操作结果</returns>
    [HttpPut("risk-rules")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateRiskRule(
        [FromBody] RiskRule rule)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.UpdateRiskRuleAsync(rule);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新风险规则时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 删除风险规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("risk-rules/{ruleId}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 404)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteRiskRule(
        [FromRoute] int ruleId)
    {
        try
        {
            if (ruleId <= 0)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("规则ID无效"));
            }

            var result = await _riskControlService.DeleteRiskRuleAsync(ruleId);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return NotFound(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除风险规则时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取实时风险指标
    /// </summary>
    /// <param name="metricNames">指标名称列表</param>
    /// <param name="timeRange">时间范围（分钟）</param>
    /// <returns>风险指标列表</returns>
    [HttpGet("metrics")]
    [ProducesResponseType(typeof(ApiResponse<List<RiskMetrics>>), 200)]
    public async Task<ActionResult<ApiResponse<List<RiskMetrics>>>> GetRiskMetrics(
        [FromQuery] List<string> metricNames,
        [FromQuery] int timeRange = 60)
    {
        try
        {
            if (metricNames == null || !metricNames.Any())
            {
                metricNames = new List<string> { "active_users", "high_risk_events", "login_attempts" };
            }

            if (timeRange <= 0 || timeRange > 1440) // 最大24小时
            {
                timeRange = 60;
            }

            var result = await _riskControlService.GetRiskMetricsAsync(metricNames, timeRange);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实时风险指标时发生错误");
            return StatusCode(500, ApiResponse<List<RiskMetrics>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 锁定用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="lockDurationMinutes">锁定时长（分钟）</param>
    /// <param name="reason">锁定原因</param>
    /// <returns>操作结果</returns>
    [HttpPost("lock-user/{userId}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> LockUserAccount(
        [FromRoute] int userId,
        [FromQuery, Required] int lockDurationMinutes,
        [FromQuery, Required] string reason)
    {
        try
        {
            if (userId <= 0 || lockDurationMinutes <= 0 || string.IsNullOrWhiteSpace(reason))
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.LockUserAccountAsync(userId, lockDurationMinutes, reason);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "锁定用户账户时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 解锁用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="reason">解锁原因</param>
    /// <returns>操作结果</returns>
    [HttpPost("unlock-user/{userId}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> UnlockUserAccount(
        [FromRoute] int userId,
        [FromQuery, Required] string reason)
    {
        try
        {
            if (userId <= 0 || string.IsNullOrWhiteSpace(reason))
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _riskControlService.UnlockUserAccountAsync(userId, reason);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解锁用户账户时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取异常登录记录
    /// </summary>
    /// <param name="userId">用户ID（可选）</param>
    /// <param name="startTime">开始时间（可选）</param>
    /// <param name="endTime">结束时间（可选）</param>
    /// <returns>异常登录记录列表</returns>
    [HttpGet("anomalous-logins")]
    [ProducesResponseType(typeof(ApiResponse<List<AnomalousLoginRecord>>), 200)]
    public async Task<ActionResult<ApiResponse<List<AnomalousLoginRecord>>>> GetAnomalousLoginRecords(
        [FromQuery] int? userId = null,
        [FromQuery] DateTime? startTime = null,
        [FromQuery] DateTime? endTime = null)
    {
        try
        {
            var result = await _riskControlService.GetAnomalousLoginRecordsAsync(userId, startTime, endTime);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取异常登录记录时发生错误");
            return StatusCode(500, ApiResponse<List<AnomalousLoginRecord>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>服务健康状态</returns>
    [HttpGet("health")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Service = "RiskControlService",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0"
        });
    }
}
