version: '3.8'

services:
  # 开发环境Redis
  redis:
    image: redis:7-alpine
    container_name: persimmon-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    networks:
      - persimmon-dev-network

  # 开发环境SQL Server
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: persimmon-sqlserver-dev
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=DevPassword123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_dev_data:/var/opt/mssql
    networks:
      - persimmon-dev-network

  # API Gateway - 开发模式
  gateway:
    build:
      context: .
      dockerfile: src/Gateway/Dockerfile
    container_name: persimmon-gateway-dev
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - Logging__LogLevel__Default=Debug
      - Services__UserService__BaseUrl=http://user-service:80
      - Services__RiskControlService__BaseUrl=http://risk-control-service:80
      - Services__PricingService__BaseUrl=http://pricing-service:80
      - Services__RecommendationService__BaseUrl=http://recommendation-service:80
      - Services__SearchService__BaseUrl=http://search-service:80
      - Services__CustomerServiceBot__BaseUrl=http://customer-service-bot:80
    depends_on:
      - redis
      - user-service
    networks:
      - persimmon-dev-network
    volumes:
      - ./logs:/app/logs

  # 用户服务 - 开发模式
  user-service:
    build:
      context: .
      dockerfile: src/Services/UserService/Dockerfile
    container_name: persimmon-user-service-dev
    ports:
      - "5001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=PersimmonChic_Users_Dev;User Id=sa;Password=DevPassword123!;TrustServerCertificate=true
      - ConnectionStrings__Redis=redis:6379
      - Logging__LogLevel__Default=Debug
      - JwtSettings__SecretKey=PersimmonChic_Dev_Secret_Key_2024
    depends_on:
      - redis
      - sqlserver
    networks:
      - persimmon-dev-network
    volumes:
      - ./logs:/app/logs

  # 风险控制服务 - 开发模式
  risk-control-service:
    build:
      context: .
      dockerfile: src/Services/RiskControlService/Dockerfile
    container_name: persimmon-risk-control-service-dev
    ports:
      - "5004:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=PersimmonChic_RiskControl_Dev;User Id=sa;Password=DevPassword123!;TrustServerCertificate=true
      - ConnectionStrings__Redis=redis:6379
      - Logging__LogLevel__Default=Debug
    depends_on:
      - redis
      - sqlserver
    networks:
      - persimmon-dev-network
    volumes:
      - ./logs:/app/logs

  # 动态定价服务 - 开发模式
  pricing-service:
    build:
      context: .
      dockerfile: src/Services/PricingService/Dockerfile
    container_name: persimmon-pricing-service-dev
    ports:
      - "5005:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - Logging__LogLevel__Default=Debug
    depends_on:
      - redis
    networks:
      - persimmon-dev-network
    volumes:
      - ./logs:/app/logs

  # 推荐服务 - 开发模式
  recommendation-service:
    build:
      context: .
      dockerfile: src/Services/RecommendationService/Dockerfile
    container_name: persimmon-recommendation-service-dev
    ports:
      - "5006:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - Logging__LogLevel__Default=Debug
    depends_on:
      - redis
    networks:
      - persimmon-dev-network
    volumes:
      - ./logs:/app/logs

  # 搜索服务 - 开发模式
  search-service:
    build:
      context: .
      dockerfile: src/Services/SearchService/Dockerfile
    container_name: persimmon-search-service-dev
    ports:
      - "5007:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - Logging__LogLevel__Default=Debug
    depends_on:
      - redis
    networks:
      - persimmon-dev-network
    volumes:
      - ./logs:/app/logs

  # AI客服机器人服务 - 开发模式
  customer-service-bot:
    build:
      context: .
      dockerfile: src/Services/CustomerServiceBot/Dockerfile
    container_name: persimmon-customer-service-bot-dev
    ports:
      - "5008:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379
      - Logging__LogLevel__Default=Debug
    depends_on:
      - redis
    networks:
      - persimmon-dev-network
    volumes:
      - ./logs:/app/logs

  # Portainer - 容器管理界面
  portainer:
    image: portainer/portainer-ce:latest
    container_name: persimmon-portainer-dev
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - persimmon-dev-network
    restart: unless-stopped

  # Redis Commander - Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: persimmon-redis-commander-dev
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - persimmon-dev-network

networks:
  persimmon-dev-network:
    driver: bridge
    name: persimmon-dev-network

volumes:
  redis_dev_data:
    name: persimmon-redis-dev-data
  sqlserver_dev_data:
    name: persimmon-sqlserver-dev-data
  portainer_data:
    name: persimmon-portainer-data
