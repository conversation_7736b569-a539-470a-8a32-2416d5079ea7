using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PersimmonChic.SearchService.Models;
using PersimmonChic.SearchService.Services;
using PersimmonChic.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.SearchService.Controllers;

/// <summary>
/// 搜索服务控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SearchController : ControllerBase
{
    private readonly ISearchService _searchService;
    private readonly ISemanticSearchEngine _semanticEngine;
    private readonly ISearchAnalyticsService _analyticsService;
    private readonly ILogger<SearchController> _logger;

    public SearchController(
        ISearchService searchService,
        ISemanticSearchEngine semanticEngine,
        ISearchAnalyticsService analyticsService,
        ILogger<SearchController> logger)
    {
        _searchService = searchService;
        _semanticEngine = semanticEngine;
        _analyticsService = analyticsService;
        _logger = logger;
    }

    /// <summary>
    /// 执行搜索
    /// </summary>
    /// <param name="request">搜索请求</param>
    /// <returns>搜索响应</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(ApiResponse<SearchResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<SearchResponse>), 400)]
    public async Task<ActionResult<ApiResponse<SearchResponse>>> Search([FromBody] SearchRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<SearchResponse>.ErrorResult("请求参数无效"));
            }

            // 添加请求上下文信息
            request.Context["ip_address"] = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";
            request.Context["user_agent"] = HttpContext.Request.Headers.UserAgent.ToString();

            var result = await _searchService.SearchAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索时发生错误");
            return StatusCode(500, ApiResponse<SearchResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 自动补全
    /// </summary>
    /// <param name="request">自动补全请求</param>
    /// <returns>自动补全响应</returns>
    [HttpPost("autocomplete")]
    [ProducesResponseType(typeof(ApiResponse<AutoCompleteResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<AutoCompleteResponse>), 400)]
    public async Task<ActionResult<ApiResponse<AutoCompleteResponse>>> AutoComplete([FromBody] AutoCompleteRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<AutoCompleteResponse>.ErrorResult("请求参数无效"));
            }

            var result = await _searchService.AutoCompleteAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动补全时发生错误");
            return StatusCode(500, ApiResponse<AutoCompleteResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取搜索建议
    /// </summary>
    /// <param name="query">查询词</param>
    /// <param name="count">建议数量</param>
    /// <returns>搜索建议列表</returns>
    [HttpGet("suggestions")]
    [ProducesResponseType(typeof(ApiResponse<List<string>>), 200)]
    public async Task<ActionResult<ApiResponse<List<string>>>> GetSuggestions(
        [FromQuery, Required] string query,
        [FromQuery] int count = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(ApiResponse<List<string>>.ErrorResult("查询词不能为空"));
            }

            if (count <= 0 || count > 50)
            {
                return BadRequest(ApiResponse<List<string>>.ErrorResult("建议数量必须在1-50之间"));
            }

            var result = await _searchService.GetSuggestionsAsync(query, count);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索建议时发生错误");
            return StatusCode(500, ApiResponse<List<string>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取热门搜索词
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="timeRange">时间范围（小时）</param>
    /// <returns>热门搜索词列表</returns>
    [HttpGet("trending")]
    [ProducesResponseType(typeof(ApiResponse<List<string>>), 200)]
    public async Task<ActionResult<ApiResponse<List<string>>>> GetTrendingQueries(
        [FromQuery] int count = 10,
        [FromQuery] int timeRange = 24)
    {
        try
        {
            if (count <= 0 || count > 100)
            {
                return BadRequest(ApiResponse<List<string>>.ErrorResult("数量必须在1-100之间"));
            }

            if (timeRange <= 0 || timeRange > 168) // 最多7天
            {
                return BadRequest(ApiResponse<List<string>>.ErrorResult("时间范围必须在1-168小时之间"));
            }

            var result = await _searchService.GetTrendingQueriesAsync(count, timeRange);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门搜索词时发生错误");
            return StatusCode(500, ApiResponse<List<string>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 语义搜索
    /// </summary>
    /// <param name="query">查询文本</param>
    /// <param name="topK">返回结果数量</param>
    /// <param name="threshold">相似度阈值</param>
    /// <returns>搜索结果</returns>
    [HttpGet("semantic")]
    [ProducesResponseType(typeof(ApiResponse<List<SearchResult>>), 200)]
    [ProducesResponseType(typeof(ApiResponse<List<SearchResult>>), 400)]
    public async Task<ActionResult<ApiResponse<List<SearchResult>>>> SemanticSearch(
        [FromQuery, Required] string query,
        [FromQuery] int topK = 20,
        [FromQuery] float threshold = 0.7f)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(ApiResponse<List<SearchResult>>.ErrorResult("查询文本不能为空"));
            }

            if (topK <= 0 || topK > 100)
            {
                return BadRequest(ApiResponse<List<SearchResult>>.ErrorResult("结果数量必须在1-100之间"));
            }

            if (threshold < 0 || threshold > 1)
            {
                return BadRequest(ApiResponse<List<SearchResult>>.ErrorResult("相似度阈值必须在0-1之间"));
            }

            var result = await _semanticEngine.SemanticSearchAsync(query, topK, threshold);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "语义搜索时发生错误");
            return StatusCode(500, ApiResponse<List<SearchResult>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 生成文本嵌入向量
    /// </summary>
    /// <param name="text">文本内容</param>
    /// <returns>向量表示</returns>
    [HttpPost("embedding")]
    [ProducesResponseType(typeof(ApiResponse<List<float>>), 200)]
    [ProducesResponseType(typeof(ApiResponse<List<float>>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<List<float>>>> GenerateEmbedding([FromBody] string text)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return BadRequest(ApiResponse<List<float>>.ErrorResult("文本内容不能为空"));
            }

            if (text.Length > 10000)
            {
                return BadRequest(ApiResponse<List<float>>.ErrorResult("文本长度不能超过10000字符"));
            }

            var result = await _semanticEngine.GenerateEmbeddingAsync(text);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成文本嵌入向量时发生错误");
            return StatusCode(500, ApiResponse<List<float>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 批量索引文档
    /// </summary>
    /// <param name="documents">文档列表</param>
    /// <returns>索引结果</returns>
    [HttpPost("index")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> IndexDocuments([FromBody] List<SearchDocument> documents)
    {
        try
        {
            if (!ModelState.IsValid || !documents.Any())
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            if (documents.Count > 1000)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("单次索引文档数量不能超过1000"));
            }

            var result = await _semanticEngine.IndexDocumentsAsync(documents);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量索引文档时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取搜索分析数据
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>搜索分析数据</returns>
    [HttpGet("analytics")]
    [ProducesResponseType(typeof(ApiResponse<Dictionary<string, object>>), 200)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, object>>>> GetSearchAnalytics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var result = await _searchService.GetSearchAnalyticsAsync(startDate, endDate);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索分析数据时发生错误");
            return StatusCode(500, ApiResponse<Dictionary<string, object>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取搜索统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>搜索统计数据</returns>
    [HttpGet("stats")]
    [ProducesResponseType(typeof(ApiResponse<Dictionary<string, object>>), 200)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, object>>>> GetSearchStats(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var result = await _analyticsService.GetSearchStatsAsync(startDate, endDate);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索统计时发生错误");
            return StatusCode(500, ApiResponse<Dictionary<string, object>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>服务健康状态</returns>
    [HttpGet("health")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Service = "SearchService",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Features = new[]
            {
                "语义搜索",
                "关键词搜索",
                "混合搜索",
                "模糊搜索",
                "精确匹配",
                "自动补全",
                "搜索建议",
                "热门搜索",
                "搜索分析",
                "文档索引管理"
            }
        });
    }
}
