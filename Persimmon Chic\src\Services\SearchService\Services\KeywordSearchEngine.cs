using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Distributed;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.SearchService.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.SearchService.Services;

/// <summary>
/// 关键词搜索引擎实现
/// </summary>
public class KeywordSearchEngine : IKeywordSearchEngine
{
    private readonly ILogger<KeywordSearchEngine> _logger;
    private readonly IRepository<SearchDocument> _documentRepository;

    public KeywordSearchEngine(
        ILogger<KeywordSearchEngine> logger,
        IRepository<SearchDocument> documentRepository)
    {
        _logger = logger;
        _documentRepository = documentRepository;
    }

    public async Task<ApiResponse<List<SearchResult>>> KeywordSearchAsync(
        string query, 
        Dictionary<string, object> filters, 
        SortBy sortBy = SortBy.Relevance, 
        int page = 1, 
        int pageSize = 20)
    {
        try
        {
            var documents = await _documentRepository.GetAllAsync();
            var activeDocuments = documents.Where(d => d.IsActive).ToList();

            // 关键词匹配
            var queryWords = query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var results = new List<(SearchDocument Document, float Score)>();

            foreach (var document in activeDocuments)
            {
                var score = CalculateKeywordScore(document, queryWords);
                if (score > 0)
                {
                    results.Add((document, score));
                }
            }

            // 应用过滤器
            results = ApplyFilters(results, filters);

            // 排序
            results = ApplySorting(results, sortBy);

            // 转换为搜索结果
            var searchResults = results.Select(r => new SearchResult
            {
                Id = r.Document.Id,
                Title = r.Document.Title,
                Description = r.Document.Description,
                Category = r.Document.Category,
                Brand = r.Document.Brand,
                Price = r.Document.Price,
                Score = r.Score,
                Relevance = r.Score,
                Tags = r.Document.Tags,
                Attributes = r.Document.Attributes,
                LastUpdated = r.Document.UpdatedAt
            }).ToList();

            return ApiResponse<List<SearchResult>>.SuccessResult(searchResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "关键词搜索时发生错误");
            return ApiResponse<List<SearchResult>>.ErrorResult($"关键词搜索失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<SearchResult>>> FuzzySearchAsync(string query, int fuzziness = 2, int topK = 20)
    {
        // 简化的模糊搜索实现
        return await KeywordSearchAsync(query, new Dictionary<string, object>(), SortBy.Relevance, 1, topK);
    }

    public async Task<ApiResponse<List<SearchResult>>> ExactMatchSearchAsync(string query, string? field = null, int topK = 20)
    {
        try
        {
            var documents = await _documentRepository.GetAllAsync();
            var results = documents
                .Where(d => d.IsActive && (
                    d.Title.Equals(query, StringComparison.OrdinalIgnoreCase) ||
                    d.Description.Contains(query, StringComparison.OrdinalIgnoreCase)))
                .Take(topK)
                .Select(d => new SearchResult
                {
                    Id = d.Id,
                    Title = d.Title,
                    Description = d.Description,
                    Category = d.Category,
                    Brand = d.Brand,
                    Price = d.Price,
                    Score = 1.0f,
                    Relevance = 1.0f,
                    Tags = d.Tags,
                    Attributes = d.Attributes,
                    LastUpdated = d.UpdatedAt
                })
                .ToList();

            return ApiResponse<List<SearchResult>>.SuccessResult(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "精确匹配搜索时发生错误");
            return ApiResponse<List<SearchResult>>.ErrorResult($"精确匹配搜索失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> BuildIndexAsync(List<SearchDocument> documents)
    {
        try
        {
            foreach (var document in documents)
            {
                await _documentRepository.AddAsync(document);
            }
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建搜索索引时发生错误");
            return ApiResponse<bool>.ErrorResult($"构建索引失败: {ex.Message}");
        }
    }

    private float CalculateKeywordScore(SearchDocument document, string[] queryWords)
    {
        var score = 0f;
        var titleText = document.Title.ToLower();
        var descriptionText = document.Description.ToLower();
        var contentText = document.Content.ToLower();

        foreach (var word in queryWords)
        {
            // 标题匹配权重最高
            if (titleText.Contains(word))
                score += 3.0f;
            
            // 描述匹配权重中等
            if (descriptionText.Contains(word))
                score += 2.0f;
            
            // 内容匹配权重较低
            if (contentText.Contains(word))
                score += 1.0f;
            
            // 标签匹配
            if (document.Tags.Any(tag => tag.ToLower().Contains(word)))
                score += 1.5f;
        }

        return score;
    }

    private List<(SearchDocument Document, float Score)> ApplyFilters(
        List<(SearchDocument Document, float Score)> results, 
        Dictionary<string, object> filters)
    {
        // 简化的过滤器实现
        return results;
    }

    private List<(SearchDocument Document, float Score)> ApplySorting(
        List<(SearchDocument Document, float Score)> results, 
        SortBy sortBy)
    {
        return sortBy switch
        {
            SortBy.Relevance => results.OrderByDescending(r => r.Score).ToList(),
            SortBy.Price => results.OrderBy(r => r.Document.Price).ToList(),
            SortBy.Date => results.OrderByDescending(r => r.Document.UpdatedAt).ToList(),
            SortBy.Name => results.OrderBy(r => r.Document.Title).ToList(),
            _ => results.OrderByDescending(r => r.Score).ToList()
        };
    }
}

/// <summary>
/// 混合搜索引擎实现
/// </summary>
public class HybridSearchEngine : IHybridSearchEngine
{
    private readonly ILogger<HybridSearchEngine> _logger;
    private readonly ISemanticSearchEngine _semanticEngine;
    private readonly IKeywordSearchEngine _keywordEngine;

    public HybridSearchEngine(
        ILogger<HybridSearchEngine> logger,
        ISemanticSearchEngine semanticEngine,
        IKeywordSearchEngine keywordEngine)
    {
        _logger = logger;
        _semanticEngine = semanticEngine;
        _keywordEngine = keywordEngine;
    }

    public async Task<ApiResponse<List<SearchResult>>> HybridSearchAsync(
        string query, 
        float semanticWeight = 0.6f, 
        float keywordWeight = 0.4f, 
        int topK = 20)
    {
        try
        {
            // 并行执行语义搜索和关键词搜索
            var semanticTask = _semanticEngine.SemanticSearchAsync(query, topK * 2);
            var keywordTask = _keywordEngine.KeywordSearchAsync(query, new Dictionary<string, object>(), SortBy.Relevance, 1, topK * 2);

            await Task.WhenAll(semanticTask, keywordTask);

            var semanticResults = semanticTask.Result.Success ? semanticTask.Result.Data : new List<SearchResult>();
            var keywordResults = keywordTask.Result.Success ? keywordTask.Result.Data : new List<SearchResult>();

            // 合并和重新评分
            var combinedResults = CombineResults(semanticResults, keywordResults, semanticWeight, keywordWeight);

            // 取前K个结果
            var topResults = combinedResults.OrderByDescending(r => r.Score).Take(topK).ToList();

            return ApiResponse<List<SearchResult>>.SuccessResult(topResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "混合搜索时发生错误");
            return ApiResponse<List<SearchResult>>.ErrorResult($"混合搜索失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<SearchResult>>> ReRankResultsAsync(
        List<SearchResult> results, 
        string query, 
        Dictionary<string, object> context)
    {
        try
        {
            // 简化的重排序实现
            var rerankedResults = results.OrderByDescending(r => r.Score * r.Relevance).ToList();
            return ApiResponse<List<SearchResult>>.SuccessResult(rerankedResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重排序搜索结果时发生错误");
            return ApiResponse<List<SearchResult>>.ErrorResult($"重排序失败: {ex.Message}");
        }
    }

    private List<SearchResult> CombineResults(
        List<SearchResult> semanticResults, 
        List<SearchResult> keywordResults, 
        float semanticWeight, 
        float keywordWeight)
    {
        var combinedResults = new Dictionary<int, SearchResult>();

        // 添加语义搜索结果
        foreach (var result in semanticResults)
        {
            result.Score *= semanticWeight;
            combinedResults[result.Id] = result;
        }

        // 合并关键词搜索结果
        foreach (var result in keywordResults)
        {
            if (combinedResults.ContainsKey(result.Id))
            {
                // 合并分数
                combinedResults[result.Id].Score += result.Score * keywordWeight;
            }
            else
            {
                result.Score *= keywordWeight;
                combinedResults[result.Id] = result;
            }
        }

        return combinedResults.Values.ToList();
    }
}

/// <summary>
/// 搜索缓存服务实现
/// </summary>
public class SearchCacheService : ISearchCacheService
{
    private readonly ILogger<SearchCacheService> _logger;
    private readonly IDistributedCache _cache;

    public SearchCacheService(
        ILogger<SearchCacheService> logger,
        IDistributedCache cache)
    {
        _logger = logger;
        _cache = cache;
    }

    public async Task<SearchResponse?> GetCachedSearchResultAsync(string cacheKey)
    {
        try
        {
            var cachedValue = await _cache.GetStringAsync($"search:{cacheKey}");
            if (!string.IsNullOrEmpty(cachedValue))
            {
                return System.Text.Json.JsonSerializer.Deserialize<SearchResponse>(cachedValue);
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存搜索结果时发生错误");
            return null;
        }
    }

    public async Task<bool> SetSearchResultCacheAsync(string cacheKey, SearchResponse searchResponse, TimeSpan? expiration = null)
    {
        try
        {
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = expiration ?? TimeSpan.FromMinutes(15)
            };

            var serializedResponse = System.Text.Json.JsonSerializer.Serialize(searchResponse);
            await _cache.SetStringAsync($"search:{cacheKey}", serializedResponse, options);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置搜索结果缓存时发生错误");
            return false;
        }
    }

    public async Task<AutoCompleteResponse?> GetCachedAutoCompleteAsync(string query)
    {
        try
        {
            var cachedValue = await _cache.GetStringAsync($"autocomplete:{query.ToLower()}");
            if (!string.IsNullOrEmpty(cachedValue))
            {
                return System.Text.Json.JsonSerializer.Deserialize<AutoCompleteResponse>(cachedValue);
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存自动补全结果时发生错误");
            return null;
        }
    }

    public async Task<bool> SetAutoCompleteCacheAsync(string query, AutoCompleteResponse response, TimeSpan? expiration = null)
    {
        try
        {
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = expiration ?? TimeSpan.FromMinutes(30)
            };

            var serializedResponse = System.Text.Json.JsonSerializer.Serialize(response);
            await _cache.SetStringAsync($"autocomplete:{query.ToLower()}", serializedResponse, options);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置自动补全缓存时发生错误");
            return false;
        }
    }

    public async Task<bool> ClearSearchCacheAsync(string? pattern = null)
    {
        // 简化实现，实际应该支持模式匹配删除
        return true;
    }

    public string GenerateSearchCacheKey(SearchRequest request)
    {
        var keyParts = new List<string>
        {
            request.Query.ToLower(),
            request.SearchType.ToString(),
            request.Page.ToString(),
            request.PageSize.ToString()
        };

        if (request.Categories.Any())
        {
            keyParts.Add($"cat:{string.Join(",", request.Categories.OrderBy(c => c))}");
        }

        if (request.Brands.Any())
        {
            keyParts.Add($"brand:{string.Join(",", request.Brands.OrderBy(b => b))}");
        }

        if (request.MinPrice.HasValue)
        {
            keyParts.Add($"minprice:{request.MinPrice.Value}");
        }

        if (request.MaxPrice.HasValue)
        {
            keyParts.Add($"maxprice:{request.MaxPrice.Value}");
        }

        var cacheKey = string.Join(":", keyParts);
        return cacheKey.Length > 250 ? cacheKey.GetHashCode().ToString("X") : cacheKey;
    }
}

/// <summary>
/// 搜索分析服务实现
/// </summary>
public class SearchAnalyticsService : ISearchAnalyticsService
{
    private readonly ILogger<SearchAnalyticsService> _logger;

    public SearchAnalyticsService(ILogger<SearchAnalyticsService> logger)
    {
        _logger = logger;
    }

    public async Task<ApiResponse<bool>> RecordSearchEventAsync(string eventType, string query, string? userId = null, Dictionary<string, object>? metadata = null)
    {
        try
        {
            _logger.LogInformation("记录搜索事件: {EventType}, 查询: {Query}, 用户: {UserId}", eventType, query, userId);
            // 这里应该记录到分析系统
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录搜索事件时发生错误");
            return ApiResponse<bool>.ErrorResult($"记录搜索事件失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<Dictionary<string, object>>> GetSearchStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            // 模拟搜索统计数据
            var stats = new Dictionary<string, object>
            {
                ["total_searches"] = 12345,
                ["unique_queries"] = 8901,
                ["average_response_time"] = 156.7,
                ["success_rate"] = 0.923
            };

            return ApiResponse<Dictionary<string, object>>.SuccessResult(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索统计时发生错误");
            return ApiResponse<Dictionary<string, object>>.ErrorResult($"获取搜索统计失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<Dictionary<string, object>>> GetQueryPerformanceAsync(string? query = null)
    {
        try
        {
            var performance = new Dictionary<string, object>
            {
                ["average_response_time"] = 145.2,
                ["cache_hit_rate"] = 0.78,
                ["result_count"] = 234
            };

            return ApiResponse<Dictionary<string, object>>.SuccessResult(performance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取查询性能时发生错误");
            return ApiResponse<Dictionary<string, object>>.ErrorResult($"获取查询性能失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<Dictionary<string, object>>> GetUserSearchBehaviorAsync(string userId, int days = 30)
    {
        try
        {
            var behavior = new Dictionary<string, object>
            {
                ["total_searches"] = 45,
                ["unique_queries"] = 32,
                ["top_categories"] = new[] { "电子产品", "数码配件", "智能设备" },
                ["search_frequency"] = 1.5
            };

            return ApiResponse<Dictionary<string, object>>.SuccessResult(behavior);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户搜索行为时发生错误");
            return ApiResponse<Dictionary<string, object>>.ErrorResult($"获取用户搜索行为失败: {ex.Message}");
        }
    }
}
