using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.RiskControlService.Hubs;
using PersimmonChic.RiskControlService.Models;
using PersimmonChic.RiskControlService.Services;
using PersimmonChic.Shared.Common;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllers();

// 配置Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Persimmon Chic Risk Control Service",
        Version = "v1",
        Description = "风控服务API - 提供实时行为分析和异常登录检测功能",
        Contact = new OpenApiContact
        {
            Name = "Persimmon Chic Team",
            Email = "<EMAIL>"
        }
    });

    // 添加JWT认证配置
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // 包含XML注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// 配置JWT认证
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
            ClockSkew = TimeSpan.Zero
        };

        // 配置SignalR的JWT认证
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                var accessToken = context.Request.Query["access_token"];
                var path = context.HttpContext.Request.Path;
                
                if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/hubs"))
                {
                    context.Token = accessToken;
                }
                
                return Task.CompletedTask;
            }
        };
    });

builder.Services.AddAuthorization();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5000", "http://localhost:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// 配置SignalR
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
});

// 配置Redis缓存
var redisConnectionString = builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379";
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConnectionString;
    options.InstanceName = "PersimmonChic.RiskControl";
});

// 注册数据访问层
builder.Services.AddScoped<IRepository<UserBehaviorEvent>, InMemoryRepository<UserBehaviorEvent>>();
builder.Services.AddScoped<IRepository<RiskAssessmentResult>, InMemoryRepository<RiskAssessmentResult>>();
builder.Services.AddScoped<IRepository<DeviceFingerprint>, InMemoryRepository<DeviceFingerprint>>();
builder.Services.AddScoped<IRepository<AnomalousLoginRecord>, InMemoryRepository<AnomalousLoginRecord>>();
builder.Services.AddScoped<IRepository<RiskRule>, InMemoryRepository<RiskRule>>();
builder.Services.AddScoped<IRepository<UserRiskProfile>, InMemoryRepository<UserRiskProfile>>();

// 注册业务服务
builder.Services.AddScoped<IRiskControlService, PersimmonChic.RiskControlService.Services.RiskControlService>();
builder.Services.AddScoped<IMLRiskAssessmentService, MLRiskAssessmentService>();
builder.Services.AddScoped<IDeviceFingerprintService, DeviceFingerprintService>();

// 注册后台服务
builder.Services.AddSingleton<IRealTimeRiskMonitorService, RealTimeRiskMonitorService>();
builder.Services.AddHostedService<RealTimeRiskMonitorService>();

// 注册HTTP客户端工厂
builder.Services.AddHttpClient();

// 配置日志
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    logging.AddDebug();
    
    if (builder.Environment.IsDevelopment())
    {
        logging.SetMinimumLevel(LogLevel.Debug);
    }
    else
    {
        logging.SetMinimumLevel(LogLevel.Information);
    }
});

// 配置健康检查
builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Risk Control Service V1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}

app.UseHttpsRedirection();

app.UseCors();

app.UseAuthentication();
app.UseAuthorization();

// 添加请求日志中间件
app.Use(async (context, next) =>
{
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    var requestId = Guid.NewGuid().ToString("N")[..8];
    
    using (logger.BeginScope(new Dictionary<string, object> { ["RequestId"] = requestId }))
    {
        logger.LogInformation("开始处理请求: {Method} {Path}", 
            context.Request.Method, context.Request.Path);
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            await next();
        }
        finally
        {
            stopwatch.Stop();
            logger.LogInformation("请求处理完成: {Method} {Path} - {StatusCode} - {ElapsedMs}ms",
                context.Request.Method, context.Request.Path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
        }
    }
});

app.MapControllers();

// 配置SignalR Hub
app.MapHub<RiskMonitorHub>("/hubs/risk-monitor");

// 配置健康检查端点
app.MapHealthChecks("/health");

// 添加服务信息端点
app.MapGet("/info", () => new
{
    Service = "PersimmonChic.RiskControlService",
    Version = "1.0.0",
    Environment = app.Environment.EnvironmentName,
    StartTime = DateTime.UtcNow,
    Features = new[]
    {
        "实时行为分析",
        "异常登录检测",
        "机器学习风险评估",
        "设备指纹识别",
        "实时监控告警",
        "SignalR实时通信"
    }
});

// 初始化默认数据
await InitializeDefaultDataAsync(app.Services);

app.Run();

/// <summary>
/// 初始化默认数据
/// </summary>
static async Task InitializeDefaultDataAsync(IServiceProvider services)
{
    using var scope = services.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    
    try
    {
        logger.LogInformation("开始初始化默认风控数据...");
        
        // 初始化默认风险规则
        var riskRuleRepository = scope.ServiceProvider.GetRequiredService<IRepository<RiskRule>>();
        
        var defaultRules = new List<RiskRule>
        {
            new RiskRule
            {
                Id = 1,
                Name = "高频登录检测",
                Description = "检测短时间内多次登录尝试",
                EventType = "login",
                Condition = "high_frequency",
                RiskWeight = 30,
                IsEnabled = true,
                Priority = 1,
                CreatedBy = "System"
            },
            new RiskRule
            {
                Id = 2,
                Name = "新IP地址检测",
                Description = "检测来自新IP地址的登录",
                EventType = "login",
                Condition = "new_ip",
                RiskWeight = 20,
                IsEnabled = true,
                Priority = 2,
                CreatedBy = "System"
            },
            new RiskRule
            {
                Id = 3,
                Name = "失败登录次数检测",
                Description = "检测连续失败登录尝试",
                EventType = "login",
                Condition = "failed_attempts",
                RiskWeight = 40,
                IsEnabled = true,
                Priority = 3,
                CreatedBy = "System"
            }
        };

        foreach (var rule in defaultRules)
        {
            await riskRuleRepository.AddAsync(rule);
        }
        
        logger.LogInformation("默认风控数据初始化完成");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "初始化默认数据时发生错误");
    }
}
