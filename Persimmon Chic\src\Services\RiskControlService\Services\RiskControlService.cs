using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.RiskControlService.Models;
using PersimmonChic.Shared.Models;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;

namespace PersimmonChic.RiskControlService.Services;

/// <summary>
/// 风控服务实现
/// </summary>
public class RiskControlService : IRiskControlService
{
    private readonly ILogger<RiskControlService> _logger;
    private readonly IRepository<UserBehaviorEvent> _behaviorEventRepository;
    private readonly IRepository<RiskAssessmentResult> _riskAssessmentRepository;
    private readonly IRepository<DeviceFingerprint> _deviceFingerprintRepository;
    private readonly IRepository<AnomalousLoginRecord> _anomalousLoginRepository;
    private readonly IRepository<RiskRule> _riskRuleRepository;
    private readonly IRepository<UserRiskProfile> _userRiskProfileRepository;
    private readonly IDistributedCache _cache;
    private readonly IMLRiskAssessmentService _mlService;
    private readonly IRealTimeRiskMonitorService _monitorService;

    public RiskControlService(
        ILogger<RiskControlService> logger,
        IRepository<UserBehaviorEvent> behaviorEventRepository,
        IRepository<RiskAssessmentResult> riskAssessmentRepository,
        IRepository<DeviceFingerprint> deviceFingerprintRepository,
        IRepository<AnomalousLoginRecord> anomalousLoginRepository,
        IRepository<RiskRule> riskRuleRepository,
        IRepository<UserRiskProfile> userRiskProfileRepository,
        IDistributedCache cache,
        IMLRiskAssessmentService mlService,
        IRealTimeRiskMonitorService monitorService)
    {
        _logger = logger;
        _behaviorEventRepository = behaviorEventRepository;
        _riskAssessmentRepository = riskAssessmentRepository;
        _deviceFingerprintRepository = deviceFingerprintRepository;
        _anomalousLoginRepository = anomalousLoginRepository;
        _riskRuleRepository = riskRuleRepository;
        _userRiskProfileRepository = userRiskProfileRepository;
        _cache = cache;
        _mlService = mlService;
        _monitorService = monitorService;
    }

    public async Task<ApiResponse<RiskDecisionResponse>> EvaluateRiskAsync(RiskDecisionRequest request)
    {
        try
        {
            _logger.LogInformation("开始评估用户 {UserId} 的风险决策", request.UserId);

            // 1. 获取用户风险档案
            var profileResponse = await GetUserRiskProfileAsync(request.UserId);
            var userProfile = profileResponse.Data ?? new UserRiskProfile { UserId = request.UserId };

            // 2. 创建行为事件记录
            var behaviorEvent = new UserBehaviorEvent
            {
                UserId = request.UserId,
                EventType = request.EventType,
                IpAddress = request.IpAddress,
                UserAgent = request.UserAgent,
                DeviceFingerprint = request.DeviceFingerprint,
                Location = request.Location,
                Metadata = request.Context
            };

            // 3. 记录行为事件
            await RecordBehaviorEventAsync(behaviorEvent);

            // 4. 执行风险评估
            var riskScore = await CalculateRiskScoreAsync(behaviorEvent, userProfile);
            var riskLevel = DetermineRiskLevel(riskScore);
            var riskFactors = await IdentifyRiskFactorsAsync(behaviorEvent, userProfile);

            // 5. 确定风控动作
            var action = DetermineRiskAction(riskLevel, riskFactors);

            // 6. 检测异常登录（如果是登录事件）
            if (request.EventType.Equals("login", StringComparison.OrdinalIgnoreCase))
            {
                await DetectAnomalousLoginAsync(request.UserId, request.IpAddress, request.UserAgent, request.DeviceFingerprint);
            }

            // 7. 更新用户风险档案
            await UpdateUserRiskProfileAfterEventAsync(userProfile, behaviorEvent, riskScore);

            // 8. 创建风险评估结果
            var assessmentResult = new RiskAssessmentResult
            {
                UserId = request.UserId,
                EventType = request.EventType,
                RiskScore = riskScore,
                RiskLevel = riskLevel,
                RiskFactors = riskFactors,
                RecommendedAction = action.ToString(),
                IsBlocked = action == RiskAction.Block
            };

            await _riskAssessmentRepository.AddAsync(assessmentResult);

            // 9. 实时监控处理
            await _monitorService.ProcessRealTimeEventAsync(behaviorEvent);

            // 10. 构建响应
            var response = new RiskDecisionResponse
            {
                IsAllowed = action != RiskAction.Block,
                Action = action,
                RiskScore = riskScore,
                RiskLevel = riskLevel,
                RiskFactors = riskFactors,
                Message = GetRiskMessage(action, riskLevel),
                ChallengeType = action == RiskAction.Challenge ? "SMS" : null,
                Metadata = new Dictionary<string, object>
                {
                    ["assessmentId"] = assessmentResult.Id,
                    ["timestamp"] = DateTime.UtcNow
                }
            };

            _logger.LogInformation("用户 {UserId} 风险评估完成，风险分数: {RiskScore}, 动作: {Action}", 
                request.UserId, riskScore, action);

            return ApiResponse<RiskDecisionResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评估用户 {UserId} 风险决策时发生错误", request.UserId);
            return ApiResponse<RiskDecisionResponse>.ErrorResult($"风险评估失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> RecordBehaviorEventAsync(UserBehaviorEvent behaviorEvent)
    {
        try
        {
            await _behaviorEventRepository.AddAsync(behaviorEvent);
            
            // 缓存最近的行为事件用于实时分析
            var cacheKey = $"user_behavior:{behaviorEvent.UserId}";
            var recentEvents = await GetRecentBehaviorEventsFromCacheAsync(behaviorEvent.UserId);
            recentEvents.Add(behaviorEvent);
            
            // 只保留最近100个事件
            if (recentEvents.Count > 100)
            {
                recentEvents = recentEvents.TakeLast(100).ToList();
            }
            
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(recentEvents), 
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(24) });

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录用户行为事件时发生错误");
            return ApiResponse<bool>.ErrorResult($"记录行为事件失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserRiskProfile>> GetUserRiskProfileAsync(int userId)
    {
        try
        {
            // 先从缓存获取
            var cacheKey = $"user_risk_profile:{userId}";
            var cachedProfile = await _cache.GetStringAsync(cacheKey);
            
            if (!string.IsNullOrEmpty(cachedProfile))
            {
                var profile = JsonSerializer.Deserialize<UserRiskProfile>(cachedProfile);
                return ApiResponse<UserRiskProfile>.SuccessResult(profile);
            }

            // 从数据库获取
            var profiles = await _userRiskProfileRepository.FindAsync(p => p.UserId == userId);
            var userProfile = profiles.FirstOrDefault();

            if (userProfile != null)
            {
                // 缓存结果
                await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(userProfile),
                    new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(30) });
            }

            return ApiResponse<UserRiskProfile>.SuccessResult(userProfile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户风险档案时发生错误");
            return ApiResponse<UserRiskProfile>.ErrorResult($"获取用户风险档案失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateUserRiskProfileAsync(UserRiskProfile profile)
    {
        try
        {
            profile.UpdatedAt = DateTime.UtcNow;
            
            var existingProfiles = await _userRiskProfileRepository.FindAsync(p => p.UserId == profile.UserId);
            var existingProfile = existingProfiles.FirstOrDefault();

            if (existingProfile != null)
            {
                // 更新现有档案
                existingProfile.BaseRiskScore = profile.BaseRiskScore;
                existingProfile.CurrentRiskLevel = profile.CurrentRiskLevel;
                existingProfile.TrustedDevices = profile.TrustedDevices;
                existingProfile.TrustedIPs = profile.TrustedIPs;
                existingProfile.TrustedLocations = profile.TrustedLocations;
                existingProfile.LastLoginTime = profile.LastLoginTime;
                existingProfile.LastLoginIP = profile.LastLoginIP;
                existingProfile.LastLoginLocation = profile.LastLoginLocation;
                existingProfile.FailedLoginAttempts = profile.FailedLoginAttempts;
                existingProfile.LastFailedLoginTime = profile.LastFailedLoginTime;
                existingProfile.IsLocked = profile.IsLocked;
                existingProfile.LockedUntil = profile.LockedUntil;
                existingProfile.UpdatedAt = profile.UpdatedAt;

                await _userRiskProfileRepository.UpdateAsync(existingProfile);
            }
            else
            {
                // 创建新档案
                await _userRiskProfileRepository.AddAsync(profile);
            }

            // 更新缓存
            var cacheKey = $"user_risk_profile:{profile.UserId}";
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(profile),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(30) });

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户风险档案时发生错误");
            return ApiResponse<bool>.ErrorResult($"更新用户风险档案失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private async Task<double> CalculateRiskScoreAsync(UserBehaviorEvent behaviorEvent, UserRiskProfile userProfile)
    {
        double riskScore = userProfile.BaseRiskScore;

        // 1. 使用机器学习模型预测
        var mlScoreResponse = await _mlService.PredictRiskScoreAsync(behaviorEvent);
        if (mlScoreResponse.Success && mlScoreResponse.Data > 0)
        {
            riskScore += mlScoreResponse.Data * 0.4; // ML模型权重40%
        }

        // 2. 基于规则的评分
        var rules = await GetActiveRiskRulesAsync(behaviorEvent.EventType);
        foreach (var rule in rules)
        {
            if (EvaluateRuleCondition(rule, behaviorEvent, userProfile))
            {
                riskScore += rule.RiskWeight * 0.3; // 规则权重30%
            }
        }

        // 3. 历史行为分析
        var recentEvents = await GetRecentBehaviorEventsFromCacheAsync(behaviorEvent.UserId);
        riskScore += AnalyzeHistoricalBehavior(recentEvents, behaviorEvent) * 0.3; // 历史行为权重30%

        return Math.Min(100, Math.Max(0, riskScore));
    }

    private RiskLevel DetermineRiskLevel(double riskScore)
    {
        return riskScore switch
        {
            >= 80 => RiskLevel.Critical,
            >= 60 => RiskLevel.High,
            >= 30 => RiskLevel.Medium,
            _ => RiskLevel.Low
        };
    }

    private RiskAction DetermineRiskAction(RiskLevel riskLevel, List<string> riskFactors)
    {
        return riskLevel switch
        {
            RiskLevel.Critical => RiskAction.Block,
            RiskLevel.High => riskFactors.Contains("SuspiciousIP") ? RiskAction.Block : RiskAction.Challenge,
            RiskLevel.Medium => RiskAction.Challenge,
            _ => RiskAction.Allow
        };
    }

    private async Task<List<string>> IdentifyRiskFactorsAsync(UserBehaviorEvent behaviorEvent, UserRiskProfile userProfile)
    {
        var riskFactors = new List<string>();

        // 检查可疑IP
        if (await IsSuspiciousIPAsync(behaviorEvent.IpAddress))
        {
            riskFactors.Add("SuspiciousIP");
        }

        // 检查异常位置
        if (!string.IsNullOrEmpty(behaviorEvent.Location) && 
            !userProfile.TrustedLocations.Contains(behaviorEvent.Location))
        {
            riskFactors.Add("UnusualLocation");
        }

        // 检查新设备
        if (!string.IsNullOrEmpty(behaviorEvent.DeviceFingerprint) && 
            !userProfile.TrustedDevices.Contains(behaviorEvent.DeviceFingerprint))
        {
            riskFactors.Add("NewDevice");
        }

        // 检查异常时间
        if (IsUnusualTime(behaviorEvent.Timestamp))
        {
            riskFactors.Add("UnusualTime");
        }

        return riskFactors;
    }

    private async Task<List<RiskRule>> GetActiveRiskRulesAsync(string eventType)
    {
        var cacheKey = $"risk_rules:{eventType}";
        var cachedRules = await _cache.GetStringAsync(cacheKey);
        
        if (!string.IsNullOrEmpty(cachedRules))
        {
            return JsonSerializer.Deserialize<List<RiskRule>>(cachedRules) ?? new List<RiskRule>();
        }

        var rules = await _riskRuleRepository.FindAsync(r => r.EventType == eventType && r.IsEnabled);
        var rulesList = rules.OrderBy(r => r.Priority).ToList();

        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(rulesList),
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(15) });

        return rulesList;
    }

    private async Task<List<UserBehaviorEvent>> GetRecentBehaviorEventsFromCacheAsync(int userId)
    {
        var cacheKey = $"user_behavior:{userId}";
        var cachedEvents = await _cache.GetStringAsync(cacheKey);
        
        if (!string.IsNullOrEmpty(cachedEvents))
        {
            return JsonSerializer.Deserialize<List<UserBehaviorEvent>>(cachedEvents) ?? new List<UserBehaviorEvent>();
        }

        return new List<UserBehaviorEvent>();
    }

    private bool EvaluateRuleCondition(RiskRule rule, UserBehaviorEvent behaviorEvent, UserRiskProfile userProfile)
    {
        // 简化的规则评估逻辑，实际应该使用规则引擎
        return rule.Condition.ToLower() switch
        {
            "high_frequency" => CheckHighFrequency(behaviorEvent.UserId),
            "new_ip" => !userProfile.TrustedIPs.Contains(behaviorEvent.IpAddress),
            "failed_attempts" => userProfile.FailedLoginAttempts > 3,
            _ => false
        };
    }

    private double AnalyzeHistoricalBehavior(List<UserBehaviorEvent> recentEvents, UserBehaviorEvent currentEvent)
    {
        if (!recentEvents.Any()) return 0;

        double riskScore = 0;

        // 检查频率异常
        var recentSimilarEvents = recentEvents
            .Where(e => e.EventType == currentEvent.EventType && 
                       e.Timestamp > DateTime.UtcNow.AddMinutes(-10))
            .Count();

        if (recentSimilarEvents > 5)
        {
            riskScore += 20; // 高频操作风险
        }

        return riskScore;
    }

    private async Task<bool> IsSuspiciousIPAsync(string ipAddress)
    {
        // 这里应该集成IP威胁情报服务
        // 简化实现：检查是否在黑名单中
        var blacklistKey = "ip_blacklist";
        var blacklist = await _cache.GetStringAsync(blacklistKey);
        
        if (!string.IsNullOrEmpty(blacklist))
        {
            var blacklistIPs = JsonSerializer.Deserialize<List<string>>(blacklist) ?? new List<string>();
            return blacklistIPs.Contains(ipAddress);
        }

        return false;
    }

    private bool IsUnusualTime(DateTime timestamp)
    {
        var hour = timestamp.Hour;
        // 凌晨2-6点认为是异常时间
        return hour >= 2 && hour <= 6;
    }

    private bool CheckHighFrequency(int userId)
    {
        // 简化实现：检查最近5分钟内的事件数量
        // 实际应该从缓存或数据库查询
        return false;
    }

    private async Task UpdateUserRiskProfileAfterEventAsync(UserRiskProfile profile, UserBehaviorEvent behaviorEvent, double riskScore)
    {
        profile.BaseRiskScore = (profile.BaseRiskScore * 0.9) + (riskScore * 0.1); // 指数移动平均
        profile.CurrentRiskLevel = DetermineRiskLevel(profile.BaseRiskScore);
        
        if (behaviorEvent.EventType.Equals("login", StringComparison.OrdinalIgnoreCase))
        {
            profile.LastLoginTime = behaviorEvent.Timestamp;
            profile.LastLoginIP = behaviorEvent.IpAddress;
            profile.LastLoginLocation = behaviorEvent.Location;
        }

        await UpdateUserRiskProfileAsync(profile);
    }

    private string GetRiskMessage(RiskAction action, RiskLevel riskLevel)
    {
        return action switch
        {
            RiskAction.Block => "由于检测到高风险行为，此操作已被阻止",
            RiskAction.Challenge => "检测到中等风险，需要额外验证",
            RiskAction.Monitor => "此操作将被监控",
            _ => "操作已允许"
        };
    }

    // 其他接口方法的实现...
    public async Task<ApiResponse<List<AnomalousLoginRecord>>> DetectAnomalousLoginAsync(int userId, string ipAddress, string userAgent, string? deviceFingerprint = null)
    {
        // 实现异常登录检测逻辑
        return ApiResponse<List<AnomalousLoginRecord>>.SuccessResult(new List<AnomalousLoginRecord>());
    }

    public async Task<ApiResponse<DeviceFingerprint?>> GetDeviceFingerprintAsync(string fingerprintHash)
    {
        // 实现设备指纹获取逻辑
        return ApiResponse<DeviceFingerprint?>.SuccessResult(null);
    }

    public async Task<ApiResponse<bool>> UpdateDeviceFingerprintAsync(DeviceFingerprint fingerprint)
    {
        // 实现设备指纹更新逻辑
        return ApiResponse<bool>.SuccessResult(true);
    }

    public async Task<ApiResponse<List<RiskRule>>> GetRiskRulesAsync(string? eventType = null)
    {
        // 实现风险规则获取逻辑
        return ApiResponse<List<RiskRule>>.SuccessResult(new List<RiskRule>());
    }

    public async Task<ApiResponse<RiskRule>> CreateRiskRuleAsync(RiskRule rule)
    {
        // 实现风险规则创建逻辑
        return ApiResponse<RiskRule>.SuccessResult(rule);
    }

    public async Task<ApiResponse<bool>> UpdateRiskRuleAsync(RiskRule rule)
    {
        // 实现风险规则更新逻辑
        return ApiResponse<bool>.SuccessResult(true);
    }

    public async Task<ApiResponse<bool>> DeleteRiskRuleAsync(int ruleId)
    {
        // 实现风险规则删除逻辑
        return ApiResponse<bool>.SuccessResult(true);
    }

    public async Task<ApiResponse<List<RiskMetrics>>> GetRiskMetricsAsync(List<string> metricNames, int timeRange = 60)
    {
        // 实现风险指标获取逻辑
        return ApiResponse<List<RiskMetrics>>.SuccessResult(new List<RiskMetrics>());
    }

    public async Task<ApiResponse<bool>> LockUserAccountAsync(int userId, int lockDurationMinutes, string reason)
    {
        // 实现用户账户锁定逻辑
        return ApiResponse<bool>.SuccessResult(true);
    }

    public async Task<ApiResponse<bool>> UnlockUserAccountAsync(int userId, string reason)
    {
        // 实现用户账户解锁逻辑
        return ApiResponse<bool>.SuccessResult(true);
    }

    public async Task<ApiResponse<List<AnomalousLoginRecord>>> GetAnomalousLoginRecordsAsync(int? userId = null, DateTime? startTime = null, DateTime? endTime = null)
    {
        try
        {
            // 使用FindAsync方法替代GetQueryable
            var allRecords = await _anomalousLoginRepository.GetAllAsync();
            var query = allRecords.AsQueryable();

            if (userId.HasValue)
            {
                query = query.Where(r => r.UserId == userId.Value);
            }

            if (startTime.HasValue)
            {
                query = query.Where(r => r.DetectedAt >= startTime.Value);
            }

            if (endTime.HasValue)
            {
                query = query.Where(r => r.DetectedAt <= endTime.Value);
            }

            var records = query.OrderByDescending(r => r.DetectedAt).Take(100).ToList();
            return ApiResponse<List<AnomalousLoginRecord>>.SuccessResult(records);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取异常登录记录时发生错误");
            return ApiResponse<List<AnomalousLoginRecord>>.ErrorResult($"获取异常登录记录失败: {ex.Message}");
        }
    }
}
