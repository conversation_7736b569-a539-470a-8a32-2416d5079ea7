using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using PersimmonChic.PricingService.Models;
using PersimmonChic.Shared.Models;
using System.Text.Json;

namespace PersimmonChic.PricingService.Services;

/// <summary>
/// 价格通知服务实现
/// </summary>
public class PriceNotificationService : IPriceNotificationService
{
    private readonly ILogger<PriceNotificationService> _logger;
    private readonly IDistributedCache _cache;
    private readonly IHttpClientFactory _httpClientFactory;
    private const string SUBSCRIPTION_CACHE_PREFIX = "price_subscription:";
    private const string NOTIFICATION_CACHE_PREFIX = "price_notification:";

    public PriceNotificationService(
        ILogger<PriceNotificationService> logger,
        IDistributedCache cache,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _cache = cache;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<ApiResponse<bool>> SendPriceChangeNotificationAsync(int productId, decimal oldPrice, decimal newPrice, string reason)
    {
        try
        {
            _logger.LogInformation("发送价格变更通知: 商品 {ProductId}, 旧价格 {OldPrice}, 新价格 {NewPrice}, 原因 {Reason}", 
                productId, oldPrice, newPrice, reason);

            // 获取订阅者列表
            var subscribers = await GetProductSubscribersAsync(productId);
            
            if (!subscribers.Any())
            {
                _logger.LogDebug("商品 {ProductId} 没有订阅者", productId);
                return ApiResponse<bool>.SuccessResult(true);
            }

            // 创建通知消息
            var notification = new PriceChangeNotification
            {
                ProductId = productId,
                OldPrice = oldPrice,
                NewPrice = newPrice,
                ChangePercentage = CalculateChangePercentage(oldPrice, newPrice),
                Reason = reason,
                Timestamp = DateTime.UtcNow
            };

            // 发送通知给所有订阅者
            var notificationTasks = subscribers.Select(subscriber => 
                SendNotificationToSubscriberAsync(subscriber, notification));

            await Task.WhenAll(notificationTasks);

            // 记录通知历史
            await RecordNotificationHistoryAsync(notification, subscribers.Count);

            _logger.LogInformation("价格变更通知发送完成: 商品 {ProductId}, 通知 {Count} 个订阅者", 
                productId, subscribers.Count);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送价格变更通知时发生错误");
            return ApiResponse<bool>.ErrorResult($"发送价格变更通知失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> SubscribePriceChangeNotificationAsync(int productId, string subscriberId, string notificationMethod)
    {
        try
        {
            _logger.LogInformation("订阅价格变更通知: 商品 {ProductId}, 订阅者 {SubscriberId}, 方式 {Method}", 
                productId, subscriberId, notificationMethod);

            var subscription = new PriceSubscription
            {
                ProductId = productId,
                SubscriberId = subscriberId,
                NotificationMethod = notificationMethod,
                SubscribedAt = DateTime.UtcNow,
                IsActive = true
            };

            // 保存订阅信息到缓存
            var subscriptionKey = $"{SUBSCRIPTION_CACHE_PREFIX}{productId}:{subscriberId}";
            await _cache.SetStringAsync(subscriptionKey, JsonSerializer.Serialize(subscription),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromDays(30) });

            // 更新商品订阅者列表
            await AddToProductSubscribersListAsync(productId, subscription);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅价格变更通知时发生错误");
            return ApiResponse<bool>.ErrorResult($"订阅价格变更通知失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UnsubscribePriceChangeNotificationAsync(int productId, string subscriberId)
    {
        try
        {
            _logger.LogInformation("取消订阅价格变更通知: 商品 {ProductId}, 订阅者 {SubscriberId}", 
                productId, subscriberId);

            // 删除订阅信息
            var subscriptionKey = $"{SUBSCRIPTION_CACHE_PREFIX}{productId}:{subscriberId}";
            await _cache.RemoveAsync(subscriptionKey);

            // 从商品订阅者列表中移除
            await RemoveFromProductSubscribersListAsync(productId, subscriberId);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅价格变更通知时发生错误");
            return ApiResponse<bool>.ErrorResult($"取消订阅价格变更通知失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private async Task<List<PriceSubscription>> GetProductSubscribersAsync(int productId)
    {
        try
        {
            var subscribersKey = $"product_subscribers:{productId}";
            var subscribersJson = await _cache.GetStringAsync(subscribersKey);
            
            if (!string.IsNullOrEmpty(subscribersJson))
            {
                var subscribers = JsonSerializer.Deserialize<List<PriceSubscription>>(subscribersJson);
                return subscribers?.Where(s => s.IsActive).ToList() ?? new List<PriceSubscription>();
            }

            return new List<PriceSubscription>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品订阅者列表时发生错误");
            return new List<PriceSubscription>();
        }
    }

    private async Task SendNotificationToSubscriberAsync(PriceSubscription subscription, PriceChangeNotification notification)
    {
        try
        {
            switch (subscription.NotificationMethod.ToLower())
            {
                case "webhook":
                    await SendWebhookNotificationAsync(subscription, notification);
                    break;
                case "email":
                    await SendEmailNotificationAsync(subscription, notification);
                    break;
                case "sms":
                    await SendSmsNotificationAsync(subscription, notification);
                    break;
                default:
                    _logger.LogWarning("不支持的通知方式: {Method}", subscription.NotificationMethod);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送通知给订阅者时发生错误: {SubscriberId}", subscription.SubscriberId);
        }
    }

    private async Task SendWebhookNotificationAsync(PriceSubscription subscription, PriceChangeNotification notification)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);

            var payload = new
            {
                event_type = "price_change",
                product_id = notification.ProductId,
                old_price = notification.OldPrice,
                new_price = notification.NewPrice,
                change_percentage = notification.ChangePercentage,
                reason = notification.Reason,
                timestamp = notification.Timestamp,
                subscriber_id = subscription.SubscriberId
            };

            var jsonContent = JsonSerializer.Serialize(payload);
            var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

            // 这里应该使用订阅者提供的webhook URL
            // 为了演示，使用一个模拟的URL
            var webhookUrl = $"https://api.example.com/webhooks/price-change/{subscription.SubscriberId}";
            
            var response = await httpClient.PostAsync(webhookUrl, content);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogDebug("Webhook通知发送成功: {SubscriberId}", subscription.SubscriberId);
            }
            else
            {
                _logger.LogWarning("Webhook通知发送失败: {SubscriberId}, 状态码: {StatusCode}", 
                    subscription.SubscriberId, response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送Webhook通知时发生错误: {SubscriberId}", subscription.SubscriberId);
        }
    }

    private async Task SendEmailNotificationAsync(PriceSubscription subscription, PriceChangeNotification notification)
    {
        try
        {
            // 这里应该集成实际的邮件服务
            // 为了演示，只记录日志
            _logger.LogInformation("发送邮件通知: 订阅者 {SubscriberId}, 商品 {ProductId}, 价格变更 {OldPrice} -> {NewPrice}", 
                subscription.SubscriberId, notification.ProductId, notification.OldPrice, notification.NewPrice);

            await Task.Delay(100); // 模拟邮件发送延迟
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送邮件通知时发生错误: {SubscriberId}", subscription.SubscriberId);
        }
    }

    private async Task SendSmsNotificationAsync(PriceSubscription subscription, PriceChangeNotification notification)
    {
        try
        {
            // 这里应该集成实际的短信服务
            // 为了演示，只记录日志
            _logger.LogInformation("发送短信通知: 订阅者 {SubscriberId}, 商品 {ProductId}, 价格变更 {OldPrice} -> {NewPrice}", 
                subscription.SubscriberId, notification.ProductId, notification.OldPrice, notification.NewPrice);

            await Task.Delay(50); // 模拟短信发送延迟
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送短信通知时发生错误: {SubscriberId}", subscription.SubscriberId);
        }
    }

    private decimal CalculateChangePercentage(decimal oldPrice, decimal newPrice)
    {
        if (oldPrice == 0) return 0;
        return Math.Round(((newPrice - oldPrice) / oldPrice) * 100, 2);
    }

    private async Task AddToProductSubscribersListAsync(int productId, PriceSubscription subscription)
    {
        try
        {
            var subscribersKey = $"product_subscribers:{productId}";
            var existingSubscribers = await GetProductSubscribersAsync(productId);
            
            // 移除现有的相同订阅者（如果存在）
            existingSubscribers.RemoveAll(s => s.SubscriberId == subscription.SubscriberId);
            
            // 添加新订阅
            existingSubscribers.Add(subscription);
            
            // 保存更新后的列表
            await _cache.SetStringAsync(subscribersKey, JsonSerializer.Serialize(existingSubscribers),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromDays(30) });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加到商品订阅者列表时发生错误");
        }
    }

    private async Task RemoveFromProductSubscribersListAsync(int productId, string subscriberId)
    {
        try
        {
            var subscribersKey = $"product_subscribers:{productId}";
            var existingSubscribers = await GetProductSubscribersAsync(productId);
            
            // 移除指定订阅者
            existingSubscribers.RemoveAll(s => s.SubscriberId == subscriberId);
            
            // 保存更新后的列表
            await _cache.SetStringAsync(subscribersKey, JsonSerializer.Serialize(existingSubscribers),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromDays(30) });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从商品订阅者列表中移除时发生错误");
        }
    }

    private async Task RecordNotificationHistoryAsync(PriceChangeNotification notification, int subscriberCount)
    {
        try
        {
            var historyKey = $"{NOTIFICATION_CACHE_PREFIX}{notification.ProductId}:{DateTime.UtcNow:yyyyMMddHHmmss}";
            var historyRecord = new
            {
                notification,
                subscriber_count = subscriberCount,
                sent_at = DateTime.UtcNow
            };

            await _cache.SetStringAsync(historyKey, JsonSerializer.Serialize(historyRecord),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromDays(7) });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录通知历史时发生错误");
        }
    }
}

/// <summary>
/// 价格变更通知
/// </summary>
public class PriceChangeNotification
{
    public int ProductId { get; set; }
    public decimal OldPrice { get; set; }
    public decimal NewPrice { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Reason { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 价格订阅
/// </summary>
public class PriceSubscription
{
    public int ProductId { get; set; }
    public string SubscriberId { get; set; } = string.Empty;
    public string NotificationMethod { get; set; } = string.Empty;
    public DateTime SubscribedAt { get; set; }
    public bool IsActive { get; set; }
}
