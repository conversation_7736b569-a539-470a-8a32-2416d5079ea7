using Microsoft.Extensions.Logging;
using PersimmonChic.RecommendationService.Models;
using PersimmonChic.Shared.Models;
using PersimmonChic.Infrastructure.DataAccess;
using MathNet.Numerics.LinearAlgebra;
using MathNet.Numerics.Statistics;

namespace PersimmonChic.RecommendationService.Services;

/// <summary>
/// 协同过滤推荐引擎实现
/// </summary>
public class CollaborativeFilteringEngine : ICollaborativeFilteringEngine
{
    private readonly ILogger<CollaborativeFilteringEngine> _logger;
    private readonly IRepository<UserBehavior> _behaviorRepository;
    private readonly IRepository<ProductFeature> _productRepository;
    
    // 缓存用户-物品评分矩阵
    private Dictionary<int, Dictionary<int, float>>? _userItemMatrix;
    private Dictionary<int, Dictionary<int, float>>? _itemUserMatrix;
    private DateTime _lastMatrixUpdate = DateTime.MinValue;
    private readonly TimeSpan _matrixCacheExpiry = TimeSpan.FromMinutes(30);
    private readonly object _matrixLock = new();

    public CollaborativeFilteringEngine(
        ILogger<CollaborativeFilteringEngine> logger,
        IRepository<UserBehavior> behaviorRepository,
        IRepository<ProductFeature> productRepository)
    {
        _logger = logger;
        _behaviorRepository = behaviorRepository;
        _productRepository = productRepository;
    }

    public async Task<ApiResponse<List<RecommendationItem>>> GetUserBasedRecommendationsAsync(int userId, int count = 10)
    {
        try
        {
            _logger.LogInformation("开始基于用户的协同过滤推荐，用户: {UserId}, 数量: {Count}", userId, count);

            // 确保评分矩阵是最新的
            await EnsureMatrixUpdatedAsync();

            if (_userItemMatrix == null || !_userItemMatrix.ContainsKey(userId))
            {
                _logger.LogWarning("用户 {UserId} 没有足够的行为数据，返回热门推荐", userId);
                return await GetPopularItemsAsync(count);
            }

            // 找到相似用户
            var similarUsers = await FindSimilarUsersAsync(userId, 20);
            
            if (!similarUsers.Any())
            {
                _logger.LogWarning("未找到与用户 {UserId} 相似的用户", userId);
                return await GetPopularItemsAsync(count);
            }

            // 基于相似用户生成推荐
            var recommendations = await GenerateUserBasedRecommendationsAsync(userId, similarUsers, count);

            _logger.LogInformation("为用户 {UserId} 生成了 {Count} 个基于用户的协同过滤推荐", userId, recommendations.Count);
            return ApiResponse<List<RecommendationItem>>.SuccessResult(recommendations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "基于用户的协同过滤推荐时发生错误");
            return ApiResponse<List<RecommendationItem>>.ErrorResult($"推荐生成失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<RecommendationItem>>> GetItemBasedRecommendationsAsync(int userId, int count = 10)
    {
        try
        {
            _logger.LogInformation("开始基于物品的协同过滤推荐，用户: {UserId}, 数量: {Count}", userId, count);

            // 确保评分矩阵是最新的
            await EnsureMatrixUpdatedAsync();

            if (_userItemMatrix == null || !_userItemMatrix.ContainsKey(userId))
            {
                _logger.LogWarning("用户 {UserId} 没有足够的行为数据，返回热门推荐", userId);
                return await GetPopularItemsAsync(count);
            }

            // 获取用户已评分的物品
            var userRatedItems = _userItemMatrix[userId].Keys.ToList();
            
            if (!userRatedItems.Any())
            {
                return await GetPopularItemsAsync(count);
            }

            // 基于物品相似度生成推荐
            var recommendations = await GenerateItemBasedRecommendationsAsync(userId, userRatedItems, count);

            _logger.LogInformation("为用户 {UserId} 生成了 {Count} 个基于物品的协同过滤推荐", userId, recommendations.Count);
            return ApiResponse<List<RecommendationItem>>.SuccessResult(recommendations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "基于物品的协同过滤推荐时发生错误");
            return ApiResponse<List<RecommendationItem>>.ErrorResult($"推荐生成失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<float>> CalculateUserSimilarityAsync(int userId1, int userId2)
    {
        try
        {
            await EnsureMatrixUpdatedAsync();

            if (_userItemMatrix == null || 
                !_userItemMatrix.ContainsKey(userId1) || 
                !_userItemMatrix.ContainsKey(userId2))
            {
                return ApiResponse<float>.SuccessResult(0f);
            }

            var user1Ratings = _userItemMatrix[userId1];
            var user2Ratings = _userItemMatrix[userId2];

            // 找到共同评分的物品
            var commonItems = user1Ratings.Keys.Intersect(user2Ratings.Keys).ToList();
            
            if (commonItems.Count < 2)
            {
                return ApiResponse<float>.SuccessResult(0f);
            }

            // 计算皮尔逊相关系数
            var user1CommonRatings = commonItems.Select(item => (double)user1Ratings[item]).ToArray();
            var user2CommonRatings = commonItems.Select(item => (double)user2Ratings[item]).ToArray();

            var correlation = Correlation.Pearson(user1CommonRatings, user2CommonRatings);
            
            // 处理NaN情况
            if (double.IsNaN(correlation))
            {
                correlation = 0;
            }

            var similarity = (float)Math.Max(0, correlation); // 只考虑正相关

            _logger.LogDebug("用户相似度计算: 用户{UserId1} vs 用户{UserId2} = {Similarity} (共同物品: {CommonCount})", 
                userId1, userId2, similarity, commonItems.Count);

            return ApiResponse<float>.SuccessResult(similarity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算用户相似度时发生错误");
            return ApiResponse<float>.SuccessResult(0f);
        }
    }

    public async Task<ApiResponse<float>> CalculateItemSimilarityAsync(int productId1, int productId2)
    {
        try
        {
            await EnsureMatrixUpdatedAsync();

            if (_itemUserMatrix == null || 
                !_itemUserMatrix.ContainsKey(productId1) || 
                !_itemUserMatrix.ContainsKey(productId2))
            {
                return ApiResponse<float>.SuccessResult(0f);
            }

            var item1Ratings = _itemUserMatrix[productId1];
            var item2Ratings = _itemUserMatrix[productId2];

            // 找到共同评分的用户
            var commonUsers = item1Ratings.Keys.Intersect(item2Ratings.Keys).ToList();
            
            if (commonUsers.Count < 2)
            {
                return ApiResponse<float>.SuccessResult(0f);
            }

            // 计算余弦相似度
            var item1CommonRatings = commonUsers.Select(user => (double)item1Ratings[user]).ToArray();
            var item2CommonRatings = commonUsers.Select(user => (double)item2Ratings[user]).ToArray();

            var similarity = CalculateCosineSimilarity(item1CommonRatings, item2CommonRatings);

            _logger.LogDebug("物品相似度计算: 物品{ProductId1} vs 物品{ProductId2} = {Similarity} (共同用户: {CommonCount})", 
                productId1, productId2, similarity, commonUsers.Count);

            return ApiResponse<float>.SuccessResult(similarity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算物品相似度时发生错误");
            return ApiResponse<float>.SuccessResult(0f);
        }
    }

    // 私有辅助方法
    private async Task EnsureMatrixUpdatedAsync()
    {
        lock (_matrixLock)
        {
            if (_userItemMatrix != null && DateTime.UtcNow - _lastMatrixUpdate < _matrixCacheExpiry)
            {
                return; // 缓存仍然有效
            }
        }

        await BuildRatingMatrixAsync();
    }

    private async Task BuildRatingMatrixAsync()
    {
        try
        {
            _logger.LogInformation("开始构建用户-物品评分矩阵");

            // 获取所有用户行为数据
            var behaviors = await _behaviorRepository.GetAllAsync();
            
            var userItemMatrix = new Dictionary<int, Dictionary<int, float>>();
            var itemUserMatrix = new Dictionary<int, Dictionary<int, float>>();

            foreach (var behavior in behaviors)
            {
                var rating = ConvertBehaviorToRating(behavior);
                
                // 构建用户-物品矩阵
                if (!userItemMatrix.ContainsKey(behavior.UserId))
                {
                    userItemMatrix[behavior.UserId] = new Dictionary<int, float>();
                }
                
                // 如果同一用户对同一物品有多个行为，取最高评分
                if (!userItemMatrix[behavior.UserId].ContainsKey(behavior.ProductId) ||
                    userItemMatrix[behavior.UserId][behavior.ProductId] < rating)
                {
                    userItemMatrix[behavior.UserId][behavior.ProductId] = rating;
                }

                // 构建物品-用户矩阵
                if (!itemUserMatrix.ContainsKey(behavior.ProductId))
                {
                    itemUserMatrix[behavior.ProductId] = new Dictionary<int, float>();
                }
                
                if (!itemUserMatrix[behavior.ProductId].ContainsKey(behavior.UserId) ||
                    itemUserMatrix[behavior.ProductId][behavior.UserId] < rating)
                {
                    itemUserMatrix[behavior.ProductId][behavior.UserId] = rating;
                }
            }

            lock (_matrixLock)
            {
                _userItemMatrix = userItemMatrix;
                _itemUserMatrix = itemUserMatrix;
                _lastMatrixUpdate = DateTime.UtcNow;
            }

            _logger.LogInformation("评分矩阵构建完成，用户数: {UserCount}, 物品数: {ItemCount}, 评分数: {RatingCount}",
                userItemMatrix.Count, itemUserMatrix.Count, behaviors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建评分矩阵时发生错误");
        }
    }

    private float ConvertBehaviorToRating(UserBehavior behavior)
    {
        // 将不同类型的行为转换为评分
        return behavior.BehaviorType switch
        {
            BehaviorType.Purchase => 5.0f,
            BehaviorType.AddToCart => 4.0f,
            BehaviorType.Like => 4.0f,
            BehaviorType.Share => 3.5f,
            BehaviorType.Click => 3.0f,
            BehaviorType.View => Math.Min(3.0f, behavior.Duration / 60f + 1f), // 基于浏览时长
            BehaviorType.Review => behavior.Rating > 0 ? behavior.Rating : 3.0f,
            BehaviorType.Search => 2.0f,
            _ => 1.0f
        };
    }

    private async Task<List<(int UserId, float Similarity)>> FindSimilarUsersAsync(int userId, int count)
    {
        var similarities = new List<(int UserId, float Similarity)>();

        if (_userItemMatrix == null || !_userItemMatrix.ContainsKey(userId))
        {
            return similarities;
        }

        var targetUserRatings = _userItemMatrix[userId];

        foreach (var otherUserId in _userItemMatrix.Keys)
        {
            if (otherUserId == userId) continue;

            var similarityResponse = await CalculateUserSimilarityAsync(userId, otherUserId);
            if (similarityResponse.Success && similarityResponse.Data > 0.1f) // 最小相似度阈值
            {
                similarities.Add((otherUserId, similarityResponse.Data));
            }
        }

        return similarities.OrderByDescending(x => x.Similarity).Take(count).ToList();
    }

    private async Task<List<RecommendationItem>> GenerateUserBasedRecommendationsAsync(
        int userId, List<(int UserId, float Similarity)> similarUsers, int count)
    {
        var candidateItems = new Dictionary<int, float>();
        var userRatedItems = _userItemMatrix![userId].Keys.ToHashSet();

        // 收集相似用户喜欢但目标用户未评分的物品
        foreach (var (similarUserId, similarity) in similarUsers)
        {
            var similarUserRatings = _userItemMatrix[similarUserId];
            
            foreach (var (productId, rating) in similarUserRatings)
            {
                if (userRatedItems.Contains(productId)) continue; // 跳过已评分物品

                if (!candidateItems.ContainsKey(productId))
                {
                    candidateItems[productId] = 0f;
                }

                // 加权评分
                candidateItems[productId] += rating * similarity;
            }
        }

        // 按评分排序并构建推荐项目
        var recommendations = new List<RecommendationItem>();
        var topItems = candidateItems.OrderByDescending(x => x.Value).Take(count);

        int rank = 1;
        foreach (var (productId, score) in topItems)
        {
            recommendations.Add(new RecommendationItem
            {
                ProductId = productId,
                ProductName = $"Product {productId}",
                Score = score,
                Confidence = Math.Min(1f, score / 5f),
                Reason = "基于相似用户偏好的推荐",
                Rank = rank++,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "UserBasedCollaborativeFiltering",
                    ["similar_users_count"] = similarUsers.Count,
                    ["weighted_score"] = score
                }
            });
        }

        return recommendations;
    }

    private async Task<List<RecommendationItem>> GenerateItemBasedRecommendationsAsync(
        int userId, List<int> userRatedItems, int count)
    {
        var candidateItems = new Dictionary<int, float>();
        var userRatings = _userItemMatrix![userId];

        // 为用户评分的每个物品找相似物品
        foreach (var ratedItemId in userRatedItems)
        {
            var userRatingForItem = userRatings[ratedItemId];

            // 找到与该物品相似的其他物品
            if (_itemUserMatrix!.ContainsKey(ratedItemId))
            {
                foreach (var candidateItemId in _itemUserMatrix.Keys)
                {
                    if (candidateItemId == ratedItemId || userRatedItems.Contains(candidateItemId))
                        continue;

                    var similarityResponse = await CalculateItemSimilarityAsync(ratedItemId, candidateItemId);
                    if (similarityResponse.Success && similarityResponse.Data > 0.1f)
                    {
                        if (!candidateItems.ContainsKey(candidateItemId))
                        {
                            candidateItems[candidateItemId] = 0f;
                        }

                        // 基于物品相似度和用户对原物品的评分计算推荐分数
                        candidateItems[candidateItemId] += userRatingForItem * similarityResponse.Data;
                    }
                }
            }
        }

        // 构建推荐项目
        var recommendations = new List<RecommendationItem>();
        var topItems = candidateItems.OrderByDescending(x => x.Value).Take(count);

        int rank = 1;
        foreach (var (productId, score) in topItems)
        {
            recommendations.Add(new RecommendationItem
            {
                ProductId = productId,
                ProductName = $"Product {productId}",
                Score = score,
                Confidence = Math.Min(1f, score / 5f),
                Reason = "基于物品相似度的推荐",
                Rank = rank++,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "ItemBasedCollaborativeFiltering",
                    ["based_on_items"] = userRatedItems.Count,
                    ["weighted_score"] = score
                }
            });
        }

        return recommendations;
    }

    private async Task<ApiResponse<List<RecommendationItem>>> GetPopularItemsAsync(int count)
    {
        try
        {
            // 获取热门物品作为后备推荐
            var products = await _productRepository.GetAllAsync();
            var popularItems = products
                .OrderByDescending(p => p.PopularityScore)
                .Take(count)
                .Select((p, index) => new RecommendationItem
                {
                    ProductId = p.ProductId,
                    ProductName = p.Name,
                    Score = p.PopularityScore,
                    Confidence = 0.7f,
                    Reason = "热门商品推荐",
                    Rank = index + 1,
                    Metadata = new Dictionary<string, object>
                    {
                        ["algorithm"] = "PopularItems",
                        ["popularity_score"] = p.PopularityScore
                    }
                })
                .ToList();

            return ApiResponse<List<RecommendationItem>>.SuccessResult(popularItems);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门物品时发生错误");
            return ApiResponse<List<RecommendationItem>>.ErrorResult($"获取热门推荐失败: {ex.Message}");
        }
    }

    private float CalculateCosineSimilarity(double[] vector1, double[] vector2)
    {
        if (vector1.Length != vector2.Length)
            return 0f;

        var dotProduct = vector1.Zip(vector2, (a, b) => a * b).Sum();
        var magnitude1 = Math.Sqrt(vector1.Sum(x => x * x));
        var magnitude2 = Math.Sqrt(vector2.Sum(x => x * x));

        if (magnitude1 == 0 || magnitude2 == 0)
            return 0f;

        return (float)(dotProduct / (magnitude1 * magnitude2));
    }
}
