using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace PersimmonChic.Shared.Common;

/// <summary>
/// 服务注册表 - 简单的服务发现实现
/// </summary>
public class ServiceRegistry
{
    private readonly ConcurrentDictionary<string, List<ServiceInstance>> _services = new();
    private readonly ILogger<ServiceRegistry> _logger;

    public ServiceRegistry(ILogger<ServiceRegistry> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 注册服务实例
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="instance">服务实例</param>
    public void RegisterService(string serviceName, ServiceInstance instance)
    {
        _services.AddOrUpdate(serviceName,
            new List<ServiceInstance> { instance },
            (key, existingList) =>
            {
                existingList.Add(instance);
                return existingList;
            });

        _logger.LogInformation("Service {ServiceName} registered at {Address}:{Port}", 
            serviceName, instance.Address, instance.Port);
    }

    /// <summary>
    /// 注销服务实例
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="instanceId">实例ID</param>
    public void UnregisterService(string serviceName, string instanceId)
    {
        if (_services.TryGetValue(serviceName, out var instances))
        {
            instances.RemoveAll(i => i.Id == instanceId);
            if (instances.Count == 0)
            {
                _services.TryRemove(serviceName, out _);
            }
        }

        _logger.LogInformation("Service {ServiceName} instance {InstanceId} unregistered", 
            serviceName, instanceId);
    }

    /// <summary>
    /// 获取服务实例
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <returns>服务实例</returns>
    public ServiceInstance? GetService(string serviceName)
    {
        if (!_services.TryGetValue(serviceName, out var instances) || instances.Count == 0)
        {
            return null;
        }

        // 简单的轮询负载均衡
        var healthyInstances = instances.Where(i => i.IsHealthy).ToList();
        if (healthyInstances.Count == 0)
        {
            return null;
        }

        var index = Random.Shared.Next(healthyInstances.Count);
        return healthyInstances[index];
    }

    /// <summary>
    /// 获取所有服务实例
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <returns>服务实例列表</returns>
    public List<ServiceInstance> GetAllServices(string serviceName)
    {
        return _services.TryGetValue(serviceName, out var instances) 
            ? instances.ToList() 
            : new List<ServiceInstance>();
    }

    /// <summary>
    /// 获取所有注册的服务名称
    /// </summary>
    /// <returns>服务名称列表</returns>
    public List<string> GetServiceNames()
    {
        return _services.Keys.ToList();
    }

    /// <summary>
    /// 更新服务实例健康状态
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="instanceId">实例ID</param>
    /// <param name="isHealthy">是否健康</param>
    public void UpdateServiceHealth(string serviceName, string instanceId, bool isHealthy)
    {
        if (_services.TryGetValue(serviceName, out var instances))
        {
            var instance = instances.FirstOrDefault(i => i.Id == instanceId);
            if (instance != null)
            {
                instance.IsHealthy = isHealthy;
                instance.LastHeartbeat = DateTime.UtcNow;
                
                _logger.LogDebug("Service {ServiceName} instance {InstanceId} health updated to {IsHealthy}", 
                    serviceName, instanceId, isHealthy);
            }
        }
    }

    /// <summary>
    /// 清理不健康的服务实例
    /// </summary>
    /// <param name="timeoutMinutes">超时分钟数</param>
    public void CleanupUnhealthyServices(int timeoutMinutes = 5)
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-timeoutMinutes);
        var servicesToRemove = new List<(string serviceName, string instanceId)>();

        foreach (var kvp in _services)
        {
            var serviceName = kvp.Key;
            var instances = kvp.Value;

            foreach (var instance in instances.ToList())
            {
                if (instance.LastHeartbeat < cutoffTime)
                {
                    servicesToRemove.Add((serviceName, instance.Id));
                }
            }
        }

        foreach (var (serviceName, instanceId) in servicesToRemove)
        {
            UnregisterService(serviceName, instanceId);
        }

        if (servicesToRemove.Count > 0)
        {
            _logger.LogInformation("Cleaned up {Count} unhealthy service instances", servicesToRemove.Count);
        }
    }
}

/// <summary>
/// 服务实例信息
/// </summary>
public class ServiceInstance
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Address { get; set; } = string.Empty;
    public int Port { get; set; }
    public string Version { get; set; } = "1.0.0";
    public bool IsHealthy { get; set; } = true;
    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;
    public DateTime LastHeartbeat { get; set; } = DateTime.UtcNow;
    public Dictionary<string, string> Metadata { get; set; } = new();

    public string BaseUrl => $"http://{Address}:{Port}";
}

/// <summary>
/// 负载均衡器
/// </summary>
public class LoadBalancer
{
    private readonly ServiceRegistry _serviceRegistry;
    private readonly ILogger<LoadBalancer> _logger;

    public LoadBalancer(ServiceRegistry serviceRegistry, ILogger<LoadBalancer> logger)
    {
        _serviceRegistry = serviceRegistry;
        _logger = logger;
    }

    /// <summary>
    /// 选择服务实例（轮询算法）
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <returns>服务实例</returns>
    public ServiceInstance? SelectInstance(string serviceName)
    {
        return _serviceRegistry.GetService(serviceName);
    }

    /// <summary>
    /// 选择服务实例（加权轮询算法）
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <returns>服务实例</returns>
    public ServiceInstance? SelectInstanceWeighted(string serviceName)
    {
        var instances = _serviceRegistry.GetAllServices(serviceName)
            .Where(i => i.IsHealthy)
            .ToList();

        if (instances.Count == 0)
        {
            return null;
        }

        // 简单实现：根据注册时间给予权重（越新权重越高）
        var weights = instances.Select(i => 
        {
            var age = DateTime.UtcNow - i.RegisteredAt;
            return Math.Max(1, 10 - (int)age.TotalHours);
        }).ToList();

        var totalWeight = weights.Sum();
        var random = Random.Shared.Next(totalWeight);
        
        var currentWeight = 0;
        for (int i = 0; i < instances.Count; i++)
        {
            currentWeight += weights[i];
            if (random < currentWeight)
            {
                return instances[i];
            }
        }

        return instances.Last();
    }
}
