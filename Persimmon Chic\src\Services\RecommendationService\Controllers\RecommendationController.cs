using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PersimmonChic.RecommendationService.Models;
using PersimmonChic.RecommendationService.Services;
using PersimmonChic.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.RecommendationService.Controllers;

/// <summary>
/// 推荐服务控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RecommendationController : ControllerBase
{
    private readonly IRecommendationService _recommendationService;
    private readonly IMLRecommendationEngine _mlEngine;
    private readonly IABTestService _abTestService;
    private readonly ILogger<RecommendationController> _logger;

    public RecommendationController(
        IRecommendationService recommendationService,
        IMLRecommendationEngine mlEngine,
        IABTestService abTestService,
        ILogger<RecommendationController> logger)
    {
        _recommendationService = recommendationService;
        _mlEngine = mlEngine;
        _abTestService = abTestService;
        _logger = logger;
    }

    /// <summary>
    /// 获取个性化推荐
    /// </summary>
    /// <param name="request">推荐请求</param>
    /// <returns>推荐响应</returns>
    [HttpPost("recommendations")]
    [ProducesResponseType(typeof(ApiResponse<RecommendationResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<RecommendationResponse>), 400)]
    public async Task<ActionResult<ApiResponse<RecommendationResponse>>> GetRecommendations(
        [FromBody] RecommendationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<RecommendationResponse>.ErrorResult("请求参数无效"));
            }

            var result = await _recommendationService.GetRecommendationsAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取推荐时发生错误");
            return StatusCode(500, ApiResponse<RecommendationResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取相似产品推荐
    /// </summary>
    /// <param name="productId">产品ID</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐响应</returns>
    [HttpGet("similar/{productId}")]
    [ProducesResponseType(typeof(ApiResponse<RecommendationResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<RecommendationResponse>), 400)]
    public async Task<ActionResult<ApiResponse<RecommendationResponse>>> GetSimilarProducts(
        [FromRoute] int productId,
        [FromQuery] int count = 10)
    {
        try
        {
            if (productId <= 0)
            {
                return BadRequest(ApiResponse<RecommendationResponse>.ErrorResult("产品ID无效"));
            }

            if (count <= 0 || count > 50)
            {
                return BadRequest(ApiResponse<RecommendationResponse>.ErrorResult("推荐数量必须在1-50之间"));
            }

            var result = await _recommendationService.GetSimilarProductsAsync(productId, count);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取相似产品推荐时发生错误");
            return StatusCode(500, ApiResponse<RecommendationResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取热门产品推荐
    /// </summary>
    /// <param name="category">产品类别</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐响应</returns>
    [HttpGet("popular")]
    [ProducesResponseType(typeof(ApiResponse<RecommendationResponse>), 200)]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<RecommendationResponse>>> GetPopularProducts(
        [FromQuery] string? category = null,
        [FromQuery] int count = 10)
    {
        try
        {
            if (count <= 0 || count > 50)
            {
                return BadRequest(ApiResponse<RecommendationResponse>.ErrorResult("推荐数量必须在1-50之间"));
            }

            var result = await _recommendationService.GetPopularProductsAsync(category, count);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门产品推荐时发生错误");
            return StatusCode(500, ApiResponse<RecommendationResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 记录用户行为
    /// </summary>
    /// <param name="behavior">用户行为数据</param>
    /// <returns>操作结果</returns>
    [HttpPost("behavior")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    public async Task<ActionResult<ApiResponse<bool>>> RecordUserBehavior(
        [FromBody] UserBehavior behavior)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _recommendationService.RecordUserBehaviorAsync(behavior);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录用户行为时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 提交推荐反馈
    /// </summary>
    /// <param name="feedback">推荐反馈</param>
    /// <returns>操作结果</returns>
    [HttpPost("feedback")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    public async Task<ActionResult<ApiResponse<bool>>> SubmitFeedback(
        [FromBody] RecommendationFeedback feedback)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _recommendationService.SubmitFeedbackAsync(feedback);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提交推荐反馈时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 更新用户画像
    /// </summary>
    /// <param name="profile">用户画像</param>
    /// <returns>操作结果</returns>
    [HttpPut("profile")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateUserProfile(
        [FromBody] UserProfile profile)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _recommendationService.UpdateUserProfileAsync(profile);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户画像时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取推荐算法性能指标
    /// </summary>
    /// <param name="algorithm">算法名称</param>
    /// <returns>性能指标</returns>
    [HttpGet("metrics")]
    [ProducesResponseType(typeof(ApiResponse<RecommendationMetrics>), 200)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<RecommendationMetrics>>> GetAlgorithmMetrics(
        [FromQuery] string? algorithm = null)
    {
        try
        {
            var result = await _recommendationService.GetAlgorithmMetricsAsync(algorithm);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取算法性能指标时发生错误");
            return StatusCode(500, ApiResponse<RecommendationMetrics>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 训练推荐模型
    /// </summary>
    /// <param name="generateMockData">是否生成模拟数据</param>
    /// <returns>训练结果</returns>
    [HttpPost("train")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> TrainModel(
        [FromQuery] bool generateMockData = true)
    {
        try
        {
            List<RecommendationTrainingData> trainingData;
            
            if (generateMockData)
            {
                // 生成模拟训练数据
                var mlEngine = _mlEngine as MLRecommendationEngine;
                trainingData = mlEngine?.GenerateMockTrainingData(100, 50, 1000) ?? new List<RecommendationTrainingData>();
                
                _logger.LogInformation("生成了 {Count} 条模拟训练数据", trainingData.Count);
            }
            else
            {
                // 这里应该从实际的用户行为数据构建训练数据
                trainingData = new List<RecommendationTrainingData>();
                _logger.LogWarning("实际训练数据构建功能尚未实现，使用空数据集");
            }

            var result = await _mlEngine.TrainModelAsync(trainingData);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练推荐模型时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取模型性能指标
    /// </summary>
    /// <returns>模型指标</returns>
    [HttpGet("model/metrics")]
    [ProducesResponseType(typeof(ApiResponse<Dictionary<string, double>>), 200)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, double>>>> GetModelMetrics()
    {
        try
        {
            var result = await _mlEngine.GetModelMetricsAsync();
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模型性能指标时发生错误");
            return StatusCode(500, ApiResponse<Dictionary<string, double>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 创建A/B测试
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>创建结果</returns>
    [HttpPost("abtest")]
    [ProducesResponseType(typeof(ApiResponse<ABTestConfig>), 201)]
    [ProducesResponseType(typeof(ApiResponse<ABTestConfig>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<ABTestConfig>>> CreateABTest(
        [FromBody] ABTestConfig config)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<ABTestConfig>.ErrorResult("请求参数无效"));
            }

            var result = await _abTestService.CreateABTestAsync(config);
            
            if (result.Success)
            {
                return CreatedAtAction(nameof(GetABTestResults), new { testName = config.Name }, result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建A/B测试时发生错误");
            return StatusCode(500, ApiResponse<ABTestConfig>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取A/B测试结果
    /// </summary>
    /// <param name="testName">测试名称</param>
    /// <returns>测试结果</returns>
    [HttpGet("abtest/{testName}/results")]
    [ProducesResponseType(typeof(ApiResponse<Dictionary<string, object>>), 200)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, object>>>> GetABTestResults(
        [FromRoute] string testName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(testName))
            {
                return BadRequest(ApiResponse<Dictionary<string, object>>.ErrorResult("测试名称无效"));
            }

            var result = await _abTestService.GetTestResultsAsync(testName);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取A/B测试结果时发生错误");
            return StatusCode(500, ApiResponse<Dictionary<string, object>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>服务健康状态</returns>
    [HttpGet("health")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Service = "RecommendationService",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Features = new[]
            {
                "个性化推荐",
                "协同过滤",
                "内容过滤",
                "机器学习推荐",
                "A/B测试",
                "实时行为追踪",
                "用户画像管理",
                "推荐缓存优化"
            }
        });
    }
}
