using PersimmonChic.Shared.Common;
using PersimmonChic.Shared.Models;
using System.Text.Json;

namespace PersimmonChic.Gateway.Services;

/// <summary>
/// 网关服务实现
/// </summary>
public class GatewayService : IGatewayService
{
    private readonly ServiceRegistry _serviceRegistry;
    private readonly HttpClientFactory _httpClientFactory;
    private readonly ILogger<GatewayService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public GatewayService(
        ServiceRegistry serviceRegistry,
        HttpClientFactory httpClientFactory,
        ILogger<GatewayService> logger)
    {
        _serviceRegistry = serviceRegistry;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<ApiResponse<T>> ForwardRequestAsync<T>(
        string serviceName,
        string method,
        string endpoint,
        object? requestBody = null,
        Dictionary<string, string>? headers = null)
    {
        try
        {
            return method.ToUpper() switch
            {
                "GET" => await _httpClientFactory.GetAsync<T>(serviceName, endpoint, headers),
                "POST" => await _httpClientFactory.PostAsync<object, T>(serviceName, endpoint, requestBody ?? new object(), headers),
                "PUT" => await _httpClientFactory.PutAsync<object, T>(serviceName, endpoint, requestBody ?? new object(), headers),
                "DELETE" => ConvertDeleteResponse<T>(await _httpClientFactory.DeleteAsync(serviceName, endpoint, headers)),
                _ => ApiResponse<T>.ErrorResult($"Unsupported HTTP method: {method}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forwarding request to {ServiceName} {Method} {Endpoint}", 
                serviceName, method, endpoint);
            return ApiResponse<T>.ErrorResult($"Request forwarding failed: {ex.Message}");
        }
    }

    public async Task<ApiResponse<HealthStatus>> CheckServiceHealthAsync(string serviceName)
    {
        try
        {
            var instance = _serviceRegistry.GetService(serviceName);
            if (instance == null)
            {
                return ApiResponse<HealthStatus>.ErrorResult($"Service {serviceName} not found");
            }

            using var httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(5) };
            var healthUrl = $"{instance.BaseUrl}/health";
            
            var response = await httpClient.GetAsync(healthUrl);
            var isHealthy = response.IsSuccessStatusCode;

            _serviceRegistry.UpdateServiceHealth(serviceName, instance.Id, isHealthy);

            var healthStatus = new HealthStatus
            {
                ServiceName = serviceName,
                Status = isHealthy ? "Healthy" : "Unhealthy",
                Version = instance.Version,
                Details = new Dictionary<string, object>
                {
                    ["Address"] = instance.Address,
                    ["Port"] = instance.Port,
                    ["LastCheck"] = DateTime.UtcNow,
                    ["ResponseTime"] = response.Headers.Date?.ToString() ?? "N/A"
                }
            };

            return ApiResponse<HealthStatus>.SuccessResult(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking health for service {ServiceName}", serviceName);
            
            var healthStatus = new HealthStatus
            {
                ServiceName = serviceName,
                Status = "Error",
                Details = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message,
                    ["LastCheck"] = DateTime.UtcNow
                }
            };

            return ApiResponse<HealthStatus>.SuccessResult(healthStatus);
        }
    }

    public async Task<ApiResponse<List<HealthStatus>>> GetAllServiceStatusAsync()
    {
        var serviceNames = _serviceRegistry.GetServiceNames();
        var healthStatuses = new List<HealthStatus>();

        foreach (var serviceName in serviceNames)
        {
            var healthResponse = await CheckServiceHealthAsync(serviceName);
            if (healthResponse.Success && healthResponse.Data != null)
            {
                healthStatuses.Add(healthResponse.Data);
            }
        }

        return ApiResponse<List<HealthStatus>>.SuccessResult(healthStatuses);
    }

    public Task<ApiResponse<bool>> RegisterServiceAsync(string serviceName, string address, int port, string version = "1.0.0")
    {
        try
        {
            var instance = new ServiceInstance
            {
                Address = address,
                Port = port,
                Version = version
            };

            _serviceRegistry.RegisterService(serviceName, instance);
            
            _logger.LogInformation("Service {ServiceName} registered successfully at {Address}:{Port}", 
                serviceName, address, port);

            return Task.FromResult(ApiResponse<bool>.SuccessResult(true, "Service registered successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering service {ServiceName}", serviceName);
            return Task.FromResult(ApiResponse<bool>.ErrorResult($"Service registration failed: {ex.Message}"));
        }
    }

    public Task<ApiResponse<bool>> UnregisterServiceAsync(string serviceName, string instanceId)
    {
        try
        {
            _serviceRegistry.UnregisterService(serviceName, instanceId);
            
            _logger.LogInformation("Service {ServiceName} instance {InstanceId} unregistered successfully", 
                serviceName, instanceId);

            return Task.FromResult(ApiResponse<bool>.SuccessResult(true, "Service unregistered successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unregistering service {ServiceName} instance {InstanceId}", 
                serviceName, instanceId);
            return Task.FromResult(ApiResponse<bool>.ErrorResult($"Service unregistration failed: {ex.Message}"));
        }
    }

    public Task<ApiResponse<bool>> UpdateServiceHealthAsync(string serviceName, string instanceId, bool isHealthy)
    {
        try
        {
            _serviceRegistry.UpdateServiceHealth(serviceName, instanceId, isHealthy);
            
            _logger.LogDebug("Service {ServiceName} instance {InstanceId} health updated to {IsHealthy}", 
                serviceName, instanceId, isHealthy);

            return Task.FromResult(ApiResponse<bool>.SuccessResult(true, "Service health updated successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating service health for {ServiceName} instance {InstanceId}", 
                serviceName, instanceId);
            return Task.FromResult(ApiResponse<bool>.ErrorResult($"Service health update failed: {ex.Message}"));
        }
    }

    private static ApiResponse<T> ConvertDeleteResponse<T>(ApiResponse<bool> deleteResponse)
    {
        if (deleteResponse.Success)
        {
            // For DELETE operations, we typically return the success status
            var result = (T)(object)deleteResponse.Data;
            return ApiResponse<T>.SuccessResult(result, deleteResponse.Message);
        }
        else
        {
            return ApiResponse<T>.ErrorResult(deleteResponse.Message, deleteResponse.ErrorCode);
        }
    }
}
