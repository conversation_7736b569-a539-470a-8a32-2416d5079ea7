using PersimmonChic.SearchService.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.SearchService.Services;

/// <summary>
/// 搜索服务接口
/// </summary>
public interface ISearchService
{
    /// <summary>
    /// 执行搜索
    /// </summary>
    /// <param name="request">搜索请求</param>
    /// <returns>搜索响应</returns>
    Task<ApiResponse<SearchResponse>> SearchAsync(SearchRequest request);
    
    /// <summary>
    /// 自动补全
    /// </summary>
    /// <param name="request">自动补全请求</param>
    /// <returns>自动补全响应</returns>
    Task<ApiResponse<AutoCompleteResponse>> AutoCompleteAsync(AutoCompleteRequest request);
    
    /// <summary>
    /// 获取搜索建议
    /// </summary>
    /// <param name="query">查询词</param>
    /// <param name="count">建议数量</param>
    /// <returns>搜索建议列表</returns>
    Task<ApiResponse<List<string>>> GetSuggestionsAsync(string query, int count = 10);
    
    /// <summary>
    /// 记录搜索查询
    /// </summary>
    /// <param name="queryLog">查询日志</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> LogSearchQueryAsync(SearchQueryLog queryLog);
    
    /// <summary>
    /// 获取热门搜索词
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="timeRange">时间范围（小时）</param>
    /// <returns>热门搜索词列表</returns>
    Task<ApiResponse<List<string>>> GetTrendingQueriesAsync(int count = 10, int timeRange = 24);
    
    /// <summary>
    /// 获取搜索分析数据
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>搜索分析数据</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetSearchAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);
}

/// <summary>
/// 语义搜索引擎接口
/// </summary>
public interface ISemanticSearchEngine
{
    /// <summary>
    /// 语义搜索
    /// </summary>
    /// <param name="query">查询文本</param>
    /// <param name="topK">返回结果数量</param>
    /// <param name="threshold">相似度阈值</param>
    /// <returns>搜索结果</returns>
    Task<ApiResponse<List<SearchResult>>> SemanticSearchAsync(string query, int topK = 20, float threshold = 0.7f);
    
    /// <summary>
    /// 生成文本向量
    /// </summary>
    /// <param name="text">文本内容</param>
    /// <returns>向量表示</returns>
    Task<ApiResponse<List<float>>> GenerateEmbeddingAsync(string text);
    
    /// <summary>
    /// 计算向量相似度
    /// </summary>
    /// <param name="vector1">向量1</param>
    /// <param name="vector2">向量2</param>
    /// <returns>相似度分数</returns>
    Task<ApiResponse<float>> CalculateSimilarityAsync(List<float> vector1, List<float> vector2);
    
    /// <summary>
    /// 批量索引文档
    /// </summary>
    /// <param name="documents">文档列表</param>
    /// <returns>索引结果</returns>
    Task<ApiResponse<bool>> IndexDocumentsAsync(List<SearchDocument> documents);
    
    /// <summary>
    /// 更新文档索引
    /// </summary>
    /// <param name="document">文档</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<bool>> UpdateDocumentAsync(SearchDocument document);
    
    /// <summary>
    /// 删除文档索引
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <returns>删除结果</returns>
    Task<ApiResponse<bool>> DeleteDocumentAsync(int documentId);
}

/// <summary>
/// 关键词搜索引擎接口
/// </summary>
public interface IKeywordSearchEngine
{
    /// <summary>
    /// 关键词搜索
    /// </summary>
    /// <param name="query">查询关键词</param>
    /// <param name="filters">过滤条件</param>
    /// <param name="sortBy">排序方式</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    Task<ApiResponse<List<SearchResult>>> KeywordSearchAsync(
        string query, 
        Dictionary<string, object> filters, 
        SortBy sortBy = SortBy.Relevance, 
        int page = 1, 
        int pageSize = 20);
    
    /// <summary>
    /// 模糊搜索
    /// </summary>
    /// <param name="query">查询词</param>
    /// <param name="fuzziness">模糊度</param>
    /// <param name="topK">返回结果数量</param>
    /// <returns>搜索结果</returns>
    Task<ApiResponse<List<SearchResult>>> FuzzySearchAsync(string query, int fuzziness = 2, int topK = 20);
    
    /// <summary>
    /// 精确匹配搜索
    /// </summary>
    /// <param name="query">查询词</param>
    /// <param name="field">搜索字段</param>
    /// <param name="topK">返回结果数量</param>
    /// <returns>搜索结果</returns>
    Task<ApiResponse<List<SearchResult>>> ExactMatchSearchAsync(string query, string? field = null, int topK = 20);
    
    /// <summary>
    /// 构建搜索索引
    /// </summary>
    /// <param name="documents">文档列表</param>
    /// <returns>索引结果</returns>
    Task<ApiResponse<bool>> BuildIndexAsync(List<SearchDocument> documents);
}

/// <summary>
/// 混合搜索引擎接口
/// </summary>
public interface IHybridSearchEngine
{
    /// <summary>
    /// 混合搜索
    /// </summary>
    /// <param name="query">查询文本</param>
    /// <param name="semanticWeight">语义搜索权重</param>
    /// <param name="keywordWeight">关键词搜索权重</param>
    /// <param name="topK">返回结果数量</param>
    /// <returns>搜索结果</returns>
    Task<ApiResponse<List<SearchResult>>> HybridSearchAsync(
        string query, 
        float semanticWeight = 0.6f, 
        float keywordWeight = 0.4f, 
        int topK = 20);
    
    /// <summary>
    /// 重新排序搜索结果
    /// </summary>
    /// <param name="results">原始结果</param>
    /// <param name="query">查询文本</param>
    /// <param name="context">上下文信息</param>
    /// <returns>重排序后的结果</returns>
    Task<ApiResponse<List<SearchResult>>> ReRankResultsAsync(
        List<SearchResult> results, 
        string query, 
        Dictionary<string, object> context);
}

/// <summary>
/// 搜索分析服务接口
/// </summary>
public interface ISearchAnalyticsService
{
    /// <summary>
    /// 记录搜索事件
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="query">查询词</param>
    /// <param name="userId">用户ID</param>
    /// <param name="metadata">元数据</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> RecordSearchEventAsync(
        string eventType, 
        string query, 
        string? userId = null, 
        Dictionary<string, object>? metadata = null);
    
    /// <summary>
    /// 获取搜索统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>搜索统计数据</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetSearchStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    /// <summary>
    /// 获取查询性能指标
    /// </summary>
    /// <param name="query">查询词</param>
    /// <returns>性能指标</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetQueryPerformanceAsync(string? query = null);
    
    /// <summary>
    /// 获取用户搜索行为
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="days">天数</param>
    /// <returns>用户搜索行为数据</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetUserSearchBehaviorAsync(string userId, int days = 30);
}

/// <summary>
/// 搜索缓存服务接口
/// </summary>
public interface ISearchCacheService
{
    /// <summary>
    /// 获取缓存的搜索结果
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>搜索响应</returns>
    Task<SearchResponse?> GetCachedSearchResultAsync(string cacheKey);
    
    /// <summary>
    /// 设置搜索结果缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="searchResponse">搜索响应</param>
    /// <param name="expiration">过期时间</param>
    /// <returns>操作结果</returns>
    Task<bool> SetSearchResultCacheAsync(string cacheKey, SearchResponse searchResponse, TimeSpan? expiration = null);
    
    /// <summary>
    /// 获取缓存的自动补全结果
    /// </summary>
    /// <param name="query">查询词</param>
    /// <returns>自动补全响应</returns>
    Task<AutoCompleteResponse?> GetCachedAutoCompleteAsync(string query);
    
    /// <summary>
    /// 设置自动补全缓存
    /// </summary>
    /// <param name="query">查询词</param>
    /// <param name="response">自动补全响应</param>
    /// <param name="expiration">过期时间</param>
    /// <returns>操作结果</returns>
    Task<bool> SetAutoCompleteCacheAsync(string query, AutoCompleteResponse response, TimeSpan? expiration = null);
    
    /// <summary>
    /// 清除搜索缓存
    /// </summary>
    /// <param name="pattern">缓存键模式</param>
    /// <returns>操作结果</returns>
    Task<bool> ClearSearchCacheAsync(string? pattern = null);
    
    /// <summary>
    /// 生成搜索缓存键
    /// </summary>
    /// <param name="request">搜索请求</param>
    /// <returns>缓存键</returns>
    string GenerateSearchCacheKey(SearchRequest request);
}

/// <summary>
/// 搜索索引管理服务接口
/// </summary>
public interface ISearchIndexService
{
    /// <summary>
    /// 创建索引
    /// </summary>
    /// <param name="indexName">索引名称</param>
    /// <param name="settings">索引设置</param>
    /// <returns>创建结果</returns>
    Task<ApiResponse<bool>> CreateIndexAsync(string indexName, Dictionary<string, object> settings);
    
    /// <summary>
    /// 删除索引
    /// </summary>
    /// <param name="indexName">索引名称</param>
    /// <returns>删除结果</returns>
    Task<ApiResponse<bool>> DeleteIndexAsync(string indexName);
    
    /// <summary>
    /// 重建索引
    /// </summary>
    /// <param name="indexName">索引名称</param>
    /// <returns>重建结果</returns>
    Task<ApiResponse<bool>> RebuildIndexAsync(string indexName);
    
    /// <summary>
    /// 获取索引统计
    /// </summary>
    /// <param name="indexName">索引名称</param>
    /// <returns>索引统计信息</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetIndexStatsAsync(string indexName);
    
    /// <summary>
    /// 优化索引
    /// </summary>
    /// <param name="indexName">索引名称</param>
    /// <returns>优化结果</returns>
    Task<ApiResponse<bool>> OptimizeIndexAsync(string indexName);
}
