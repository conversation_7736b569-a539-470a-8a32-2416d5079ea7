{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.SignalR": "Information", "PersimmonChic.RiskControlService": "Debug"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "Per<PERSON><PERSON>on<PERSON><PERSON>_Super_Secret_Key_2024_Demo_Application", "Issuer": "PersimmonChic", "Audience": "PersimmonChic-Users", "ExpirationHours": 24}, "ConnectionStrings": {"Redis": "localhost:6379", "Database": "Data Source=risk_control.db"}, "RiskControlSettings": {"EnableRealTimeMonitoring": true, "EnableMLRiskAssessment": true, "EnableDeviceFingerprinting": true, "MaxEventQueueSize": 10000, "EventProcessingBatchSize": 100, "CacheExpirationMinutes": 30, "ModelRetrainingIntervalHours": 24, "DefaultRiskThresholds": {"Low": 30, "Medium": 60, "High": 80, "Critical": 95}, "AlertSettings": {"EnableEmailAlerts": false, "EnableSmsAlerts": false, "EnableWebhookAlerts": true, "WebhookUrl": "http://localhost:5000/api/webhooks/risk-alerts", "AlertCooldownMinutes": 5}, "MLSettings": {"ModelPath": "Models/risk_assessment_model.zip", "TrainingDataMinSize": 100, "RetrainingThreshold": 1000, "FeatureExtractionEnabled": true}, "DeviceFingerprintSettings": {"HashAlgorithm": "SHA256", "TrustedDeviceExpirationDays": 90, "MaxTrustedDevicesPerUser": 10, "RequireDeviceVerification": false}, "RateLimitSettings": {"MaxRequestsPerMinute": 1000, "MaxRequestsPerHour": 10000, "EnableRateLimit": true, "BlockDurationMinutes": 15}}, "ServiceDiscovery": {"ServiceName": "RiskControlService", "Address": "localhost", "Port": 5004, "HealthCheckEndpoint": "/health", "GatewayUrl": "http://localhost:5000", "Tags": ["risk-control", "security", "ml", "real-time"]}, "SignalR": {"EnableDetailedErrors": true, "KeepAliveIntervalSeconds": 15, "ClientTimeoutIntervalSeconds": 30, "MaximumReceiveMessageSize": 32768, "StreamBufferCapacity": 10}, "HealthChecks": {"UI": {"Enable": true, "Path": "/health-ui"}, "Checks": [{"Name": "Redis", "Type": "Redis", "ConnectionString": "localhost:6379"}, {"Name": "MLModel", "Type": "Custom", "Description": "机器学习模型健康检查"}]}, "Monitoring": {"EnableMetrics": true, "MetricsEndpoint": "/metrics", "EnableTracing": true, "TracingEndpoint": "/trace", "SampleRate": 0.1}, "Security": {"EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5000", "http://localhost:5001"], "EnableRateLimiting": true, "EnableRequestValidation": true, "MaxRequestSize": 1048576, "EnableAuditLogging": true}, "Features": {"RealTimeRiskMonitoring": {"Enabled": true, "Description": "实时风险监控和告警"}, "MLRiskAssessment": {"Enabled": true, "Description": "基于机器学习的风险评估"}, "DeviceFingerprinting": {"Enabled": true, "Description": "设备指纹识别和验证"}, "AnomalyDetection": {"Enabled": true, "Description": "异常行为检测"}, "RiskRuleEngine": {"Enabled": true, "Description": "可配置的风险规则引擎"}, "SignalRIntegration": {"Enabled": true, "Description": "SignalR实时通信"}}}