<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.33" />
    <PackageReference Include="Microsoft.ML" Version="3.0.1" />
    <PackageReference Include="Microsoft.ML.FastTree" Version="3.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\Models\PersimmonChic.Shared.Models.csproj" />
    <ProjectReference Include="..\..\Shared\Contracts\PersimmonChic.Shared.Contracts.csproj" />
    <ProjectReference Include="..\..\Shared\Common\PersimmonChic.Shared.Common.csproj" />
    <ProjectReference Include="..\..\Infrastructure\DataAccess\PersimmonChic.Infrastructure.DataAccess.csproj" />
  </ItemGroup>

</Project>
