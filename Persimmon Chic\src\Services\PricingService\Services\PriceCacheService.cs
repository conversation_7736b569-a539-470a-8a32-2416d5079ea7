using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace PersimmonChic.PricingService.Services;

/// <summary>
/// 价格缓存服务实现
/// </summary>
public class PriceCacheService : IPriceCacheService
{
    private readonly ILogger<PriceCacheService> _logger;
    private readonly IDistributedCache _cache;
    private const string PRICE_CACHE_PREFIX = "price:";
    private const string PRODUCT_CACHE_PREFIX = "product_price:";

    public PriceCacheService(
        ILogger<PriceCacheService> logger,
        IDistributedCache cache)
    {
        _logger = logger;
        _cache = cache;
    }

    public async Task<decimal?> GetCachedPriceAsync(string cacheKey)
    {
        try
        {
            var cachedValue = await _cache.GetStringAsync(PRICE_CACHE_PREFIX + cacheKey);
            
            if (!string.IsNullOrEmpty(cachedValue))
            {
                if (decimal.TryParse(cachedValue, out var price))
                {
                    _logger.LogDebug("从缓存获取价格: {CacheKey} = {Price}", cacheKey, price);
                    return price;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存价格时发生错误: {CacheKey}", cacheKey);
            return null;
        }
    }

    public async Task<bool> SetPriceCacheAsync(string cacheKey, decimal price, TimeSpan? expiration = null)
    {
        try
        {
            var options = new DistributedCacheEntryOptions();
            
            if (expiration.HasValue)
            {
                options.SlidingExpiration = expiration.Value;
            }
            else
            {
                options.SlidingExpiration = TimeSpan.FromMinutes(15); // 默认15分钟
            }

            await _cache.SetStringAsync(PRICE_CACHE_PREFIX + cacheKey, price.ToString(), options);
            
            _logger.LogDebug("设置价格缓存: {CacheKey} = {Price}, 过期时间: {Expiration}", 
                cacheKey, price, expiration?.TotalMinutes ?? 15);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置价格缓存时发生错误: {CacheKey}", cacheKey);
            return false;
        }
    }

    public async Task<bool> RemovePriceCacheAsync(string cacheKey)
    {
        try
        {
            await _cache.RemoveAsync(PRICE_CACHE_PREFIX + cacheKey);
            _logger.LogDebug("删除价格缓存: {CacheKey}", cacheKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除价格缓存时发生错误: {CacheKey}", cacheKey);
            return false;
        }
    }

    public async Task<bool> ClearProductPriceCacheAsync(int productId)
    {
        try
        {
            // 由于Redis不支持通配符删除，这里使用简化的实现
            // 实际生产环境中可能需要维护一个产品相关缓存键的集合
            
            var productCacheKey = $"{PRODUCT_CACHE_PREFIX}{productId}";
            await _cache.RemoveAsync(productCacheKey);
            
            // 清除常见的价格缓存组合
            var commonCacheKeys = new[]
            {
                GeneratePriceCacheKey(productId, new Dictionary<string, object>()),
                GeneratePriceCacheKey(productId, new Dictionary<string, object> { ["channel"] = "online" }),
                GeneratePriceCacheKey(productId, new Dictionary<string, object> { ["channel"] = "retail" }),
                GeneratePriceCacheKey(productId, new Dictionary<string, object> { ["user_tier"] = "gold" }),
                GeneratePriceCacheKey(productId, new Dictionary<string, object> { ["user_tier"] = "silver" }),
                GeneratePriceCacheKey(productId, new Dictionary<string, object> { ["region"] = "CN" }),
                GeneratePriceCacheKey(productId, new Dictionary<string, object> { ["region"] = "US" })
            };

            foreach (var key in commonCacheKeys)
            {
                await RemovePriceCacheAsync(key);
            }

            _logger.LogInformation("清除商品 {ProductId} 相关的价格缓存", productId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除商品价格缓存时发生错误: {ProductId}", productId);
            return false;
        }
    }

    public string GeneratePriceCacheKey(int productId, Dictionary<string, object> context)
    {
        try
        {
            var keyParts = new List<string> { $"product_{productId}" };

            // 添加上下文信息到缓存键
            if (context.ContainsKey("user_id"))
            {
                keyParts.Add($"user_{context["user_id"]}");
            }

            if (context.ContainsKey("user_tier"))
            {
                keyParts.Add($"tier_{context["user_tier"]}");
            }

            if (context.ContainsKey("channel"))
            {
                keyParts.Add($"channel_{context["channel"]}");
            }

            if (context.ContainsKey("region"))
            {
                keyParts.Add($"region_{context["region"]}");
            }

            if (context.ContainsKey("quantity"))
            {
                keyParts.Add($"qty_{context["quantity"]}");
            }

            if (context.ContainsKey("currency"))
            {
                keyParts.Add($"curr_{context["currency"]}");
            }

            // 添加时间戳（小时级别）以支持时间敏感的价格
            var hourKey = DateTime.UtcNow.ToString("yyyyMMddHH");
            keyParts.Add($"time_{hourKey}");

            var cacheKey = string.Join(":", keyParts);
            
            // 确保缓存键长度不超过限制
            if (cacheKey.Length > 250)
            {
                // 使用哈希来缩短键长度
                var hash = cacheKey.GetHashCode().ToString("X");
                cacheKey = $"product_{productId}:hash_{hash}";
            }

            return cacheKey;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成价格缓存键时发生错误");
            return $"product_{productId}:default";
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计信息</returns>
    public async Task<Dictionary<string, object>> GetCacheStatsAsync()
    {
        try
        {
            var stats = new Dictionary<string, object>
            {
                ["cache_type"] = "Redis",
                ["timestamp"] = DateTime.UtcNow,
                ["status"] = "healthy"
            };

            // 尝试执行一个简单的缓存操作来检查连接
            var testKey = "cache_health_check";
            await _cache.SetStringAsync(testKey, "ok", new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromSeconds(10)
            });
            
            var testValue = await _cache.GetStringAsync(testKey);
            stats["connection_test"] = testValue == "ok" ? "passed" : "failed";
            
            await _cache.RemoveAsync(testKey);

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存统计信息时发生错误");
            return new Dictionary<string, object>
            {
                ["cache_type"] = "Redis",
                ["timestamp"] = DateTime.UtcNow,
                ["status"] = "error",
                ["error"] = ex.Message
            };
        }
    }

    /// <summary>
    /// 预热价格缓存
    /// </summary>
    /// <param name="productIds">商品ID列表</param>
    /// <returns>预热结果</returns>
    public async Task<bool> WarmupPriceCacheAsync(List<int> productIds)
    {
        try
        {
            _logger.LogInformation("开始预热价格缓存，商品数量: {Count}", productIds.Count);

            var warmupTasks = new List<Task>();

            foreach (var productId in productIds)
            {
                // 为每个商品预热常见的价格缓存组合
                var contexts = new[]
                {
                    new Dictionary<string, object>(), // 默认价格
                    new Dictionary<string, object> { ["channel"] = "online" },
                    new Dictionary<string, object> { ["channel"] = "retail" },
                    new Dictionary<string, object> { ["user_tier"] = "gold" },
                    new Dictionary<string, object> { ["user_tier"] = "silver" },
                    new Dictionary<string, object> { ["region"] = "CN" }
                };

                foreach (var context in contexts)
                {
                    warmupTasks.Add(WarmupSinglePriceCacheAsync(productId, context));
                }
            }

            await Task.WhenAll(warmupTasks);

            _logger.LogInformation("价格缓存预热完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预热价格缓存时发生错误");
            return false;
        }
    }

    private async Task WarmupSinglePriceCacheAsync(int productId, Dictionary<string, object> context)
    {
        try
        {
            var cacheKey = GeneratePriceCacheKey(productId, context);
            
            // 检查缓存是否已存在
            var existingPrice = await GetCachedPriceAsync(cacheKey);
            if (existingPrice.HasValue)
            {
                return; // 缓存已存在，跳过
            }

            // 这里应该调用实际的价格计算逻辑
            // 为了演示，使用一个模拟价格
            var mockPrice = 100m + (productId % 100);
            await SetPriceCacheAsync(cacheKey, mockPrice, TimeSpan.FromHours(1));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预热单个价格缓存时发生错误: ProductId={ProductId}", productId);
        }
    }
}
