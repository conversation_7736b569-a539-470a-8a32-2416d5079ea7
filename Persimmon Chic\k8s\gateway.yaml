# Gateway部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: gateway
    app.kubernetes.io/component: api-gateway
    app.kubernetes.io/part-of: persimmon-chic
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 3
  selector:
    matchLabels:
      app.kubernetes.io/name: gateway
  template:
    metadata:
      labels:
        app.kubernetes.io/name: gateway
        app.kubernetes.io/component: api-gateway
        app.kubernetes.io/version: "1.0.0"
    spec:
      containers:
      - name: gateway
        image: persimmonchic/persimmonchic-gateway:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: ASPNETCORE_URLS
        - name: ConnectionStrings__Redis
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: CONNECTIONSTRINGS__REDIS
        - name: JwtSettings__SecretKey
          valueFrom:
            secretKeyRef:
              name: persimmon-chic-secrets
              key: JWT_SECRET_KEY
        - name: JwtSettings__Issuer
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: JWTSETTINGS__ISSUER
        - name: JwtSettings__Audience
          valueFrom:
            configMapKeyRef:
              name: persimmon-chic-config
              key: JWTSETTINGS__AUDIENCE
        envFrom:
        - configMapRef:
            name: persimmon-chic-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      imagePullSecrets:
      - name: persimmon-chic-registry-secret

---
# Gateway服务
apiVersion: v1
kind: Service
metadata:
  name: gateway-service
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: gateway
    app.kubernetes.io/component: api-gateway
spec:
  selector:
    app.kubernetes.io/name: gateway
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP

---
# Gateway水平自动扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gateway-hpa
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: gateway
    app.kubernetes.io/component: api-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# Gateway Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-ingress
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: gateway
    app.kubernetes.io/component: api-gateway
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  rules:
  - host: api.persimmonchic.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway-service
            port:
              number: 80
  - host: localhost
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: gateway-service
            port:
              number: 80
