using Microsoft.Extensions.Logging;
using PersimmonChic.CustomerServiceBot.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.CustomerServiceBot.Services;

/// <summary>
/// 意图识别服务实现
/// </summary>
public class IntentRecognitionService : IIntentRecognitionService
{
    private readonly ILogger<IntentRecognitionService> _logger;
    
    // 预定义的意图模式
    private readonly Dictionary<string, List<string>> _intentPatterns = new()
    {
        ["greeting"] = new List<string> { "你好", "您好", "hi", "hello", "早上好", "下午好", "晚上好", "在吗" },
        ["product_inquiry"] = new List<string> { "商品", "产品", "手机", "电脑", "耳机", "价格", "多少钱", "推荐", "介绍" },
        ["order_status"] = new List<string> { "订单", "快递", "物流", "发货", "到货", "查询", "状态", "进度" },
        ["complaint"] = new List<string> { "投诉", "问题", "故障", "坏了", "不满意", "差评", "退款", "赔偿" },
        ["return_refund"] = new List<string> { "退货", "退款", "换货", "退换", "不想要", "质量问题", "七天无理由" },
        ["human_agent"] = new List<string> { "人工", "客服", "转人工", "真人", "工作人员", "联系客服" },
        ["after_sales"] = new List<string> { "售后", "维修", "保修", "质保", "服务", "技术支持" },
        ["payment"] = new List<string> { "支付", "付款", "结算", "买单", "付费", "充值", "余额" },
        ["shipping"] = new List<string> { "配送", "送货", "快递费", "运费", "包邮", "送达时间" },
        ["account"] = new List<string> { "账号", "密码", "登录", "注册", "个人信息", "修改资料" }
    };

    public IntentRecognitionService(ILogger<IntentRecognitionService> logger)
    {
        _logger = logger;
    }

    public async Task<ApiResponse<IntentRecognitionResult>> RecognizeIntentAsync(string message, ConversationContext context)
    {
        try
        {
            _logger.LogDebug("开始意图识别，消息: {Message}", message);

            var result = new IntentRecognitionResult();
            var candidates = new List<IntentCandidate>();

            // 简化的意图识别算法
            var normalizedMessage = message.ToLower();
            
            foreach (var (intent, patterns) in _intentPatterns)
            {
                var score = CalculateIntentScore(normalizedMessage, patterns);
                
                if (score > 0)
                {
                    candidates.Add(new IntentCandidate
                    {
                        Intent = intent,
                        Score = score,
                        Entities = ExtractEntities(normalizedMessage, intent)
                    });
                }
            }

            // 按分数排序
            candidates = candidates.OrderByDescending(c => c.Score).ToList();

            if (candidates.Any())
            {
                var topCandidate = candidates.First();
                result.Intent = topCandidate.Intent;
                result.Confidence = topCandidate.Score;
                result.Entities = topCandidate.Entities;
                result.Candidates = candidates.Take(3).ToList(); // 返回前3个候选
            }
            else
            {
                result.Intent = "unknown";
                result.Confidence = 0.0f;
            }

            // 考虑上下文信息调整置信度
            if (!string.IsNullOrEmpty(context.CurrentIntent))
            {
                result = AdjustWithContext(result, context);
            }

            _logger.LogDebug("意图识别完成，意图: {Intent}, 置信度: {Confidence}", 
                result.Intent, result.Confidence);

            return ApiResponse<IntentRecognitionResult>.SuccessResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "意图识别时发生错误");
            return ApiResponse<IntentRecognitionResult>.ErrorResult($"意图识别失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> TrainModelAsync(List<(string Message, string Intent)> trainingData)
    {
        try
        {
            _logger.LogInformation("开始训练意图识别模型，训练数据量: {Count}", trainingData.Count);

            // 简化的训练过程 - 在实际应用中应该使用机器学习框架
            foreach (var (message, intent) in trainingData)
            {
                var words = message.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
                
                if (_intentPatterns.ContainsKey(intent))
                {
                    // 添加新的模式词汇
                    foreach (var word in words)
                    {
                        if (word.Length > 2 && !_intentPatterns[intent].Contains(word))
                        {
                            _intentPatterns[intent].Add(word);
                        }
                    }
                }
                else
                {
                    // 创建新的意图类别
                    _intentPatterns[intent] = words.Where(w => w.Length > 2).ToList();
                }
            }

            _logger.LogInformation("意图识别模型训练完成");
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练意图识别模型时发生错误");
            return ApiResponse<bool>.ErrorResult($"模型训练失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private float CalculateIntentScore(string message, List<string> patterns)
    {
        var messageWords = message.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var matchCount = 0;
        var totalWeight = 0f;

        foreach (var pattern in patterns)
        {
            foreach (var word in messageWords)
            {
                if (word.Contains(pattern) || pattern.Contains(word))
                {
                    matchCount++;
                    // 完全匹配给更高权重
                    totalWeight += word == pattern ? 1.0f : 0.7f;
                }
            }
        }

        if (matchCount == 0) return 0f;

        // 计算归一化分数
        var score = totalWeight / Math.Max(messageWords.Length, patterns.Count);
        return Math.Min(1.0f, score);
    }

    private Dictionary<string, object> ExtractEntities(string message, string intent)
    {
        var entities = new Dictionary<string, object>();

        try
        {
            switch (intent)
            {
                case "product_inquiry":
                    ExtractProductEntities(message, entities);
                    break;
                    
                case "order_status":
                    ExtractOrderEntities(message, entities);
                    break;
                    
                case "payment":
                    ExtractPaymentEntities(message, entities);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "提取实体时发生错误");
        }

        return entities;
    }

    private void ExtractProductEntities(string message, Dictionary<string, object> entities)
    {
        var productKeywords = new Dictionary<string, string>
        {
            ["手机"] = "smartphone",
            ["电脑"] = "computer",
            ["笔记本"] = "laptop",
            ["耳机"] = "headphone",
            ["音响"] = "speaker",
            ["平板"] = "tablet",
            ["手表"] = "watch"
        };

        foreach (var (keyword, category) in productKeywords)
        {
            if (message.Contains(keyword))
            {
                entities["product_category"] = category;
                entities["product_keyword"] = keyword;
                break;
            }
        }

        // 提取价格范围
        var pricePatterns = new[]
        {
            @"(\d+)元以下", @"低于(\d+)", @"不超过(\d+)",
            @"(\d+)-(\d+)元", @"(\d+)到(\d+)"
        };

        foreach (var pattern in pricePatterns)
        {
            var match = System.Text.RegularExpressions.Regex.Match(message, pattern);
            if (match.Success)
            {
                if (match.Groups.Count == 2)
                {
                    entities["max_price"] = int.Parse(match.Groups[1].Value);
                }
                else if (match.Groups.Count == 3)
                {
                    entities["min_price"] = int.Parse(match.Groups[1].Value);
                    entities["max_price"] = int.Parse(match.Groups[2].Value);
                }
                break;
            }
        }
    }

    private void ExtractOrderEntities(string message, Dictionary<string, object> entities)
    {
        // 提取订单号
        var orderPattern = @"订单号?[:：]?\s*([A-Za-z0-9]{10,20})";
        var match = System.Text.RegularExpressions.Regex.Match(message, orderPattern);
        
        if (match.Success)
        {
            entities["order_number"] = match.Groups[1].Value;
        }

        // 提取时间范围
        var timeKeywords = new Dictionary<string, string>
        {
            ["今天"] = "today",
            ["昨天"] = "yesterday",
            ["本周"] = "this_week",
            ["上周"] = "last_week",
            ["本月"] = "this_month"
        };

        foreach (var (keyword, timeRange) in timeKeywords)
        {
            if (message.Contains(keyword))
            {
                entities["time_range"] = timeRange;
                break;
            }
        }
    }

    private void ExtractPaymentEntities(string message, Dictionary<string, object> entities)
    {
        // 提取支付方式
        var paymentMethods = new Dictionary<string, string>
        {
            ["微信"] = "wechat",
            ["支付宝"] = "alipay",
            ["银行卡"] = "bank_card",
            ["信用卡"] = "credit_card",
            ["现金"] = "cash"
        };

        foreach (var (keyword, method) in paymentMethods)
        {
            if (message.Contains(keyword))
            {
                entities["payment_method"] = method;
                break;
            }
        }

        // 提取金额
        var amountPattern = @"(\d+(?:\.\d{2})?)元";
        var match = System.Text.RegularExpressions.Regex.Match(message, amountPattern);
        
        if (match.Success)
        {
            entities["amount"] = decimal.Parse(match.Groups[1].Value);
        }
    }

    private IntentRecognitionResult AdjustWithContext(IntentRecognitionResult result, ConversationContext context)
    {
        // 基于对话上下文调整意图识别结果
        if (context.TurnCount > 1 && !string.IsNullOrEmpty(context.CurrentIntent))
        {
            // 如果当前意图与上下文相关，提高置信度
            var relatedIntents = GetRelatedIntents(context.CurrentIntent);
            
            if (relatedIntents.Contains(result.Intent))
            {
                result.Confidence = Math.Min(1.0f, result.Confidence * 1.2f);
            }
        }

        return result;
    }

    private List<string> GetRelatedIntents(string intent)
    {
        var relatedIntentsMap = new Dictionary<string, List<string>>
        {
            ["product_inquiry"] = new List<string> { "payment", "order_status", "shipping" },
            ["order_status"] = new List<string> { "shipping", "return_refund", "complaint" },
            ["complaint"] = new List<string> { "return_refund", "human_agent", "after_sales" },
            ["return_refund"] = new List<string> { "complaint", "after_sales", "human_agent" }
        };

        return relatedIntentsMap.ContainsKey(intent) ? relatedIntentsMap[intent] : new List<string>();
    }
}

/// <summary>
/// 知识库服务实现
/// </summary>
public class KnowledgeBaseService : IKnowledgeBaseService
{
    private readonly ILogger<KnowledgeBaseService> _logger;
    private readonly List<KnowledgeBaseEntry> _knowledgeBase;

    public KnowledgeBaseService(ILogger<KnowledgeBaseService> logger)
    {
        _logger = logger;
        _knowledgeBase = InitializeKnowledgeBase();
    }

    public async Task<ApiResponse<string>> FindAnswerAsync(string question, string? category = null)
    {
        try
        {
            _logger.LogDebug("在知识库中查找答案，问题: {Question}, 类别: {Category}", question, category);

            var normalizedQuestion = question.ToLower();
            var candidates = new List<(KnowledgeBaseEntry Entry, float Score)>();

            foreach (var entry in _knowledgeBase.Where(e => e.IsActive))
            {
                // 类别过滤
                if (!string.IsNullOrEmpty(category) && !entry.Category.Equals(category, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                var score = CalculateRelevanceScore(normalizedQuestion, entry);
                
                if (score > 0.3f) // 最小相关度阈值
                {
                    candidates.Add((entry, score));
                }
            }

            if (candidates.Any())
            {
                var bestMatch = candidates.OrderByDescending(c => c.Score).First();
                
                // 更新使用次数
                bestMatch.Entry.UsageCount++;
                
                _logger.LogDebug("找到匹配答案，相关度: {Score}", bestMatch.Score);
                
                return ApiResponse<string>.SuccessResult(bestMatch.Entry.Answer);
            }

            return ApiResponse<string>.ErrorResult("未找到相关答案");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查找知识库答案时发生错误");
            return ApiResponse<string>.ErrorResult($"查找答案失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<KnowledgeBaseEntry>>> SearchKnowledgeBaseAsync(string query, int maxResults = 10)
    {
        try
        {
            var normalizedQuery = query.ToLower();
            var results = new List<(KnowledgeBaseEntry Entry, float Score)>();

            foreach (var entry in _knowledgeBase.Where(e => e.IsActive))
            {
                var score = CalculateRelevanceScore(normalizedQuery, entry);
                
                if (score > 0.1f)
                {
                    results.Add((entry, score));
                }
            }

            var topResults = results
                .OrderByDescending(r => r.Score)
                .Take(maxResults)
                .Select(r => r.Entry)
                .ToList();

            return ApiResponse<List<KnowledgeBaseEntry>>.SuccessResult(topResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索知识库时发生错误");
            return ApiResponse<List<KnowledgeBaseEntry>>.ErrorResult($"搜索失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> AddKnowledgeEntryAsync(KnowledgeBaseEntry entry)
    {
        try
        {
            entry.Id = _knowledgeBase.Count + 1;
            entry.CreatedAt = DateTime.UtcNow;
            entry.UpdatedAt = DateTime.UtcNow;
            
            _knowledgeBase.Add(entry);
            
            _logger.LogInformation("添加知识库条目: {Question}", entry.Question);
            
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加知识库条目时发生错误");
            return ApiResponse<bool>.ErrorResult($"添加失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateKnowledgeEntryAsync(KnowledgeBaseEntry entry)
    {
        try
        {
            var existingEntry = _knowledgeBase.FirstOrDefault(e => e.Id == entry.Id);
            
            if (existingEntry != null)
            {
                existingEntry.Question = entry.Question;
                existingEntry.Answer = entry.Answer;
                existingEntry.Keywords = entry.Keywords;
                existingEntry.Category = entry.Category;
                existingEntry.Tags = entry.Tags;
                existingEntry.UpdatedAt = DateTime.UtcNow;
                
                _logger.LogInformation("更新知识库条目: {Id}", entry.Id);
                
                return ApiResponse<bool>.SuccessResult(true);
            }
            
            return ApiResponse<bool>.ErrorResult("知识库条目不存在");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新知识库条目时发生错误");
            return ApiResponse<bool>.ErrorResult($"更新失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private float CalculateRelevanceScore(string query, KnowledgeBaseEntry entry)
    {
        var score = 0f;
        var queryWords = query.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        // 问题匹配
        var questionWords = entry.Question.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        score += CalculateWordMatchScore(queryWords, questionWords) * 0.6f;

        // 关键词匹配
        var keywords = entry.Keywords.Select(k => k.ToLower()).ToArray();
        score += CalculateWordMatchScore(queryWords, keywords) * 0.3f;

        // 标签匹配
        var tags = entry.Tags.Select(t => t.ToLower()).ToArray();
        score += CalculateWordMatchScore(queryWords, tags) * 0.1f;

        return Math.Min(1.0f, score);
    }

    private float CalculateWordMatchScore(string[] queryWords, string[] targetWords)
    {
        if (!queryWords.Any() || !targetWords.Any()) return 0f;

        var matchCount = 0;
        foreach (var queryWord in queryWords)
        {
            if (targetWords.Any(tw => tw.Contains(queryWord) || queryWord.Contains(tw)))
            {
                matchCount++;
            }
        }

        return (float)matchCount / queryWords.Length;
    }

    private List<KnowledgeBaseEntry> InitializeKnowledgeBase()
    {
        return new List<KnowledgeBaseEntry>
        {
            new KnowledgeBaseEntry
            {
                Id = 1,
                Question = "如何查询订单状态？",
                Answer = "您可以通过以下方式查询订单状态：1. 登录官网或APP，在\"我的订单\"中查看；2. 提供订单号，我来帮您查询；3. 关注微信公众号，绑定手机号后查询。",
                Keywords = new List<string> { "订单", "状态", "查询", "物流" },
                Category = "订单服务",
                Tags = new List<string> { "常见问题", "订单" }
            },
            new KnowledgeBaseEntry
            {
                Id = 2,
                Question = "退换货政策是什么？",
                Answer = "我们提供7天无理由退换货服务：1. 商品需保持原包装完整；2. 不影响二次销售；3. 自收货之日起7天内申请；4. 运费政策：质量问题我们承担，个人原因客户承担。",
                Keywords = new List<string> { "退货", "换货", "退款", "七天", "无理由" },
                Category = "售后服务",
                Tags = new List<string> { "退换货", "政策" }
            },
            new KnowledgeBaseEntry
            {
                Id = 3,
                Question = "支付方式有哪些？",
                Answer = "我们支持多种支付方式：1. 微信支付；2. 支付宝；3. 银行卡支付；4. 信用卡分期；5. 花呗分期；6. 京东白条。所有支付方式都是安全可靠的。",
                Keywords = new List<string> { "支付", "付款", "微信", "支付宝", "银行卡" },
                Category = "支付服务",
                Tags = new List<string> { "支付", "常见问题" }
            },
            new KnowledgeBaseEntry
            {
                Id = 4,
                Question = "配送时间和运费如何计算？",
                Answer = "配送说明：1. 一般地区1-3个工作日送达；2. 偏远地区3-7个工作日；3. 订单满99元免运费；4. 急件可选择次日达服务（需额外收费）。具体运费在结算时显示。",
                Keywords = new List<string> { "配送", "运费", "快递", "送货", "时间" },
                Category = "配送服务",
                Tags = new List<string> { "配送", "运费" }
            },
            new KnowledgeBaseEntry
            {
                Id = 5,
                Question = "如何联系客服？",
                Answer = "联系客服的方式：1. 在线客服：工作时间9:00-21:00；2. 客服热线：400-123-4567；3. 微信客服：关注公众号后回复\"人工客服\"；4. 邮箱：<EMAIL>。",
                Keywords = new List<string> { "客服", "联系", "电话", "人工", "在线" },
                Category = "客服服务",
                Tags = new List<string> { "联系方式", "客服" }
            }
        };
    }
}
