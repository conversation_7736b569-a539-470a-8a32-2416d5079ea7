{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "PersimmonChic.SearchService": "Debug"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "Per<PERSON><PERSON>on<PERSON><PERSON>_Super_Secret_Key_2024_Demo_Application", "Issuer": "PersimmonChic", "Audience": "PersimmonChic-Users", "ExpirationHours": 24}, "ConnectionStrings": {"Redis": "localhost:6379", "Database": "Data Source=search.db"}, "SearchSettings": {"EnableSemanticSearch": true, "EnableKeywordSearch": true, "EnableHybridSearch": true, "EnableFuzzySearch": true, "EnableAutoComplete": true, "EnableSearchCache": true, "DefaultSearchType": "Hybrid", "DefaultPageSize": 20, "MaxPageSize": 100, "MaxQueryLength": 1000, "SemanticSearchSettings": {"ModelPath": "Models/semantic_model.onnx", "VectorDimensions": 384, "SimilarityThreshold": 0.7, "MaxCandidates": 1000, "EnableVectorCache": true, "VectorCacheExpirationHours": 24}, "KeywordSearchSettings": {"EnableStemming": true, "EnableStopWords": true, "FuzzinessLevel": 2, "MinTermLength": 2, "MaxTerms": 50, "BoostFactors": {"title": 3.0, "description": 2.0, "content": 1.0, "tags": 1.5}}, "HybridSearchSettings": {"SemanticWeight": 0.6, "KeywordWeight": 0.4, "EnableDynamicWeighting": true, "ReRankingEnabled": true, "ReRankingThreshold": 0.5}, "AutoCompleteSettings": {"MaxSuggestions": 10, "MinQueryLength": 1, "EnablePopularSuggestions": true, "EnableHistorySuggestions": true, "EnableProductSuggestions": true, "SuggestionCacheExpirationMinutes": 30}, "FacetSettings": {"EnableFacets": true, "MaxFacetValues": 20, "DefaultFacets": ["category", "brand", "price_range"], "FacetCacheExpirationMinutes": 60}, "HighlightingSettings": {"EnableHighlighting": true, "MaxFragmentLength": 150, "MaxFragments": 3, "PreTag": "<em>", "PostTag": "</em>"}, "CacheSettings": {"EnableDistributedCache": true, "SearchResultCacheExpirationMinutes": 15, "AutoCompleteCacheExpirationMinutes": 30, "FacetCacheExpirationMinutes": 60, "MaxCacheSize": 10000, "EnableCacheWarmup": true}, "AnalyticsSettings": {"EnableSearchAnalytics": true, "EnableQueryLogging": true, "EnablePerformanceMetrics": true, "LogRetentionDays": 90, "EnableRealTimeAnalytics": true, "AnalyticsBatchSize": 100}}, "ServiceDiscovery": {"ServiceName": "SearchService", "Address": "localhost", "Port": 5007, "HealthCheckEndpoint": "/health", "GatewayUrl": "http://localhost:5000", "Tags": ["search", "semantic", "nlp", "autocomplete", "analytics"]}, "HealthChecks": {"UI": {"Enable": true, "Path": "/health-ui"}, "Checks": [{"Name": "Redis", "Type": "Redis", "ConnectionString": "localhost:6379"}, {"Name": "SemanticModel", "Type": "Custom", "Description": "语义搜索模型健康检查"}, {"Name": "SearchIndex", "Type": "Custom", "Description": "搜索索引健康检查"}]}, "Monitoring": {"EnableMetrics": true, "MetricsEndpoint": "/metrics", "EnableTracing": true, "TracingEndpoint": "/trace", "SampleRate": 0.1, "CustomMetrics": ["search_requests_total", "search_latency_seconds", "search_accuracy_rate", "cache_hit_rate", "autocomplete_requests_total", "semantic_similarity_scores"]}, "Security": {"EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5000", "http://localhost:5001"], "EnableRateLimiting": true, "RateLimitSettings": {"MaxRequestsPerMinute": 1000, "MaxRequestsPerHour": 10000, "EnablePerUserLimiting": true, "SearchRateLimit": 100, "AutoCompleteRateLimit": 200}, "EnableRequestValidation": true, "MaxRequestSize": 1048576, "EnableAuditLogging": true, "SensitiveDataMasking": true}, "Features": {"SemanticSearch": {"Enabled": true, "Description": "基于深度学习的语义搜索引擎"}, "KeywordSearch": {"Enabled": true, "Description": "传统关键词匹配搜索"}, "HybridSearch": {"Enabled": true, "Description": "语义搜索与关键词搜索的混合算法"}, "FuzzySearch": {"Enabled": true, "Description": "模糊匹配搜索，容错性强"}, "AutoComplete": {"Enabled": true, "Description": "智能自动补全和搜索建议"}, "SearchAnalytics": {"Enabled": true, "Description": "搜索行为分析和统计"}, "FacetedSearch": {"Enabled": true, "Description": "多维度分面搜索"}, "SearchHighlighting": {"Enabled": true, "Description": "搜索结果高亮显示"}, "TrendingQueries": {"Enabled": true, "Description": "热门搜索词分析"}, "PersonalizedSearch": {"Enabled": false, "Description": "个性化搜索结果（待实现）"}}, "Integration": {"ExternalServices": {"RecommendationService": {"BaseUrl": "http://localhost:5006", "Timeout": 5000, "RetryCount": 3}, "ProductService": {"BaseUrl": "http://localhost:5002", "Timeout": 5000, "RetryCount": 3}, "AnalyticsService": {"BaseUrl": "http://localhost:5009", "Timeout": 10000, "RetryCount": 2}}, "MessageQueue": {"Provider": "RabbitMQ", "ConnectionString": "amqp://localhost:5672", "ExchangeName": "search.events", "QueueName": "search.queries"}, "SearchEngines": {"Elasticsearch": {"Enabled": false, "Url": "http://localhost:9200", "IndexName": "products"}, "Solr": {"Enabled": false, "Url": "http://localhost:8983/solr", "CoreName": "products"}}}, "BusinessRules": {"SearchFilters": {"EnableCategoryFilter": true, "EnableBrandFilter": true, "EnablePriceFilter": true, "EnableAvailabilityFilter": true, "EnableRatingFilter": true}, "ResultRanking": {"EnablePopularityBoost": true, "PopularityBoostFactor": 1.2, "EnableRecencyBoost": true, "RecencyBoostFactor": 1.1, "EnablePersonalizationBoost": false, "PersonalizationBoostFactor": 1.3}, "QualityControl": {"MinResultQuality": 0.3, "MaxResultsPerPage": 100, "EnableSpellCheck": true, "EnableQueryExpansion": true, "EnableSynonymExpansion": false}}, "Performance": {"SearchTimeout": 30000, "IndexingTimeout": 300000, "MaxConcurrentSearches": 100, "EnableAsyncProcessing": true, "BatchProcessingSize": 1000, "EnableQueryOptimization": true, "CacheWarmupOnStartup": true}}