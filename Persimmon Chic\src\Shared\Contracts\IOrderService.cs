using PersimmonChic.Shared.Models;

namespace PersimmonChic.Shared.Contracts;

/// <summary>
/// 订单服务接口
/// </summary>
public interface IOrderService
{
    /// <summary>
    /// 创建订单
    /// </summary>
    /// <param name="request">创建订单请求</param>
    /// <returns>订单信息</returns>
    Task<ApiResponse<Order>> CreateOrderAsync(CreateOrderRequest request);

    /// <summary>
    /// 根据ID获取订单信息
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns>订单信息</returns>
    Task<ApiResponse<Order>> GetOrderByIdAsync(int orderId);

    /// <summary>
    /// 根据订单号获取订单信息
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>订单信息</returns>
    Task<ApiResponse<Order>> GetOrderByNumberAsync(string orderNumber);

    /// <summary>
    /// 获取用户订单列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="request">分页请求</param>
    /// <returns>订单列表</returns>
    Task<ApiResponse<PagedResponse<Order>>> GetUserOrdersAsync(int userId, PagedRequest request);

    /// <summary>
    /// 获取订单列表（管理员）
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>订单列表</returns>
    Task<ApiResponse<PagedResponse<Order>>> GetOrdersAsync(PagedRequest request);

    /// <summary>
    /// 根据状态获取订单列表
    /// </summary>
    /// <param name="status">订单状态</param>
    /// <param name="request">分页请求</param>
    /// <returns>订单列表</returns>
    Task<ApiResponse<PagedResponse<Order>>> GetOrdersByStatusAsync(OrderStatus status, PagedRequest request);

    /// <summary>
    /// 更新订单状态
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="status">新状态</param>
    /// <param name="notes">备注</param>
    /// <param name="operatorId">操作员ID</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<bool>> UpdateOrderStatusAsync(int orderId, OrderStatus status, string? notes = null, int? operatorId = null);

    /// <summary>
    /// 取消订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="reason">取消原因</param>
    /// <param name="operatorId">操作员ID</param>
    /// <returns>取消结果</returns>
    Task<ApiResponse<bool>> CancelOrderAsync(int orderId, string reason, int? operatorId = null);

    /// <summary>
    /// 确认付款
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="paymentMethod">支付方式</param>
    /// <param name="transactionId">交易ID</param>
    /// <returns>确认结果</returns>
    Task<ApiResponse<bool>> ConfirmPaymentAsync(int orderId, PaymentMethod paymentMethod, string? transactionId = null);

    /// <summary>
    /// 发货
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="trackingNumber">快递单号</param>
    /// <param name="carrier">承运商</param>
    /// <param name="operatorId">操作员ID</param>
    /// <returns>发货结果</returns>
    Task<ApiResponse<bool>> ShipOrderAsync(int orderId, string? trackingNumber = null, string? carrier = null, int? operatorId = null);

    /// <summary>
    /// 确认收货
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns>确认结果</returns>
    Task<ApiResponse<bool>> ConfirmDeliveryAsync(int orderId);

    /// <summary>
    /// 完成订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="operatorId">操作员ID</param>
    /// <returns>完成结果</returns>
    Task<ApiResponse<bool>> CompleteOrderAsync(int orderId, int? operatorId = null);

    /// <summary>
    /// 申请退款
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="reason">退款原因</param>
    /// <param name="amount">退款金额</param>
    /// <returns>申请结果</returns>
    Task<ApiResponse<bool>> RequestRefundAsync(int orderId, string reason, decimal? amount = null);

    /// <summary>
    /// 处理退款
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="approved">是否批准</param>
    /// <param name="notes">处理备注</param>
    /// <param name="operatorId">操作员ID</param>
    /// <returns>处理结果</returns>
    Task<ApiResponse<bool>> ProcessRefundAsync(int orderId, bool approved, string? notes = null, int? operatorId = null);

    /// <summary>
    /// 获取订单状态历史
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns>状态历史</returns>
    Task<ApiResponse<List<OrderStatusHistory>>> GetOrderStatusHistoryAsync(int orderId);

    /// <summary>
    /// 计算订单金额
    /// </summary>
    /// <param name="items">订单项列表</param>
    /// <param name="shippingAddress">收货地址</param>
    /// <returns>订单金额信息</returns>
    Task<ApiResponse<OrderCalculationResult>> CalculateOrderAsync(List<OrderItemRequest> items, string shippingAddress);

    /// <summary>
    /// 获取订单统计信息
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>统计信息</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetOrderStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取销售报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="groupBy">分组方式（day/week/month）</param>
    /// <returns>销售报表</returns>
    Task<ApiResponse<List<SalesReportItem>>> GetSalesReportAsync(DateTime startDate, DateTime endDate, string groupBy = "day");
}

/// <summary>
/// 订单金额计算结果
/// </summary>
public class OrderCalculationResult
{
    public decimal SubTotal { get; set; }
    public decimal ShippingFee { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public List<OrderItemCalculation> Items { get; set; } = new();
}

/// <summary>
/// 订单项计算结果
/// </summary>
public class OrderItemCalculation
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public decimal TotalPrice { get; set; }
    public bool IsAvailable { get; set; }
    public string? Message { get; set; }
}

/// <summary>
/// 销售报表项
/// </summary>
public class SalesReportItem
{
    public DateTime Date { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalAmount { get; set; }
    public int CustomerCount { get; set; }
    public decimal AverageOrderValue { get; set; }
}
