{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "PersimmonChic.RecommendationService": "Debug", "Microsoft.ML": "Information"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "Per<PERSON><PERSON>on<PERSON><PERSON>_Super_Secret_Key_2024_Demo_Application", "Issuer": "PersimmonChic", "Audience": "PersimmonChic-Users", "ExpirationHours": 24}, "ConnectionStrings": {"Redis": "localhost:6379", "Database": "Data Source=recommendation.db"}, "RecommendationSettings": {"EnableMLRecommendation": true, "EnableCollaborativeFiltering": true, "EnableContentBasedFiltering": true, "EnableHybridRecommendation": true, "EnableABTesting": true, "EnableRecommendationCache": true, "DefaultRecommendationCount": 10, "MaxRecommendationCount": 50, "CacheExpirationMinutes": 30, "ModelRetrainingIntervalHours": 24, "MLSettings": {"ModelPath": "Models/recommendation_model.zip", "TrainingDataMinSize": 100, "MatrixFactorizationRank": 100, "MaxIterations": 20, "EnableIncrementalLearning": false, "ModelValidationSplit": 0.2}, "CollaborativeFilteringSettings": {"MinSimilarUsers": 5, "MaxSimilarUsers": 50, "MinCommonItems": 2, "SimilarityThreshold": 0.1, "UserBasedWeight": 0.6, "ItemBasedWeight": 0.4}, "ContentBasedSettings": {"FeatureVectorSize": 100, "CategoryWeight": 0.3, "BrandWeight": 0.2, "TagWeight": 0.3, "PriceWeight": 0.1, "PopularityWeight": 0.1}, "HybridSettings": {"MLWeight": 0.4, "CollaborativeWeight": 0.3, "ContentWeight": 0.2, "PopularityWeight": 0.1, "EnableDynamicWeighting": true}, "ABTestSettings": {"DefaultTrafficSplit": 0.5, "MinTestDuration": 7, "MaxTestDuration": 30, "SignificanceLevel": 0.05, "MinSampleSize": 100}, "CacheSettings": {"EnableDistributedCache": true, "DefaultExpirationMinutes": 30, "UserRecommendationExpiration": 60, "PopularItemsExpiration": 120, "SimilarItemsExpiration": 240, "MaxCacheSize": 10000}, "PerformanceSettings": {"MaxConcurrentRecommendations": 100, "RecommendationTimeoutSeconds": 30, "EnablePerformanceMetrics": true, "MetricsRetentionDays": 30}}, "ServiceDiscovery": {"ServiceName": "RecommendationService", "Address": "localhost", "Port": 5006, "HealthCheckEndpoint": "/health", "GatewayUrl": "http://localhost:5000", "Tags": ["recommendation", "ml", "collaborative-filtering", "content-based", "abtest"]}, "HealthChecks": {"UI": {"Enable": true, "Path": "/health-ui"}, "Checks": [{"Name": "Redis", "Type": "Redis", "ConnectionString": "localhost:6379"}, {"Name": "MLModel", "Type": "Custom", "Description": "机器学习推荐模型健康检查"}, {"Name": "RecommendationEngine", "Type": "Custom", "Description": "推荐引擎健康检查"}]}, "Monitoring": {"EnableMetrics": true, "MetricsEndpoint": "/metrics", "EnableTracing": true, "TracingEndpoint": "/trace", "SampleRate": 0.1, "CustomMetrics": ["recommendations_generated_total", "recommendation_latency_seconds", "cache_hit_rate", "model_prediction_accuracy", "abtest_conversion_rate", "user_behavior_events_total"]}, "Security": {"EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5000", "http://localhost:5001"], "EnableRateLimiting": true, "RateLimitSettings": {"MaxRequestsPerMinute": 1000, "MaxRequestsPerHour": 10000, "EnablePerUserLimiting": true, "RecommendationRateLimit": 100}, "EnableRequestValidation": true, "MaxRequestSize": 1048576, "EnableAuditLogging": true, "SensitiveDataMasking": true}, "Features": {"PersonalizedRecommendation": {"Enabled": true, "Description": "基于用户行为和偏好的个性化推荐"}, "CollaborativeFiltering": {"Enabled": true, "Description": "基于用户和物品相似度的协同过滤推荐"}, "ContentBasedFiltering": {"Enabled": true, "Description": "基于内容特征的推荐算法"}, "MachineLearningRecommendation": {"Enabled": true, "Description": "基于ML.NET的矩阵分解推荐模型"}, "HybridRecommendation": {"Enabled": true, "Description": "多算法融合的混合推荐系统"}, "ABTesting": {"Enabled": true, "Description": "推荐算法A/B测试框架"}, "RealTimeBehaviorTracking": {"Enabled": true, "Description": "实时用户行为追踪和分析"}, "UserProfileManagement": {"Enabled": true, "Description": "用户画像构建和管理"}, "RecommendationCache": {"Enabled": true, "Description": "高性能推荐结果缓存"}, "PerformanceAnalytics": {"Enabled": true, "Description": "推荐效果分析和性能监控"}}, "Integration": {"ExternalServices": {"UserService": {"BaseUrl": "http://localhost:5001", "Timeout": 5000, "RetryCount": 3}, "ProductService": {"BaseUrl": "http://localhost:5002", "Timeout": 5000, "RetryCount": 3}, "AnalyticsService": {"BaseUrl": "http://localhost:5009", "Timeout": 10000, "RetryCount": 2}}, "MessageQueue": {"Provider": "RabbitMQ", "ConnectionString": "amqp://localhost:5672", "ExchangeName": "recommendation.events", "QueueName": "recommendation.behaviors"}, "DataPipeline": {"EnableRealTimeProcessing": true, "BatchProcessingInterval": 300, "MaxBatchSize": 1000, "EnableDataValidation": true}}, "BusinessRules": {"RecommendationFilters": {"EnablePriceFilter": true, "EnableCategoryFilter": true, "EnableBrandFilter": true, "EnableAvailabilityFilter": true, "EnableAgeRestrictionFilter": true}, "DiversitySettings": {"EnableDiversification": true, "MaxSameCategoryRatio": 0.6, "MaxSameBrandRatio": 0.4, "MinPriceVariation": 0.3}, "FreshnessSettings": {"EnableFreshnessBoost": true, "NewItemBoostFactor": 1.2, "TrendingItemBoostFactor": 1.1, "SeasonalBoostFactor": 1.15}}}