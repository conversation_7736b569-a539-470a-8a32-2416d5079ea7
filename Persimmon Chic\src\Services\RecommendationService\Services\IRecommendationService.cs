using PersimmonChic.RecommendationService.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.RecommendationService.Services;

/// <summary>
/// 推荐服务接口
/// </summary>
public interface IRecommendationService
{
    /// <summary>
    /// 获取个性化推荐
    /// </summary>
    /// <param name="request">推荐请求</param>
    /// <returns>推荐响应</returns>
    Task<ApiResponse<RecommendationResponse>> GetRecommendationsAsync(RecommendationRequest request);
    
    /// <summary>
    /// 获取相似产品推荐
    /// </summary>
    /// <param name="productId">产品ID</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐响应</returns>
    Task<ApiResponse<RecommendationResponse>> GetSimilarProductsAsync(int productId, int count = 10);
    
    /// <summary>
    /// 获取热门产品推荐
    /// </summary>
    /// <param name="category">产品类别</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐响应</returns>
    Task<ApiResponse<RecommendationResponse>> GetPopularProductsAsync(string? category = null, int count = 10);
    
    /// <summary>
    /// 记录用户行为
    /// </summary>
    /// <param name="behavior">用户行为数据</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> RecordUserBehaviorAsync(UserBehavior behavior);
    
    /// <summary>
    /// 提交推荐反馈
    /// </summary>
    /// <param name="feedback">推荐反馈</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> SubmitFeedbackAsync(RecommendationFeedback feedback);
    
    /// <summary>
    /// 更新用户画像
    /// </summary>
    /// <param name="profile">用户画像</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdateUserProfileAsync(UserProfile profile);
    
    /// <summary>
    /// 获取推荐算法性能指标
    /// </summary>
    /// <param name="algorithm">算法名称</param>
    /// <returns>性能指标</returns>
    Task<ApiResponse<RecommendationMetrics>> GetAlgorithmMetricsAsync(string? algorithm = null);
}

/// <summary>
/// 机器学习推荐引擎接口
/// </summary>
public interface IMLRecommendationEngine
{
    /// <summary>
    /// 训练推荐模型
    /// </summary>
    /// <param name="trainingData">训练数据</param>
    /// <returns>训练结果</returns>
    Task<ApiResponse<bool>> TrainModelAsync(List<RecommendationTrainingData> trainingData);
    
    /// <summary>
    /// 预测用户对产品的评分
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="productId">产品ID</param>
    /// <returns>预测评分</returns>
    Task<ApiResponse<float>> PredictRatingAsync(int userId, int productId);
    
    /// <summary>
    /// 获取用户推荐列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐列表</returns>
    Task<ApiResponse<List<RecommendationItem>>> GetUserRecommendationsAsync(int userId, int count = 10);
    
    /// <summary>
    /// 更新模型
    /// </summary>
    /// <param name="newData">新的训练数据</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<bool>> UpdateModelAsync(List<RecommendationTrainingData> newData);
    
    /// <summary>
    /// 获取模型性能指标
    /// </summary>
    /// <returns>性能指标</returns>
    Task<ApiResponse<Dictionary<string, double>>> GetModelMetricsAsync();
}

/// <summary>
/// 协同过滤推荐引擎接口
/// </summary>
public interface ICollaborativeFilteringEngine
{
    /// <summary>
    /// 基于用户的协同过滤推荐
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐列表</returns>
    Task<ApiResponse<List<RecommendationItem>>> GetUserBasedRecommendationsAsync(int userId, int count = 10);
    
    /// <summary>
    /// 基于物品的协同过滤推荐
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐列表</returns>
    Task<ApiResponse<List<RecommendationItem>>> GetItemBasedRecommendationsAsync(int userId, int count = 10);
    
    /// <summary>
    /// 计算用户相似度
    /// </summary>
    /// <param name="userId1">用户1 ID</param>
    /// <param name="userId2">用户2 ID</param>
    /// <returns>相似度分数</returns>
    Task<ApiResponse<float>> CalculateUserSimilarityAsync(int userId1, int userId2);
    
    /// <summary>
    /// 计算物品相似度
    /// </summary>
    /// <param name="productId1">产品1 ID</param>
    /// <param name="productId2">产品2 ID</param>
    /// <returns>相似度分数</returns>
    Task<ApiResponse<float>> CalculateItemSimilarityAsync(int productId1, int productId2);
}

/// <summary>
/// 内容过滤推荐引擎接口
/// </summary>
public interface IContentBasedEngine
{
    /// <summary>
    /// 基于内容的推荐
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">推荐数量</param>
    /// <returns>推荐列表</returns>
    Task<ApiResponse<List<RecommendationItem>>> GetContentBasedRecommendationsAsync(int userId, int count = 10);
    
    /// <summary>
    /// 构建产品特征向量
    /// </summary>
    /// <param name="productId">产品ID</param>
    /// <returns>特征向量</returns>
    Task<ApiResponse<List<float>>> BuildProductFeatureVectorAsync(int productId);
    
    /// <summary>
    /// 构建用户偏好向量
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>偏好向量</returns>
    Task<ApiResponse<List<float>>> BuildUserPreferenceVectorAsync(int userId);
    
    /// <summary>
    /// 计算内容相似度
    /// </summary>
    /// <param name="vector1">向量1</param>
    /// <param name="vector2">向量2</param>
    /// <returns>相似度分数</returns>
    Task<ApiResponse<float>> CalculateContentSimilarityAsync(List<float> vector1, List<float> vector2);
}

/// <summary>
/// A/B测试服务接口
/// </summary>
public interface IABTestService
{
    /// <summary>
    /// 创建A/B测试
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>创建结果</returns>
    Task<ApiResponse<ABTestConfig>> CreateABTestAsync(ABTestConfig config);
    
    /// <summary>
    /// 获取用户的测试变体
    /// </summary>
    /// <param name="testName">测试名称</param>
    /// <param name="userId">用户ID</param>
    /// <returns>测试变体</returns>
    Task<ApiResponse<ABTestVariant>> GetUserVariantAsync(string testName, int userId);
    
    /// <summary>
    /// 记录A/B测试指标
    /// </summary>
    /// <param name="testName">测试名称</param>
    /// <param name="variant">变体名称</param>
    /// <param name="metric">指标名称</param>
    /// <param name="value">指标值</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> RecordMetricAsync(string testName, string variant, string metric, double value);
    
    /// <summary>
    /// 获取A/B测试结果
    /// </summary>
    /// <param name="testName">测试名称</param>
    /// <returns>测试结果</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetTestResultsAsync(string testName);
    
    /// <summary>
    /// 停止A/B测试
    /// </summary>
    /// <param name="testName">测试名称</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> StopABTestAsync(string testName);
}

/// <summary>
/// 推荐缓存服务接口
/// </summary>
public interface IRecommendationCacheService
{
    /// <summary>
    /// 获取缓存的推荐
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>推荐响应</returns>
    Task<RecommendationResponse?> GetCachedRecommendationAsync(string cacheKey);
    
    /// <summary>
    /// 设置推荐缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="recommendation">推荐响应</param>
    /// <param name="expiration">过期时间</param>
    /// <returns>操作结果</returns>
    Task<bool> SetRecommendationCacheAsync(string cacheKey, RecommendationResponse recommendation, TimeSpan? expiration = null);
    
    /// <summary>
    /// 删除推荐缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <returns>操作结果</returns>
    Task<bool> RemoveRecommendationCacheAsync(string cacheKey);
    
    /// <summary>
    /// 清除用户相关的推荐缓存
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    Task<bool> ClearUserRecommendationCacheAsync(int userId);
    
    /// <summary>
    /// 生成推荐缓存键
    /// </summary>
    /// <param name="request">推荐请求</param>
    /// <returns>缓存键</returns>
    string GenerateRecommendationCacheKey(RecommendationRequest request);
}
