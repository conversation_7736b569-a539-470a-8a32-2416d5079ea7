using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.Shared.Models;

/// <summary>
/// 订单实体模型
/// </summary>
public class Order
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string OrderNumber { get; set; } = string.Empty;
    
    public int UserId { get; set; }
    
    public OrderStatus Status { get; set; } = OrderStatus.Pending;
    
    [Required]
    [Range(0.01, double.MaxValue)]
    public decimal TotalAmount { get; set; }
    
    public decimal ShippingFee { get; set; }
    
    public decimal TaxAmount { get; set; }
    
    public decimal DiscountAmount { get; set; }
    
    public decimal FinalAmount { get; set; }
    
    public PaymentMethod PaymentMethod { get; set; }
    
    public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public DateTime? PaidAt { get; set; }
    
    public DateTime? ShippedAt { get; set; }
    
    public DateTime? DeliveredAt { get; set; }
    
    public DateTime? CancelledAt { get; set; }
    
    [StringLength(500)]
    public string? Notes { get; set; }
    
    // 收货地址信息
    [Required]
    [StringLength(100)]
    public string ShippingName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(20)]
    public string ShippingPhone { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string ShippingAddress { get; set; } = string.Empty;
    
    [StringLength(50)]
    public string ShippingCity { get; set; } = string.Empty;
    
    [StringLength(50)]
    public string ShippingProvince { get; set; } = string.Empty;
    
    [StringLength(20)]
    public string ShippingPostalCode { get; set; } = string.Empty;
    
    // 导航属性
    public virtual User User { get; set; } = null!;
    public virtual ICollection<OrderItem> Items { get; set; } = new List<OrderItem>();
    public virtual ICollection<OrderStatusHistory> StatusHistory { get; set; } = new List<OrderStatusHistory>();
}

/// <summary>
/// 订单状态枚举
/// </summary>
public enum OrderStatus
{
    Pending = 0,        // 待付款
    Paid = 1,           // 已付款
    Processing = 2,     // 处理中
    Shipped = 3,        // 已发货
    Delivered = 4,      // 已送达
    Completed = 5,      // 已完成
    Cancelled = 6,      // 已取消
    Refunded = 7        // 已退款
}

/// <summary>
/// 支付方式枚举
/// </summary>
public enum PaymentMethod
{
    CreditCard = 0,
    DebitCard = 1,
    PayPal = 2,
    Alipay = 3,
    WeChatPay = 4,
    BankTransfer = 5,
    CashOnDelivery = 6
}

/// <summary>
/// 支付状态枚举
/// </summary>
public enum PaymentStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4,
    Refunded = 5
}

/// <summary>
/// 订单项模型
/// </summary>
public class OrderItem
{
    public int Id { get; set; }
    
    public int OrderId { get; set; }
    
    public int ProductId { get; set; }
    
    [Required]
    [StringLength(200)]
    public string ProductName { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string ProductSKU { get; set; } = string.Empty;
    
    [Required]
    [Range(0.01, double.MaxValue)]
    public decimal UnitPrice { get; set; }
    
    [Required]
    [Range(1, int.MaxValue)]
    public int Quantity { get; set; }
    
    public decimal TotalPrice { get; set; }
    
    [StringLength(500)]
    public string? ProductImage { get; set; }
    
    // 导航属性
    public virtual Order Order { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
}

/// <summary>
/// 订单状态历史模型
/// </summary>
public class OrderStatusHistory
{
    public int Id { get; set; }
    
    public int OrderId { get; set; }
    
    public OrderStatus Status { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    [StringLength(500)]
    public string? Notes { get; set; }
    
    public int? OperatorId { get; set; }
    
    [StringLength(100)]
    public string? OperatorName { get; set; }
    
    // 导航属性
    public virtual Order Order { get; set; } = null!;
}

/// <summary>
/// 创建订单请求模型
/// </summary>
public class CreateOrderRequest
{
    public int UserId { get; set; }
    
    public List<OrderItemRequest> Items { get; set; } = new();
    
    public PaymentMethod PaymentMethod { get; set; }
    
    // 收货地址信息
    [Required]
    public string ShippingName { get; set; } = string.Empty;
    
    [Required]
    public string ShippingPhone { get; set; } = string.Empty;
    
    [Required]
    public string ShippingAddress { get; set; } = string.Empty;
    
    public string ShippingCity { get; set; } = string.Empty;
    
    public string ShippingProvince { get; set; } = string.Empty;
    
    public string ShippingPostalCode { get; set; } = string.Empty;
    
    public string? Notes { get; set; }
}

/// <summary>
/// 订单项请求模型
/// </summary>
public class OrderItemRequest
{
    public int ProductId { get; set; }
    
    [Range(1, int.MaxValue)]
    public int Quantity { get; set; }
}
