using System.ComponentModel.DataAnnotations;
using Microsoft.ML.Data;

namespace PersimmonChic.RecommendationService.Models;

/// <summary>
/// 用户行为数据
/// </summary>
public class UserBehavior
{
    public int Id { get; set; }
    
    [Required]
    public int UserId { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public BehaviorType BehaviorType { get; set; }
    
    public float Rating { get; set; } = 0f;
    
    public int Duration { get; set; } = 0; // 浏览时长(秒)
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 产品特征数据
/// </summary>
public class ProductFeature
{
    public int Id { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public string Category { get; set; } = string.Empty;
    
    public string Brand { get; set; } = string.Empty;
    
    public decimal Price { get; set; }
    
    public float PopularityScore { get; set; } = 0f;
    
    public List<string> Tags { get; set; } = new();
    
    public Dictionary<string, object> Attributes { get; set; } = new();
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 用户画像数据
/// </summary>
public class UserProfile
{
    public int Id { get; set; }
    
    [Required]
    public int UserId { get; set; }
    
    public int Age { get; set; }
    
    public string Gender { get; set; } = string.Empty;
    
    public string Location { get; set; } = string.Empty;
    
    public List<string> Interests { get; set; } = new();
    
    public List<string> PreferredCategories { get; set; } = new();
    
    public List<string> PreferredBrands { get; set; } = new();
    
    public decimal AverageOrderValue { get; set; }
    
    public int PurchaseFrequency { get; set; }
    
    public DateTime LastActiveAt { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Preferences { get; set; } = new();
}

/// <summary>
/// 推荐请求
/// </summary>
public class RecommendationRequest
{
    [Required]
    public int UserId { get; set; }
    
    public int Count { get; set; } = 10;
    
    public RecommendationType Type { get; set; } = RecommendationType.Personalized;
    
    public List<int> ExcludeProductIds { get; set; } = new();
    
    public string? Category { get; set; }
    
    public decimal? MinPrice { get; set; }
    
    public decimal? MaxPrice { get; set; }
    
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 推荐响应
/// </summary>
public class RecommendationResponse
{
    public int UserId { get; set; }
    
    public List<RecommendationItem> Items { get; set; } = new();
    
    public RecommendationType Type { get; set; }
    
    public string Algorithm { get; set; } = string.Empty;
    
    public float ConfidenceScore { get; set; }
    
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 推荐项目
/// </summary>
public class RecommendationItem
{
    public int ProductId { get; set; }
    
    public string ProductName { get; set; } = string.Empty;
    
    public float Score { get; set; }
    
    public float Confidence { get; set; }
    
    public string Reason { get; set; } = string.Empty;
    
    public int Rank { get; set; }
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// A/B测试配置
/// </summary>
public class ABTestConfig
{
    public int Id { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public List<ABTestVariant> Variants { get; set; } = new();
    
    public float TrafficSplit { get; set; } = 0.5f; // 流量分配比例
    
    public bool IsActive { get; set; } = true;
    
    public DateTime StartDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? EndDate { get; set; }
    
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// A/B测试变体
/// </summary>
public class ABTestVariant
{
    public string Name { get; set; } = string.Empty;
    
    public string Algorithm { get; set; } = string.Empty;
    
    public float Weight { get; set; } = 0.5f;
    
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 推荐反馈
/// </summary>
public class RecommendationFeedback
{
    public int Id { get; set; }
    
    [Required]
    public int UserId { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public FeedbackType FeedbackType { get; set; }
    
    public float? Rating { get; set; }
    
    public string? Comment { get; set; }
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// ML.NET 训练数据模型
/// </summary>
public class RecommendationTrainingData
{
    [KeyType(count: 10000)]
    public uint UserId { get; set; }
    
    [KeyType(count: 10000)]
    public uint ProductId { get; set; }
    
    public float Rating { get; set; }
}

/// <summary>
/// ML.NET 预测结果模型
/// </summary>
public class RecommendationPrediction
{
    public float Score { get; set; }
}

/// <summary>
/// 协同过滤数据模型
/// </summary>
public class CollaborativeFilteringData
{
    public int UserId { get; set; }
    
    public int ProductId { get; set; }
    
    public float Rating { get; set; }
    
    public float Confidence { get; set; }
    
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 内容过滤数据模型
/// </summary>
public class ContentBasedData
{
    public int ProductId { get; set; }
    
    public List<float> FeatureVector { get; set; } = new();
    
    public Dictionary<string, float> CategoryWeights { get; set; } = new();
    
    public Dictionary<string, float> AttributeWeights { get; set; } = new();
}

/// <summary>
/// 推荐算法性能指标
/// </summary>
public class RecommendationMetrics
{
    public string Algorithm { get; set; } = string.Empty;
    
    public float Precision { get; set; }
    
    public float Recall { get; set; }
    
    public float F1Score { get; set; }
    
    public float NDCG { get; set; } // Normalized Discounted Cumulative Gain
    
    public float Coverage { get; set; }
    
    public float Diversity { get; set; }
    
    public float Novelty { get; set; }
    
    public int TotalRecommendations { get; set; }
    
    public int SuccessfulRecommendations { get; set; }
    
    public DateTime EvaluatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 行为类型枚举
/// </summary>
public enum BehaviorType
{
    View = 1,
    Click = 2,
    AddToCart = 3,
    Purchase = 4,
    Like = 5,
    Share = 6,
    Review = 7,
    Search = 8
}

/// <summary>
/// 推荐类型枚举
/// </summary>
public enum RecommendationType
{
    Personalized = 1,
    Popular = 2,
    Similar = 3,
    Collaborative = 4,
    ContentBased = 5,
    Hybrid = 6,
    Trending = 7,
    NewArrivals = 8
}

/// <summary>
/// 反馈类型枚举
/// </summary>
public enum FeedbackType
{
    Positive = 1,
    Negative = 2,
    Neutral = 3,
    Click = 4,
    Purchase = 5,
    Ignore = 6
}
