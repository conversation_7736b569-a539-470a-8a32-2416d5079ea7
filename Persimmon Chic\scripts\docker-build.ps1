# Persimmon Chic Docker 构建脚本
param(
    [string]$Service = "all",
    [string]$Environment = "production",
    [switch]$NoBuild,
    [switch]$Push,
    [string]$Registry = "persimmonchic"
)

Write-Host "=== Persimmon Chic Docker 构建脚本 ===" -ForegroundColor Green
Write-Host "服务: $Service" -ForegroundColor Yellow
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host ""

# 服务配置
$services = @{
    "gateway" = @{
        "Name" = "API Gateway"
        "DockerfilePath" = "src/Gateway/Dockerfile"
        "ImageName" = "persimmonchic-gateway"
        "Context" = "."
    }
    "user-service" = @{
        "Name" = "User Service"
        "DockerfilePath" = "src/Services/UserService/Dockerfile"
        "ImageName" = "persimmonchic-user-service"
        "Context" = "."
    }
    "risk-control-service" = @{
        "Name" = "Risk Control Service"
        "DockerfilePath" = "src/Services/RiskControlService/Dockerfile"
        "ImageName" = "persimmonchic-risk-control-service"
        "Context" = "."
    }
    "pricing-service" = @{
        "Name" = "Pricing Service"
        "DockerfilePath" = "src/Services/PricingService/Dockerfile"
        "ImageName" = "persimmonchic-pricing-service"
        "Context" = "."
    }
    "recommendation-service" = @{
        "Name" = "Recommendation Service"
        "DockerfilePath" = "src/Services/RecommendationService/Dockerfile"
        "ImageName" = "persimmonchic-recommendation-service"
        "Context" = "."
    }
    "search-service" = @{
        "Name" = "Search Service"
        "DockerfilePath" = "src/Services/SearchService/Dockerfile"
        "ImageName" = "persimmonchic-search-service"
        "Context" = "."
    }
    "customer-service-bot" = @{
        "Name" = "AI Customer Service Bot"
        "DockerfilePath" = "src/Services/CustomerServiceBot/Dockerfile"
        "ImageName" = "persimmonchic-customer-service-bot"
        "Context" = "."
    }
}

function Build-DockerImage {
    param(
        [string]$ServiceKey,
        [hashtable]$ServiceConfig
    )
    
    $serviceName = $ServiceConfig.Name
    $dockerfilePath = $ServiceConfig.DockerfilePath
    $imageName = $ServiceConfig.ImageName
    $context = $ServiceConfig.Context
    
    Write-Host "构建服务: $serviceName" -ForegroundColor Yellow
    Write-Host "  Dockerfile: $dockerfilePath" -ForegroundColor Gray
    Write-Host "  镜像名称: $imageName" -ForegroundColor Gray
    
    # 检查Dockerfile是否存在
    if (-not (Test-Path $dockerfilePath)) {
        Write-Host "  错误: Dockerfile不存在: $dockerfilePath" -ForegroundColor Red
        return $false
    }
    
    try {
        # 生成标签
        $tag = if ($Environment -eq "development") { "dev" } else { "latest" }
        $fullImageName = "$Registry/$imageName`:$tag"
        
        Write-Host "  开始构建镜像: $fullImageName" -ForegroundColor Cyan
        
        # 构建Docker镜像
        $buildArgs = @(
            "build",
            "-f", $dockerfilePath,
            "-t", $fullImageName,
            $context
        )
        
        if ($NoBuild) {
            $buildArgs += "--no-cache"
        }
        
        $buildResult = & docker @buildArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 构建成功: $fullImageName" -ForegroundColor Green
            
            # 推送到仓库
            if ($Push) {
                Write-Host "  推送镜像到仓库..." -ForegroundColor Cyan
                & docker push $fullImageName
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "  ✓ 推送成功" -ForegroundColor Green
                } else {
                    Write-Host "  ✗ 推送失败" -ForegroundColor Red
                    return $false
                }
            }
            
            return $true
        } else {
            Write-Host "  ✗ 构建失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 构建异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主构建逻辑
$buildResults = @()

if ($Service -eq "all") {
    Write-Host "开始构建所有服务..." -ForegroundColor Green
    Write-Host ""
    
    foreach ($serviceKey in $services.Keys) {
        $result = Build-DockerImage -ServiceKey $serviceKey -ServiceConfig $services[$serviceKey]
        $buildResults += @{
            "Service" = $services[$serviceKey].Name
            "Success" = $result
        }
        Write-Host ""
    }
} else {
    if ($services.ContainsKey($Service)) {
        Write-Host "开始构建服务: $Service" -ForegroundColor Green
        Write-Host ""
        
        $result = Build-DockerImage -ServiceKey $Service -ServiceConfig $services[$Service]
        $buildResults += @{
            "Service" = $services[$Service].Name
            "Success" = $result
        }
    } else {
        Write-Host "错误: 未找到服务 '$Service'" -ForegroundColor Red
        Write-Host "可用服务: $($services.Keys -join ', ')" -ForegroundColor Yellow
        exit 1
    }
}

# 显示构建总结
Write-Host ""
Write-Host "=== 构建总结 ===" -ForegroundColor Green
Write-Host ""

$successCount = 0
$totalCount = $buildResults.Count

foreach ($result in $buildResults) {
    $status = if ($result.Success) { "✓" } else { "✗" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    
    Write-Host "$status $($result.Service)" -ForegroundColor $color
    
    if ($result.Success) {
        $successCount++
    }
}

Write-Host ""
$overallStatus = if ($successCount -eq $totalCount) { "✓ 全部成功" } else { "⚠ 部分失败" }
$overallColor = if ($successCount -eq $totalCount) { "Green" } else { "Yellow" }

Write-Host "构建结果: $overallStatus ($successCount/$totalCount)" -ForegroundColor $overallColor

# 提供使用建议
Write-Host ""
Write-Host "=== 使用建议 ===" -ForegroundColor Cyan
Write-Host "1. 使用 docker images 查看构建的镜像"
Write-Host "2. 使用 docker-compose up 启动服务"
Write-Host "3. 使用以下命令构建单个服务:"
Write-Host "   .\docker-build.ps1 -Service gateway"
Write-Host "   .\docker-build.ps1 -Service user-service -Push"
Write-Host ""

# 退出代码
if ($successCount -eq $totalCount) {
    Write-Host "所有镜像构建成功！" -ForegroundColor Green
    exit 0
} else {
    Write-Host "部分镜像构建失败，请检查错误信息。" -ForegroundColor Yellow
    exit 1
}
