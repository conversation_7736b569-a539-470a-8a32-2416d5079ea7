# Persimmon Chic 简化启动脚本

Write-Host "启动 Persimmon Chic 微服务演示..." -ForegroundColor Green
Write-Host ""

# 检查 .NET SDK
try {
    $dotnetVersion = & "C:\Program Files\dotnet\dotnet.exe" --version
    Write-Host "✓ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ 未找到 .NET SDK，请先安装 .NET 9.0 SDK" -ForegroundColor Red
    exit 1
}

# 设置工作目录
$rootPath = Split-Path -Parent $PSScriptRoot
Set-Location $rootPath

Write-Host "工作目录: $rootPath" -ForegroundColor Cyan
Write-Host ""

# 构建项目
Write-Host "构建项目..." -ForegroundColor Yellow

$projectsToBuild = @(
    "src/Shared/Models/PersimmonChic.Shared.Models.csproj",
    "src/Shared/Contracts/PersimmonChic.Shared.Contracts.csproj", 
    "src/Shared/Common/PersimmonChic.Shared.Common.csproj",
    "src/Infrastructure/DataAccess/PersimmonChic.Infrastructure.DataAccess.csproj",
    "src/Gateway/PersimmonChic.Gateway.csproj",
    "src/Services/UserService/PersimmonChic.UserService.csproj"
)

foreach ($project in $projectsToBuild) {
    Write-Host "  构建 $project..." -ForegroundColor Gray
    try {
        & "C:\Program Files\dotnet\dotnet.exe" build $project --configuration Debug --verbosity quiet
        if ($LASTEXITCODE -ne 0) {
            throw "构建失败"
        }
    } catch {
        Write-Host "✗ 构建 $project 失败" -ForegroundColor Red
        Write-Host "请检查项目依赖和代码错误" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "✓ 所有项目构建成功" -ForegroundColor Green
Write-Host ""

# 启动服务
Write-Host "启动服务..." -ForegroundColor Yellow

# 启动 API Gateway
Write-Host "启动 API Gateway (端口 5000)..." -ForegroundColor Magenta
$gatewayJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Gateway/PersimmonChic.Gateway.csproj --configuration Debug
}

Start-Sleep -Seconds 3

# 启动用户服务
Write-Host "启动用户服务 (端口 5001)..." -ForegroundColor Blue
$userServiceJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Services/UserService/PersimmonChic.UserService.csproj --configuration Debug
}

Write-Host ""
Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# 检查服务状态
Write-Host ""
Write-Host "检查服务状态..." -ForegroundColor Cyan

$services = @(
    @{ Name = "API Gateway"; Url = "http://localhost:5000/health" },
    @{ Name = "用户服务"; Url = "http://localhost:5001/health" }
)

foreach ($service in $services) {
    try {
        $response = Invoke-RestMethod -Uri $service.Url -Method Get -TimeoutSec 5
        Write-Host "✓ $($service.Name): 运行正常" -ForegroundColor Green
    } catch {
        Write-Host "! $($service.Name): 可能未完全启动 ($($service.Url))" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Persimmon Chic 微服务演示启动完成!" -ForegroundColor Green
Write-Host ""
Write-Host "服务访问地址:" -ForegroundColor Cyan
Write-Host "   • API Gateway: http://localhost:5000" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5000" -ForegroundColor Gray
Write-Host "     - 服务列表: http://localhost:5000/services" -ForegroundColor Gray
Write-Host "   • 用户服务: http://localhost:5001" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5001" -ForegroundColor Gray

Write-Host ""
Write-Host "测试 API:" -ForegroundColor Yellow
Write-Host "   • 登录测试: POST http://localhost:5000/api/users/login" -ForegroundColor Gray
Write-Host "     用户名: admin, 密码: 123456" -ForegroundColor Gray
Write-Host "   • 获取用户: GET http://localhost:5000/api/users/1" -ForegroundColor Gray

Write-Host ""
Write-Host "提示:" -ForegroundColor Yellow
Write-Host "   • 按 Ctrl+C 停止所有服务" -ForegroundColor Gray
Write-Host "   • 查看实时日志请检查 PowerShell 作业" -ForegroundColor Gray
Write-Host "   • API 文档可通过 Swagger UI 访问" -ForegroundColor Gray

Write-Host ""
Write-Host "打开浏览器访问 API Gateway..." -ForegroundColor Cyan

# 尝试打开浏览器
try {
    Start-Process "http://localhost:5000"
} catch {
    Write-Host "无法自动打开浏览器，请手动访问: http://localhost:5000" -ForegroundColor Yellow
}

# 等待用户输入来停止服务
Write-Host ""
Write-Host "按任意键停止所有服务..." -ForegroundColor Red
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 停止所有作业
Write-Host ""
Write-Host "停止所有服务..." -ForegroundColor Red

Stop-Job -Job $gatewayJob -ErrorAction SilentlyContinue
Remove-Job -Job $gatewayJob -ErrorAction SilentlyContinue
Write-Host "✓ 已停止 API Gateway" -ForegroundColor Green

Stop-Job -Job $userServiceJob -ErrorAction SilentlyContinue  
Remove-Job -Job $userServiceJob -ErrorAction SilentlyContinue
Write-Host "✓ 已停止用户服务" -ForegroundColor Green

Write-Host ""
Write-Host "Persimmon Chic 微服务演示已停止" -ForegroundColor Green
Write-Host ""
Write-Host "更多信息请查看 README.md 文件" -ForegroundColor Cyan
