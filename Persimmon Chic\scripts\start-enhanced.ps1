# Persimmon Chic Enhanced Microservices Startup Script
# 启动增强版微服务架构演示

Write-Host "Starting Persimmon Chic Enhanced Microservices Demo..." -ForegroundColor Green
Write-Host ""

# Check .NET SDK
try {
    $dotnetVersion = & "C:\Program Files\dotnet\dotnet.exe" --version
    Write-Host "✓ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET SDK not found, please install .NET 9.0 SDK first" -ForegroundColor Red
    exit 1
}

# Set working directory
$rootPath = Split-Path -Parent $PSScriptRoot
Set-Location $rootPath

Write-Host "Working Directory: $rootPath" -ForegroundColor Cyan
Write-Host ""

# Build projects
Write-Host "Building enhanced microservices..." -ForegroundColor Yellow

$projectsToBuild = @(
    "src/Shared/Models/PersimmonChic.Shared.Models.csproj",
    "src/Shared/Contracts/PersimmonChic.Shared.Contracts.csproj", 
    "src/Shared/Common/PersimmonChic.Shared.Common.csproj",
    "src/Infrastructure/DataAccess/PersimmonChic.Infrastructure.DataAccess.csproj",
    "src/Gateway/PersimmonChic.Gateway.csproj",
    "src/Services/UserService/PersimmonChic.UserService.csproj",
    "src/Services/RiskControlService/PersimmonChic.RiskControlService.csproj",
    "src/Services/PricingService/PersimmonChic.PricingService.csproj"
)

foreach ($project in $projectsToBuild) {
    Write-Host "  Building $project..." -ForegroundColor Gray
    try {
        & "C:\Program Files\dotnet\dotnet.exe" build $project --configuration Debug --verbosity quiet
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed"
        }
    } catch {
        Write-Host "✗ Failed to build $project" -ForegroundColor Red
        Write-Host "Please check project dependencies and code errors" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "✓ All projects built successfully" -ForegroundColor Green
Write-Host ""

# Start services
Write-Host "Starting enhanced microservices..." -ForegroundColor Yellow

# Start API Gateway
Write-Host "Starting API Gateway (port 5000)..." -ForegroundColor Magenta
$gatewayJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Gateway/PersimmonChic.Gateway.csproj --configuration Debug
}

Start-Sleep -Seconds 3

# Start User Service
Write-Host "Starting User Service (port 5001)..." -ForegroundColor Blue
$userServiceJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Services/UserService/PersimmonChic.UserService.csproj --configuration Debug
}

Start-Sleep -Seconds 2

# Start Risk Control Service
Write-Host "Starting Risk Control Service (port 5004)..." -ForegroundColor Red
$riskControlJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    $env:ASPNETCORE_URLS = "http://localhost:5004"
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Services/RiskControlService/PersimmonChic.RiskControlService.csproj --configuration Debug
}

Start-Sleep -Seconds 2

# Start Pricing Service
Write-Host "Starting Pricing Service (port 5005)..." -ForegroundColor Green
$pricingServiceJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    $env:ASPNETCORE_URLS = "http://localhost:5005"
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Services/PricingService/PersimmonChic.PricingService.csproj --configuration Debug
}

Write-Host ""
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service status
Write-Host ""
Write-Host "Checking service status..." -ForegroundColor Cyan

$services = @(
    @{ Name = "API Gateway"; Url = "http://localhost:5000/health" },
    @{ Name = "User Service"; Url = "http://localhost:5001/health" },
    @{ Name = "Risk Control Service"; Url = "http://localhost:5004/health" },
    @{ Name = "Pricing Service"; Url = "http://localhost:5005/health" }
)

foreach ($service in $services) {
    try {
        $response = Invoke-RestMethod -Uri $service.Url -Method Get -TimeoutSec 5
        Write-Host "✓ $($service.Name): Running" -ForegroundColor Green
    } catch {
        Write-Host "! $($service.Name): May not be fully started ($($service.Url))" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Persimmon Chic Enhanced Microservices Demo Started!" -ForegroundColor Green
Write-Host ""
Write-Host "Service Access URLs:" -ForegroundColor Cyan
Write-Host "   • API Gateway: http://localhost:5000" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5000" -ForegroundColor Gray
Write-Host "     - Service List: http://localhost:5000/services" -ForegroundColor Gray
Write-Host "   • User Service: http://localhost:5001" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5001" -ForegroundColor Gray
Write-Host "   • Risk Control Service: http://localhost:5004" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5004" -ForegroundColor Gray
Write-Host "     - SignalR Hub: http://localhost:5004/hubs/risk-monitor" -ForegroundColor Gray
Write-Host "   • Pricing Service: http://localhost:5005" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5005" -ForegroundColor Gray

Write-Host ""
Write-Host "Enhanced Features:" -ForegroundColor Yellow
Write-Host "   • Real-time Risk Monitoring with ML" -ForegroundColor Gray
Write-Host "   • Dynamic Pricing Engine" -ForegroundColor Gray
Write-Host "   • Channel-based Pricing" -ForegroundColor Gray
Write-Host "   • User Tier Pricing" -ForegroundColor Gray
Write-Host "   • Device Fingerprinting" -ForegroundColor Gray
Write-Host "   • Anomaly Detection" -ForegroundColor Gray
Write-Host "   • Price Change Notifications" -ForegroundColor Gray
Write-Host "   • Redis Caching" -ForegroundColor Gray

Write-Host ""
Write-Host "Test APIs:" -ForegroundColor Yellow
Write-Host "   • User Login: POST http://localhost:5000/api/users/login" -ForegroundColor Gray
Write-Host "     Username: admin, Password: 123456" -ForegroundColor Gray
Write-Host "   • Risk Assessment: POST http://localhost:5004/api/riskcontrol/evaluate" -ForegroundColor Gray
Write-Host "   • Price Calculation: POST http://localhost:5005/api/pricing/calculate" -ForegroundColor Gray
Write-Host "   • Get Product Price: GET http://localhost:5005/api/pricing/product/1" -ForegroundColor Gray

Write-Host ""
Write-Host "Tips:" -ForegroundColor Yellow
Write-Host "   • Press Ctrl+C to stop all services" -ForegroundColor Gray
Write-Host "   • Check PowerShell jobs for real-time logs" -ForegroundColor Gray
Write-Host "   • API documentation available via Swagger UI" -ForegroundColor Gray
Write-Host "   • Risk monitoring dashboard at SignalR hub" -ForegroundColor Gray

Write-Host ""
Write-Host "Opening browser to access API Gateway..." -ForegroundColor Cyan

# Try to open browser
try {
    Start-Process "http://localhost:5000"
} catch {
    Write-Host "Cannot auto-open browser, please visit manually: http://localhost:5000" -ForegroundColor Yellow
}

# Wait for user input to stop services
Write-Host ""
Write-Host "Press any key to stop all services..." -ForegroundColor Red
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop all jobs
Write-Host ""
Write-Host "Stopping all services..." -ForegroundColor Red

Stop-Job -Job $gatewayJob -ErrorAction SilentlyContinue
Remove-Job -Job $gatewayJob -ErrorAction SilentlyContinue
Write-Host "✓ Stopped API Gateway" -ForegroundColor Green

Stop-Job -Job $userServiceJob -ErrorAction SilentlyContinue  
Remove-Job -Job $userServiceJob -ErrorAction SilentlyContinue
Write-Host "✓ Stopped User Service" -ForegroundColor Green

Stop-Job -Job $riskControlJob -ErrorAction SilentlyContinue
Remove-Job -Job $riskControlJob -ErrorAction SilentlyContinue
Write-Host "✓ Stopped Risk Control Service" -ForegroundColor Green

Stop-Job -Job $pricingServiceJob -ErrorAction SilentlyContinue
Remove-Job -Job $pricingServiceJob -ErrorAction SilentlyContinue
Write-Host "✓ Stopped Pricing Service" -ForegroundColor Green

Write-Host ""
Write-Host "Persimmon Chic Enhanced Microservices Demo Stopped" -ForegroundColor Green
Write-Host ""
Write-Host "Architecture Summary:" -ForegroundColor Cyan
Write-Host "   • 4 Microservices successfully demonstrated" -ForegroundColor Gray
Write-Host "   • API Gateway with routing and authentication" -ForegroundColor Gray
Write-Host "   • User management with JWT tokens" -ForegroundColor Gray
Write-Host "   • Real-time risk control with ML and SignalR" -ForegroundColor Gray
Write-Host "   • Dynamic pricing with caching and notifications" -ForegroundColor Gray
Write-Host ""
Write-Host "For more information, please check README.md and docs/ folder" -ForegroundColor Cyan
