using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.RecommendationService.Models;
using PersimmonChic.RecommendationService.Services;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllers();

// 配置Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Persimmon Chic Recommendation Service",
        Version = "v1",
        Description = "智能推荐服务API - 提供个性化推荐、协同过滤和机器学习推荐功能",
        Contact = new OpenApiContact
        {
            Name = "Persimmon Chic Team",
            Email = "<EMAIL>"
        }
    });

    // 添加JWT认证配置
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // 包含XML注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// 配置JWT认证
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5000", "http://localhost:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// 配置Redis缓存
var redisConnectionString = builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379";
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConnectionString;
    options.InstanceName = "PersimmonChic.Recommendation";
});

// 注册数据访问层
builder.Services.AddScoped<IRepository<UserBehavior>, InMemoryRepository<UserBehavior>>();
builder.Services.AddScoped<IRepository<ProductFeature>, InMemoryRepository<ProductFeature>>();
builder.Services.AddScoped<IRepository<UserProfile>, InMemoryRepository<UserProfile>>();
builder.Services.AddScoped<IRepository<RecommendationFeedback>, InMemoryRepository<RecommendationFeedback>>();
builder.Services.AddScoped<IRepository<ABTestConfig>, InMemoryRepository<ABTestConfig>>();

// 注册推荐引擎服务
builder.Services.AddScoped<IMLRecommendationEngine, MLRecommendationEngine>();
builder.Services.AddScoped<ICollaborativeFilteringEngine, CollaborativeFilteringEngine>();
builder.Services.AddScoped<IContentBasedEngine, ContentBasedEngine>();
builder.Services.AddScoped<IABTestService, ABTestService>();
builder.Services.AddScoped<IRecommendationCacheService, RecommendationCacheService>();

// 注册主要业务服务
builder.Services.AddScoped<IRecommendationService, PersimmonChic.RecommendationService.Services.RecommendationService>();

// 注册HTTP客户端工厂
builder.Services.AddHttpClient();

// 配置日志
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    logging.AddDebug();
    
    if (builder.Environment.IsDevelopment())
    {
        logging.SetMinimumLevel(LogLevel.Debug);
    }
    else
    {
        logging.SetMinimumLevel(LogLevel.Information);
    }
});

// 配置健康检查
builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Recommendation Service V1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}

app.UseHttpsRedirection();

app.UseCors();

app.UseAuthentication();
app.UseAuthorization();

// 添加请求日志中间件
app.Use(async (context, next) =>
{
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    var requestId = Guid.NewGuid().ToString("N")[..8];
    
    using (logger.BeginScope(new Dictionary<string, object> { ["RequestId"] = requestId }))
    {
        logger.LogInformation("开始处理请求: {Method} {Path}", 
            context.Request.Method, context.Request.Path);
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            await next();
        }
        finally
        {
            stopwatch.Stop();
            logger.LogInformation("请求处理完成: {Method} {Path} - {StatusCode} - {ElapsedMs}ms",
                context.Request.Method, context.Request.Path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
        }
    }
});

app.MapControllers();

// 配置健康检查端点
app.MapHealthChecks("/health");

// 添加服务信息端点
app.MapGet("/info", () => new
{
    Service = "PersimmonChic.RecommendationService",
    Version = "1.0.0",
    Environment = app.Environment.EnvironmentName,
    StartTime = DateTime.UtcNow,
    Features = new[]
    {
        "个性化推荐引擎",
        "协同过滤算法",
        "内容过滤算法",
        "机器学习推荐",
        "A/B测试框架",
        "实时行为追踪",
        "用户画像管理",
        "推荐缓存优化",
        "多算法混合推荐",
        "推荐效果评估"
    }
});

// 初始化默认数据
await InitializeDefaultDataAsync(app.Services);

app.Run();

/// <summary>
/// 初始化默认数据
/// </summary>
static async Task InitializeDefaultDataAsync(IServiceProvider services)
{
    using var scope = services.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    
    try
    {
        logger.LogInformation("开始初始化推荐服务默认数据...");
        
        // 初始化默认产品特征数据
        var productRepository = scope.ServiceProvider.GetRequiredService<IRepository<ProductFeature>>();
        
        var defaultProducts = new List<ProductFeature>
        {
            new ProductFeature
            {
                Id = 1,
                ProductId = 1,
                Name = "智能手机 Pro Max",
                Description = "最新款智能手机，配备先进的AI芯片",
                Category = "电子产品",
                Brand = "TechBrand",
                Price = 6999m,
                PopularityScore = 4.8f,
                Tags = new List<string> { "智能", "高端", "拍照", "5G" },
                Attributes = new Dictionary<string, object>
                {
                    ["screen_size"] = "6.7英寸",
                    ["storage"] = "256GB",
                    ["color"] = "深空灰"
                }
            },
            new ProductFeature
            {
                Id = 2,
                ProductId = 2,
                Name = "无线蓝牙耳机",
                Description = "高品质音质，主动降噪功能",
                Category = "电子产品",
                Brand = "AudioBrand",
                Price = 1299m,
                PopularityScore = 4.5f,
                Tags = new List<string> { "无线", "降噪", "音质", "便携" },
                Attributes = new Dictionary<string, object>
                {
                    ["battery_life"] = "30小时",
                    ["noise_cancellation"] = true,
                    ["color"] = "白色"
                }
            },
            new ProductFeature
            {
                Id = 3,
                ProductId = 3,
                Name = "运动智能手表",
                Description = "专业运动监测，健康管理",
                Category = "可穿戴设备",
                Brand = "SportBrand",
                Price = 2199m,
                PopularityScore = 4.3f,
                Tags = new List<string> { "运动", "健康", "防水", "GPS" },
                Attributes = new Dictionary<string, object>
                {
                    ["waterproof"] = "50米防水",
                    ["gps"] = true,
                    ["battery_life"] = "7天"
                }
            }
        };

        foreach (var product in defaultProducts)
        {
            await productRepository.AddAsync(product);
        }

        // 初始化默认用户画像数据
        var profileRepository = scope.ServiceProvider.GetRequiredService<IRepository<UserProfile>>();
        
        var defaultProfiles = new List<UserProfile>
        {
            new UserProfile
            {
                Id = 1,
                UserId = 1,
                Age = 28,
                Gender = "男",
                Location = "北京",
                Interests = new List<string> { "科技", "数码", "游戏" },
                PreferredCategories = new List<string> { "电子产品", "数码配件" },
                PreferredBrands = new List<string> { "TechBrand", "AudioBrand" },
                AverageOrderValue = 2500m,
                PurchaseFrequency = 3
            },
            new UserProfile
            {
                Id = 2,
                UserId = 2,
                Age = 32,
                Gender = "女",
                Location = "上海",
                Interests = new List<string> { "健身", "时尚", "旅行" },
                PreferredCategories = new List<string> { "可穿戴设备", "运动用品" },
                PreferredBrands = new List<string> { "SportBrand" },
                AverageOrderValue = 1800m,
                PurchaseFrequency = 2
            }
        };

        foreach (var profile in defaultProfiles)
        {
            await profileRepository.AddAsync(profile);
        }

        // 初始化默认A/B测试配置
        var abTestRepository = scope.ServiceProvider.GetRequiredService<IRepository<ABTestConfig>>();
        
        var defaultABTest = new ABTestConfig
        {
            Id = 1,
            Name = "recommendation_algorithm",
            Description = "推荐算法A/B测试",
            Variants = new List<ABTestVariant>
            {
                new ABTestVariant
                {
                    Name = "control",
                    Algorithm = "collaborative",
                    Weight = 0.5f,
                    Parameters = new Dictionary<string, object> { ["type"] = "user_based" }
                },
                new ABTestVariant
                {
                    Name = "treatment",
                    Algorithm = "hybrid",
                    Weight = 0.5f,
                    Parameters = new Dictionary<string, object> { ["ml_weight"] = 0.6, ["cf_weight"] = 0.4 }
                }
            },
            TrafficSplit = 0.5f,
            IsActive = true
        };

        await abTestRepository.AddAsync(defaultABTest);

        // 初始化一些模拟用户行为数据
        var behaviorRepository = scope.ServiceProvider.GetRequiredService<IRepository<UserBehavior>>();
        
        var random = new Random(42);
        var behaviorTypes = Enum.GetValues<BehaviorType>();
        
        for (int i = 0; i < 50; i++)
        {
            var behavior = new UserBehavior
            {
                Id = i + 1,
                UserId = random.Next(1, 6), // 用户1-5
                ProductId = random.Next(1, 4), // 产品1-3
                BehaviorType = behaviorTypes[random.Next(behaviorTypes.Length)],
                Rating = random.Next(1, 6),
                Duration = random.Next(10, 300),
                Timestamp = DateTime.UtcNow.AddDays(-random.Next(0, 30)),
                Context = new Dictionary<string, object>
                {
                    ["source"] = "web",
                    ["device"] = random.Next(2) == 0 ? "mobile" : "desktop"
                }
            };

            await behaviorRepository.AddAsync(behavior);
        }
        
        logger.LogInformation("推荐服务默认数据初始化完成");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "初始化默认数据时发生错误");
    }
}
