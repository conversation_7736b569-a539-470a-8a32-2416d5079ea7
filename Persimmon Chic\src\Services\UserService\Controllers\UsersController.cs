using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PersimmonChic.Shared.Contracts;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.UserService.Controllers;

/// <summary>
/// 用户控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IUserService userService, ILogger<UsersController> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录响应</returns>
    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<LoginResponse>>> Login([FromBody] LoginRequest request)
    {
        var result = await _userService.LoginAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    /// <returns>注册响应</returns>
    [HttpPost("register")]
    public async Task<ActionResult<ApiResponse<User>>> Register([FromBody] RegisterRequest request)
    {
        var result = await _userService.RegisterAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取用户信息
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户信息</returns>
    [HttpGet("{id}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<User>>> GetUser(int id)
    {
        var result = await _userService.GetUserByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 根据用户名获取用户信息
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户信息</returns>
    [HttpGet("by-username/{username}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<User>>> GetUserByUsername(string username)
    {
        var result = await _userService.GetUserByUsernameAsync(username);
        return Ok(result);
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>更新结果</returns>
    [HttpPut]
    [Authorize]
    public async Task<ActionResult<ApiResponse<User>>> UpdateUser([FromBody] User user)
    {
        var result = await _userService.UpdateUserAsync(user);
        return Ok(result);
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteUser(int id)
    {
        var result = await _userService.DeleteUserAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 获取用户列表（分页）
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>用户列表</returns>
    [HttpPost("list")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<ApiResponse<PagedResponse<User>>>> GetUsers([FromBody] PagedRequest request)
    {
        var result = await _userService.GetUsersAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="request">修改密码请求</param>
    /// <returns>修改结果</returns>
    [HttpPost("change-password")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        var result = await _userService.ChangePasswordAsync(request.UserId, request.OldPassword, request.NewPassword);
        return Ok(result);
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="request">重置密码请求</param>
    /// <returns>重置结果</returns>
    [HttpPost("reset-password")]
    public async Task<ActionResult<ApiResponse<bool>>> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        var result = await _userService.ResetPasswordAsync(request.Email);
        return Ok(result);
    }

    /// <summary>
    /// 验证令牌
    /// </summary>
    /// <param name="request">令牌验证请求</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate-token")]
    public async Task<ActionResult<ApiResponse<User>>> ValidateToken([FromBody] TokenValidationRequest request)
    {
        var result = await _userService.ValidateTokenAsync(request.Token);
        return Ok(result);
    }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    /// <param name="request">刷新令牌请求</param>
    /// <returns>新令牌</returns>
    [HttpPost("refresh-token")]
    public async Task<ActionResult<ApiResponse<LoginResponse>>> RefreshToken([FromBody] TokenValidationRequest request)
    {
        var result = await _userService.RefreshTokenAsync(request.Token);
        return Ok(result);
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <param name="request">登出请求</param>
    /// <returns>登出结果</returns>
    [HttpPost("logout")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> Logout([FromBody] TokenValidationRequest request)
    {
        var result = await _userService.LogoutAsync(request.Token);
        return Ok(result);
    }

    /// <summary>
    /// 激活用户账户
    /// </summary>
    /// <param name="request">激活请求</param>
    /// <returns>激活结果</returns>
    [HttpPost("activate")]
    public async Task<ActionResult<ApiResponse<bool>>> ActivateUser([FromBody] ActivateUserRequest request)
    {
        var result = await _userService.ActivateUserAsync(request.UserId, request.ActivationCode);
        return Ok(result);
    }

    /// <summary>
    /// 禁用用户账户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>禁用结果</returns>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> DeactivateUser(int id)
    {
        var result = await _userService.DeactivateUserAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 获取用户统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, object>>>> GetStatistics()
    {
        var result = await _userService.GetUserStatisticsAsync();
        return Ok(result);
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet("/health")]
    public ActionResult<object> Health()
    {
        return Ok(new { Status = "Healthy", Service = "UserService", Timestamp = DateTime.UtcNow });
    }
}

/// <summary>
/// 修改密码请求模型
/// </summary>
public class ChangePasswordRequest
{
    public int UserId { get; set; }
    public string OldPassword { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}

/// <summary>
/// 重置密码请求模型
/// </summary>
public class ResetPasswordRequest
{
    public string Email { get; set; } = string.Empty;
}

/// <summary>
/// 令牌验证请求模型
/// </summary>
public class TokenValidationRequest
{
    public string Token { get; set; } = string.Empty;
}

/// <summary>
/// 激活用户请求模型
/// </summary>
public class ActivateUserRequest
{
    public int UserId { get; set; }
    public string ActivationCode { get; set; } = string.Empty;
}
