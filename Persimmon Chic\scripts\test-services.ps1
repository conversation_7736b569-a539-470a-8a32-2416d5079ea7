# Persimmon Chic 微服务测试脚本
# 用于测试推荐服务、搜索服务和AI客服机器人服务

param(
    [string]$ServiceName = "all",
    [int]$TimeoutSeconds = 30
)

Write-Host "=== Persimmon Chic 微服务测试脚本 ===" -ForegroundColor Green
Write-Host "测试服务: $ServiceName" -ForegroundColor Yellow
Write-Host "超时时间: $TimeoutSeconds 秒" -ForegroundColor Yellow
Write-Host ""

# 服务配置
$services = @{
    "RecommendationService" = @{
        "Name" = "推荐服务"
        "Port" = 5006
        "HealthEndpoint" = "/health"
        "TestEndpoints" = @(
            @{ "Method" = "GET"; "Path" = "/api/recommendation/health"; "Description" = "健康检查" },
            @{ "Method" = "GET"; "Path" = "/api/recommendation/user/1/recommendations?count=5"; "Description" = "获取用户推荐" },
            @{ "Method" = "GET"; "Path" = "/api/recommendation/product/1/similar?count=3"; "Description" = "获取相似商品" },
            @{ "Method" = "GET"; "Path" = "/api/recommendation/trending?count=10"; "Description" = "获取热门商品" }
        )
    }
    "SearchService" = @{
        "Name" = "搜索服务"
        "Port" = 5007
        "HealthEndpoint" = "/health"
        "TestEndpoints" = @(
            @{ "Method" = "GET"; "Path" = "/api/search/health"; "Description" = "健康检查" },
            @{ "Method" = "GET"; "Path" = "/api/search/suggestions?query=手机&count=5"; "Description" = "获取搜索建议" },
            @{ "Method" = "GET"; "Path" = "/api/search/trending?count=10"; "Description" = "获取热门搜索" },
            @{ "Method" = "GET"; "Path" = "/api/search/semantic?query=智能手机&topK=5"; "Description" = "语义搜索" }
        )
    }
    "CustomerServiceBot" = @{
        "Name" = "AI客服机器人"
        "Port" = 5008
        "HealthEndpoint" = "/health"
        "TestEndpoints" = @(
            @{ "Method" = "GET"; "Path" = "/api/chat/health"; "Description" = "健康检查" },
            @{ "Method" = "POST"; "Path" = "/api/chat/session"; "Description" = "创建会话" },
            @{ "Method" = "GET"; "Path" = "/api/chat/knowledge/search?query=退货"; "Description" = "搜索知识库" }
        )
    }
}

# 测试函数
function Test-ServiceEndpoint {
    param(
        [string]$BaseUrl,
        [hashtable]$Endpoint,
        [int]$Timeout = 30
    )
    
    $url = "$BaseUrl$($Endpoint.Path)"
    $method = $Endpoint.Method
    $description = $Endpoint.Description
    
    try {
        Write-Host "  测试: $description" -ForegroundColor Cyan
        Write-Host "    URL: $method $url" -ForegroundColor Gray
        
        $response = $null
        $statusCode = $null
        
        if ($method -eq "GET") {
            $response = Invoke-RestMethod -Uri $url -Method GET -TimeoutSec $Timeout -ErrorAction Stop
            $statusCode = 200
        }
        elseif ($method -eq "POST") {
            if ($Endpoint.Path -eq "/api/chat/session") {
                $response = Invoke-RestMethod -Uri $url -Method POST -TimeoutSec $Timeout -ErrorAction Stop
                $statusCode = 201
            }
            else {
                $body = @{} | ConvertTo-Json
                $response = Invoke-RestMethod -Uri $url -Method POST -Body $body -ContentType "application/json" -TimeoutSec $Timeout -ErrorAction Stop
                $statusCode = 200
            }
        }
        
        Write-Host "    结果: ✓ 成功 (状态码: $statusCode)" -ForegroundColor Green
        
        # 显示响应摘要
        if ($response) {
            if ($response.GetType().Name -eq "PSCustomObject") {
                $responseText = ($response | ConvertTo-Json -Depth 2 -Compress)
                if ($responseText.Length -gt 100) {
                    $responseText = $responseText.Substring(0, 100) + "..."
                }
                Write-Host "    响应: $responseText" -ForegroundColor Gray
            }
        }
        
        return $true
    }
    catch {
        Write-Host "    结果: ✗ 失败" -ForegroundColor Red
        Write-Host "    错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-Service {
    param(
        [string]$ServiceKey,
        [hashtable]$ServiceConfig
    )
    
    $serviceName = $ServiceConfig.Name
    $port = $ServiceConfig.Port
    $baseUrl = "http://localhost:$port"
    
    Write-Host "测试服务: $serviceName (端口: $port)" -ForegroundColor Yellow
    Write-Host "基础URL: $baseUrl" -ForegroundColor Gray
    Write-Host ""
    
    $successCount = 0
    $totalCount = $ServiceConfig.TestEndpoints.Count
    
    foreach ($endpoint in $ServiceConfig.TestEndpoints) {
        if (Test-ServiceEndpoint -BaseUrl $baseUrl -Endpoint $endpoint -Timeout $TimeoutSeconds) {
            $successCount++
        }
        Start-Sleep -Milliseconds 500  # 避免请求过于频繁
    }
    
    Write-Host ""
    Write-Host "服务测试完成: $serviceName" -ForegroundColor Yellow
    Write-Host "成功: $successCount/$totalCount" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })
    Write-Host ""
    
    return @{
        "ServiceName" = $serviceName
        "Success" = $successCount
        "Total" = $totalCount
        "SuccessRate" = [math]::Round(($successCount / $totalCount) * 100, 2)
    }
}

# 主测试逻辑
$testResults = @()

if ($ServiceName -eq "all") {
    Write-Host "开始测试所有服务..." -ForegroundColor Green
    Write-Host ""
    
    foreach ($serviceKey in $services.Keys) {
        $result = Test-Service -ServiceKey $serviceKey -ServiceConfig $services[$serviceKey]
        $testResults += $result
        Write-Host "=" * 60 -ForegroundColor Gray
    }
}
else {
    if ($services.ContainsKey($ServiceName)) {
        Write-Host "开始测试服务: $ServiceName" -ForegroundColor Green
        Write-Host ""
        
        $result = Test-Service -ServiceKey $ServiceName -ServiceConfig $services[$ServiceName]
        $testResults += $result
    }
    else {
        Write-Host "错误: 未找到服务 '$ServiceName'" -ForegroundColor Red
        Write-Host "可用服务: $($services.Keys -join ', ')" -ForegroundColor Yellow
        exit 1
    }
}

# 显示测试总结
Write-Host ""
Write-Host "=== 测试总结 ===" -ForegroundColor Green
Write-Host ""

$totalSuccess = 0
$totalTests = 0

foreach ($result in $testResults) {
    $status = if ($result.Success -eq $result.Total) { "✓" } else { "⚠" }
    $color = if ($result.Success -eq $result.Total) { "Green" } else { "Yellow" }
    
    Write-Host "$status $($result.ServiceName): $($result.Success)/$($result.Total) ($($result.SuccessRate)%)" -ForegroundColor $color
    
    $totalSuccess += $result.Success
    $totalTests += $result.Total
}

Write-Host ""
$overallSuccessRate = if ($totalTests -gt 0) { [math]::Round(($totalSuccess / $totalTests) * 100, 2) } else { 0 }
$overallStatus = if ($totalSuccess -eq $totalTests) { "✓ 全部通过" } else { "⚠ 部分失败" }
$overallColor = if ($totalSuccess -eq $totalTests) { "Green" } else { "Yellow" }

Write-Host "总体结果: $overallStatus ($totalSuccess/$totalTests, $overallSuccessRate%)" -ForegroundColor $overallColor

# 提供使用建议
Write-Host ""
Write-Host "=== 使用建议 ===" -ForegroundColor Cyan
Write-Host "1. 确保所有服务都已启动并运行在正确的端口上"
Write-Host "2. 如果测试失败，请检查服务日志和配置"
Write-Host "3. 可以使用以下命令测试单个服务:"
Write-Host "   .\test-services.ps1 -ServiceName RecommendationService"
Write-Host "   .\test-services.ps1 -ServiceName SearchService"
Write-Host "   .\test-services.ps1 -ServiceName CustomerServiceBot"
Write-Host ""

# 退出代码
if ($totalSuccess -eq $totalTests) {
    Write-Host "所有测试通过！" -ForegroundColor Green
    exit 0
}
else {
    Write-Host "部分测试失败，请检查服务状态。" -ForegroundColor Yellow
    exit 1
}
