using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.Shared.Models;

/// <summary>
/// 商品实体模型
/// </summary>
public class Product
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    [Range(0.01, double.MaxValue)]
    public decimal Price { get; set; }
    
    public decimal? OriginalPrice { get; set; }
    
    [Required]
    [Range(0, int.MaxValue)]
    public int Stock { get; set; }
    
    [StringLength(100)]
    public string SKU { get; set; } = string.Empty;
    
    public int CategoryId { get; set; }
    
    public ProductStatus Status { get; set; } = ProductStatus.Active;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public int ViewCount { get; set; }
    
    public int SalesCount { get; set; }
    
    public double Rating { get; set; }
    
    public int ReviewCount { get; set; }
    
    // 导航属性
    public virtual Category Category { get; set; } = null!;
    public virtual ICollection<ProductImage> Images { get; set; } = new List<ProductImage>();
    public virtual ICollection<ProductAttribute> Attributes { get; set; } = new List<ProductAttribute>();
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
}

/// <summary>
/// 商品状态枚举
/// </summary>
public enum ProductStatus
{
    Draft = 0,
    Active = 1,
    Inactive = 2,
    OutOfStock = 3,
    Discontinued = 4
}

/// <summary>
/// 商品分类模型
/// </summary>
public class Category
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string Description { get; set; } = string.Empty;
    
    public int? ParentId { get; set; }
    
    public int SortOrder { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public string? Icon { get; set; }
    
    // 导航属性
    public virtual Category? Parent { get; set; }
    public virtual ICollection<Category> Children { get; set; } = new List<Category>();
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}

/// <summary>
/// 商品图片模型
/// </summary>
public class ProductImage
{
    public int Id { get; set; }
    
    public int ProductId { get; set; }
    
    [Required]
    [StringLength(500)]
    public string ImageUrl { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string AltText { get; set; } = string.Empty;
    
    public int SortOrder { get; set; }
    
    public bool IsPrimary { get; set; }
    
    // 导航属性
    public virtual Product Product { get; set; } = null!;
}

/// <summary>
/// 商品属性模型
/// </summary>
public class ProductAttribute
{
    public int Id { get; set; }
    
    public int ProductId { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [StringLength(200)]
    public string Value { get; set; } = string.Empty;
    
    public int SortOrder { get; set; }
    
    // 导航属性
    public virtual Product Product { get; set; } = null!;
}

/// <summary>
/// 商品搜索请求模型
/// </summary>
public class ProductSearchRequest
{
    public string? Keyword { get; set; }
    public int? CategoryId { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public ProductStatus? Status { get; set; }
    public ProductSortBy SortBy { get; set; } = ProductSortBy.CreatedAt;
    public bool SortDescending { get; set; } = true;
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 商品排序方式枚举
/// </summary>
public enum ProductSortBy
{
    CreatedAt = 0,
    Price = 1,
    Name = 2,
    Sales = 3,
    Rating = 4,
    ViewCount = 5
}
