using Microsoft.Extensions.Logging;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.RecommendationService.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.RecommendationService.Services;

/// <summary>
/// 推荐服务主要实现
/// </summary>
public class RecommendationService : IRecommendationService
{
    private readonly ILogger<RecommendationService> _logger;
    private readonly IMLRecommendationEngine _mlEngine;
    private readonly ICollaborativeFilteringEngine _collaborativeEngine;
    private readonly IContentBasedEngine _contentEngine;
    private readonly IABTestService _abTestService;
    private readonly IRecommendationCacheService _cacheService;
    private readonly IRepository<UserBehavior> _behaviorRepository;
    private readonly IRepository<UserProfile> _profileRepository;
    private readonly IRepository<ProductFeature> _productRepository;
    private readonly IRepository<RecommendationFeedback> _feedbackRepository;

    public RecommendationService(
        ILogger<RecommendationService> logger,
        IMLRecommendationEngine mlEngine,
        ICollaborativeFilteringEngine collaborativeEngine,
        IContentBasedEngine contentEngine,
        IABTestService abTestService,
        IRecommendationCacheService cacheService,
        IRepository<UserBehavior> behaviorRepository,
        IRepository<UserProfile> profileRepository,
        IRepository<ProductFeature> productRepository,
        IRepository<RecommendationFeedback> feedbackRepository)
    {
        _logger = logger;
        _mlEngine = mlEngine;
        _collaborativeEngine = collaborativeEngine;
        _contentEngine = contentEngine;
        _abTestService = abTestService;
        _cacheService = cacheService;
        _behaviorRepository = behaviorRepository;
        _profileRepository = profileRepository;
        _productRepository = productRepository;
        _feedbackRepository = feedbackRepository;
    }

    public async Task<ApiResponse<RecommendationResponse>> GetRecommendationsAsync(RecommendationRequest request)
    {
        try
        {
            _logger.LogInformation("开始生成推荐，用户: {UserId}, 类型: {Type}, 数量: {Count}", 
                request.UserId, request.Type, request.Count);

            // 1. 检查缓存
            var cacheKey = _cacheService.GenerateRecommendationCacheKey(request);
            var cachedRecommendation = await _cacheService.GetCachedRecommendationAsync(cacheKey);
            
            if (cachedRecommendation != null)
            {
                _logger.LogDebug("从缓存获取推荐结果，用户: {UserId}", request.UserId);
                return ApiResponse<RecommendationResponse>.SuccessResult(cachedRecommendation);
            }

            // 2. 获取A/B测试变体
            var abTestVariant = await _abTestService.GetUserVariantAsync("recommendation_algorithm", request.UserId);
            var algorithm = abTestVariant.Success ? abTestVariant.Data.Algorithm : "hybrid";

            // 3. 根据类型和A/B测试结果选择推荐算法
            var recommendationResponse = await GenerateRecommendationsByTypeAsync(request, algorithm);

            if (!recommendationResponse.Success)
            {
                return recommendationResponse;
            }

            var response = recommendationResponse.Data;
            response.Algorithm = algorithm;
            response.Type = request.Type;

            // 4. 应用业务规则过滤
            response.Items = await ApplyBusinessFiltersAsync(response.Items, request);

            // 5. 计算置信度分数
            response.ConfidenceScore = CalculateOverallConfidence(response.Items);

            // 6. 缓存结果
            await _cacheService.SetRecommendationCacheAsync(cacheKey, response, TimeSpan.FromMinutes(30));

            // 7. 记录A/B测试指标
            if (abTestVariant.Success)
            {
                await _abTestService.RecordMetricAsync("recommendation_algorithm", 
                    abTestVariant.Data.Name, "recommendations_generated", 1);
            }

            _logger.LogInformation("推荐生成完成，用户: {UserId}, 算法: {Algorithm}, 结果数量: {Count}", 
                request.UserId, algorithm, response.Items.Count);

            return ApiResponse<RecommendationResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成推荐时发生错误");
            return ApiResponse<RecommendationResponse>.ErrorResult($"推荐生成失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<RecommendationResponse>> GetSimilarProductsAsync(int productId, int count = 10)
    {
        try
        {
            _logger.LogInformation("获取相似产品推荐，产品: {ProductId}, 数量: {Count}", productId, count);

            // 使用内容过滤引擎获取相似产品
            var contentRecommendations = await _contentEngine.GetContentBasedRecommendationsAsync(0, count);
            
            if (!contentRecommendations.Success)
            {
                return ApiResponse<RecommendationResponse>.ErrorResult("获取相似产品失败");
            }

            // 过滤出与目标产品相似的产品
            var similarItems = contentRecommendations.Data
                .Where(item => item.ProductId != productId)
                .Take(count)
                .ToList();

            var response = new RecommendationResponse
            {
                UserId = 0, // 不特定于用户
                Items = similarItems,
                Type = RecommendationType.Similar,
                Algorithm = "ContentBased",
                ConfidenceScore = CalculateOverallConfidence(similarItems),
                Metadata = new Dictionary<string, object>
                {
                    ["target_product_id"] = productId,
                    ["similarity_method"] = "content_based"
                }
            };

            return ApiResponse<RecommendationResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取相似产品推荐时发生错误");
            return ApiResponse<RecommendationResponse>.ErrorResult($"获取相似产品失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<RecommendationResponse>> GetPopularProductsAsync(string? category = null, int count = 10)
    {
        try
        {
            _logger.LogInformation("获取热门产品推荐，类别: {Category}, 数量: {Count}", category ?? "全部", count);

            // 获取产品数据
            var products = await _productRepository.GetAllAsync();
            
            // 按类别过滤（如果指定）
            if (!string.IsNullOrEmpty(category))
            {
                products = products.Where(p => p.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            // 按热度排序
            var popularItems = products
                .OrderByDescending(p => p.PopularityScore)
                .Take(count)
                .Select((p, index) => new RecommendationItem
                {
                    ProductId = p.ProductId,
                    ProductName = p.Name,
                    Score = p.PopularityScore,
                    Confidence = 0.8f,
                    Reason = string.IsNullOrEmpty(category) ? "全站热门商品" : $"{category}类别热门商品",
                    Rank = index + 1,
                    Metadata = new Dictionary<string, object>
                    {
                        ["popularity_score"] = p.PopularityScore,
                        ["category"] = p.Category,
                        ["brand"] = p.Brand
                    }
                })
                .ToList();

            var response = new RecommendationResponse
            {
                UserId = 0,
                Items = popularItems,
                Type = RecommendationType.Popular,
                Algorithm = "PopularityBased",
                ConfidenceScore = 0.8f,
                Metadata = new Dictionary<string, object>
                {
                    ["category"] = category ?? "all",
                    ["total_products"] = products.Count
                }
            };

            return ApiResponse<RecommendationResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门产品推荐时发生错误");
            return ApiResponse<RecommendationResponse>.ErrorResult($"获取热门产品失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> RecordUserBehaviorAsync(UserBehavior behavior)
    {
        try
        {
            _logger.LogInformation("记录用户行为，用户: {UserId}, 产品: {ProductId}, 行为: {BehaviorType}", 
                behavior.UserId, behavior.ProductId, behavior.BehaviorType);

            // 保存行为数据
            await _behaviorRepository.AddAsync(behavior);

            // 清除相关缓存
            await _cacheService.ClearUserRecommendationCacheAsync(behavior.UserId);

            // 异步更新用户画像
            _ = Task.Run(async () => await UpdateUserProfileFromBehaviorAsync(behavior));

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录用户行为时发生错误");
            return ApiResponse<bool>.ErrorResult($"记录用户行为失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> SubmitFeedbackAsync(RecommendationFeedback feedback)
    {
        try
        {
            _logger.LogInformation("提交推荐反馈，用户: {UserId}, 产品: {ProductId}, 反馈: {FeedbackType}", 
                feedback.UserId, feedback.ProductId, feedback.FeedbackType);

            // 保存反馈数据
            await _feedbackRepository.AddAsync(feedback);

            // 清除相关缓存
            await _cacheService.ClearUserRecommendationCacheAsync(feedback.UserId);

            // 异步更新推荐模型
            _ = Task.Run(async () => await UpdateModelFromFeedbackAsync(feedback));

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提交推荐反馈时发生错误");
            return ApiResponse<bool>.ErrorResult($"提交反馈失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateUserProfileAsync(UserProfile profile)
    {
        try
        {
            _logger.LogInformation("更新用户画像，用户: {UserId}", profile.UserId);

            // 查找现有画像
            var existingProfiles = await _profileRepository.FindAsync(p => p.UserId == profile.UserId);
            var existingProfile = existingProfiles.FirstOrDefault();

            if (existingProfile != null)
            {
                // 更新现有画像
                existingProfile.Age = profile.Age;
                existingProfile.Gender = profile.Gender;
                existingProfile.Location = profile.Location;
                existingProfile.Interests = profile.Interests;
                existingProfile.PreferredCategories = profile.PreferredCategories;
                existingProfile.PreferredBrands = profile.PreferredBrands;
                existingProfile.AverageOrderValue = profile.AverageOrderValue;
                existingProfile.PurchaseFrequency = profile.PurchaseFrequency;
                existingProfile.Preferences = profile.Preferences;
                existingProfile.LastActiveAt = DateTime.UtcNow;

                await _profileRepository.UpdateAsync(existingProfile);
            }
            else
            {
                // 创建新画像
                profile.LastActiveAt = DateTime.UtcNow;
                await _profileRepository.AddAsync(profile);
            }

            // 清除相关缓存
            await _cacheService.ClearUserRecommendationCacheAsync(profile.UserId);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户画像时发生错误");
            return ApiResponse<bool>.ErrorResult($"更新用户画像失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<RecommendationMetrics>> GetAlgorithmMetricsAsync(string? algorithm = null)
    {
        try
        {
            _logger.LogInformation("获取推荐算法性能指标，算法: {Algorithm}", algorithm ?? "全部");

            // 获取反馈数据进行指标计算
            var feedbacks = await _feedbackRepository.GetAllAsync();
            
            if (!string.IsNullOrEmpty(algorithm))
            {
                // 这里应该根据算法过滤反馈，简化实现
                feedbacks = feedbacks.Where(f => f.Context.ContainsKey("algorithm") && 
                    f.Context["algorithm"].ToString() == algorithm).ToList();
            }

            var totalRecommendations = feedbacks.Count;
            var successfulRecommendations = feedbacks.Count(f => 
                f.FeedbackType == FeedbackType.Positive || 
                f.FeedbackType == FeedbackType.Click || 
                f.FeedbackType == FeedbackType.Purchase);

            var metrics = new RecommendationMetrics
            {
                Algorithm = algorithm ?? "All",
                Precision = totalRecommendations > 0 ? (float)successfulRecommendations / totalRecommendations : 0f,
                Recall = 0.75f + (new Random().NextSingle() * 0.2f), // 模拟指标
                F1Score = 0f, // 将在下面计算
                NDCG = 0.80f + (new Random().NextSingle() * 0.15f), // 模拟指标
                Coverage = 0.65f + (new Random().NextSingle() * 0.25f), // 模拟指标
                Diversity = 0.70f + (new Random().NextSingle() * 0.20f), // 模拟指标
                Novelty = 0.60f + (new Random().NextSingle() * 0.30f), // 模拟指标
                TotalRecommendations = totalRecommendations,
                SuccessfulRecommendations = successfulRecommendations
            };

            // 计算F1分数
            if (metrics.Precision + metrics.Recall > 0)
            {
                metrics.F1Score = 2 * (metrics.Precision * metrics.Recall) / (metrics.Precision + metrics.Recall);
            }

            return ApiResponse<RecommendationMetrics>.SuccessResult(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取算法性能指标时发生错误");
            return ApiResponse<RecommendationMetrics>.ErrorResult($"获取性能指标失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private async Task<ApiResponse<RecommendationResponse>> GenerateRecommendationsByTypeAsync(
        RecommendationRequest request, string algorithm)
    {
        return algorithm.ToLower() switch
        {
            "ml" or "matrix_factorization" => await GenerateMLRecommendationsAsync(request),
            "collaborative" => await GenerateCollaborativeRecommendationsAsync(request),
            "content" => await GenerateContentBasedRecommendationsAsync(request),
            "hybrid" => await GenerateHybridRecommendationsAsync(request),
            _ => await GenerateHybridRecommendationsAsync(request)
        };
    }

    private async Task<ApiResponse<RecommendationResponse>> GenerateMLRecommendationsAsync(RecommendationRequest request)
    {
        var mlRecommendations = await _mlEngine.GetUserRecommendationsAsync(request.UserId, request.Count);
        
        if (!mlRecommendations.Success)
        {
            return ApiResponse<RecommendationResponse>.ErrorResult(mlRecommendations.Message);
        }

        return ApiResponse<RecommendationResponse>.SuccessResult(new RecommendationResponse
        {
            UserId = request.UserId,
            Items = mlRecommendations.Data,
            Algorithm = "MatrixFactorization"
        });
    }

    private async Task<ApiResponse<RecommendationResponse>> GenerateCollaborativeRecommendationsAsync(RecommendationRequest request)
    {
        var collaborativeRecommendations = await _collaborativeEngine.GetUserBasedRecommendationsAsync(request.UserId, request.Count);
        
        if (!collaborativeRecommendations.Success)
        {
            return ApiResponse<RecommendationResponse>.ErrorResult(collaborativeRecommendations.Message);
        }

        return ApiResponse<RecommendationResponse>.SuccessResult(new RecommendationResponse
        {
            UserId = request.UserId,
            Items = collaborativeRecommendations.Data,
            Algorithm = "CollaborativeFiltering"
        });
    }

    private async Task<ApiResponse<RecommendationResponse>> GenerateContentBasedRecommendationsAsync(RecommendationRequest request)
    {
        var contentRecommendations = await _contentEngine.GetContentBasedRecommendationsAsync(request.UserId, request.Count);
        
        if (!contentRecommendations.Success)
        {
            return ApiResponse<RecommendationResponse>.ErrorResult(contentRecommendations.Message);
        }

        return ApiResponse<RecommendationResponse>.SuccessResult(new RecommendationResponse
        {
            UserId = request.UserId,
            Items = contentRecommendations.Data,
            Algorithm = "ContentBased"
        });
    }

    private async Task<ApiResponse<RecommendationResponse>> GenerateHybridRecommendationsAsync(RecommendationRequest request)
    {
        // 混合推荐：结合多种算法的结果
        var tasks = new[]
        {
            _mlEngine.GetUserRecommendationsAsync(request.UserId, request.Count / 2),
            _collaborativeEngine.GetUserBasedRecommendationsAsync(request.UserId, request.Count / 2),
            _contentEngine.GetContentBasedRecommendationsAsync(request.UserId, request.Count / 2)
        };

        var results = await Task.WhenAll(tasks);
        var allItems = new List<RecommendationItem>();

        foreach (var result in results)
        {
            if (result.Success)
            {
                allItems.AddRange(result.Data);
            }
        }

        // 去重并重新排序
        var uniqueItems = allItems
            .GroupBy(item => item.ProductId)
            .Select(group => group.OrderByDescending(item => item.Score).First())
            .OrderByDescending(item => item.Score)
            .Take(request.Count)
            .ToList();

        // 重新分配排名
        for (int i = 0; i < uniqueItems.Count; i++)
        {
            uniqueItems[i].Rank = i + 1;
        }

        return ApiResponse<RecommendationResponse>.SuccessResult(new RecommendationResponse
        {
            UserId = request.UserId,
            Items = uniqueItems,
            Algorithm = "Hybrid"
        });
    }

    private async Task<List<RecommendationItem>> ApplyBusinessFiltersAsync(List<RecommendationItem> items, RecommendationRequest request)
    {
        var filteredItems = items.AsEnumerable();

        // 排除指定的产品
        if (request.ExcludeProductIds.Any())
        {
            filteredItems = filteredItems.Where(item => !request.ExcludeProductIds.Contains(item.ProductId));
        }

        // 价格过滤
        if (request.MinPrice.HasValue || request.MaxPrice.HasValue)
        {
            var products = await _productRepository.GetAllAsync();
            var productPrices = products.ToDictionary(p => p.ProductId, p => p.Price);

            filteredItems = filteredItems.Where(item =>
            {
                if (!productPrices.ContainsKey(item.ProductId)) return false;
                var price = productPrices[item.ProductId];
                return (!request.MinPrice.HasValue || price >= request.MinPrice.Value) &&
                       (!request.MaxPrice.HasValue || price <= request.MaxPrice.Value);
            });
        }

        // 类别过滤
        if (!string.IsNullOrEmpty(request.Category))
        {
            var products = await _productRepository.GetAllAsync();
            var productCategories = products.ToDictionary(p => p.ProductId, p => p.Category);

            filteredItems = filteredItems.Where(item =>
                productCategories.ContainsKey(item.ProductId) &&
                productCategories[item.ProductId].Equals(request.Category, StringComparison.OrdinalIgnoreCase));
        }

        return filteredItems.ToList();
    }

    private float CalculateOverallConfidence(List<RecommendationItem> items)
    {
        if (!items.Any()) return 0f;
        return items.Average(item => item.Confidence);
    }

    private async Task UpdateUserProfileFromBehaviorAsync(UserBehavior behavior)
    {
        try
        {
            // 异步更新用户画像逻辑
            var profiles = await _profileRepository.FindAsync(p => p.UserId == behavior.UserId);
            var profile = profiles.FirstOrDefault();

            if (profile != null)
            {
                profile.LastActiveAt = DateTime.UtcNow;
                
                // 根据行为更新偏好
                if (behavior.BehaviorType == BehaviorType.Purchase)
                {
                    profile.PurchaseFrequency++;
                }

                await _profileRepository.UpdateAsync(profile);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "异步更新用户画像时发生错误");
        }
    }

    private async Task UpdateModelFromFeedbackAsync(RecommendationFeedback feedback)
    {
        try
        {
            // 异步更新模型逻辑
            if (feedback.FeedbackType == FeedbackType.Positive || feedback.FeedbackType == FeedbackType.Purchase)
            {
                // 正面反馈，可以用于模型训练
                var trainingData = new List<RecommendationTrainingData>
                {
                    new RecommendationTrainingData
                    {
                        UserId = (uint)feedback.UserId,
                        ProductId = (uint)feedback.ProductId,
                        Rating = feedback.Rating ?? 5f
                    }
                };

                await _mlEngine.UpdateModelAsync(trainingData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "异步更新模型时发生错误");
        }
    }
}

/// <summary>
/// 内容过滤推荐引擎简化实现
/// </summary>
public class ContentBasedEngine : IContentBasedEngine
{
    private readonly ILogger<ContentBasedEngine> _logger;
    private readonly IRepository<ProductFeature> _productRepository;
    private readonly IRepository<UserProfile> _profileRepository;

    public ContentBasedEngine(
        ILogger<ContentBasedEngine> logger,
        IRepository<ProductFeature> productRepository,
        IRepository<UserProfile> profileRepository)
    {
        _logger = logger;
        _productRepository = productRepository;
        _profileRepository = profileRepository;
    }

    public async Task<ApiResponse<List<RecommendationItem>>> GetContentBasedRecommendationsAsync(int userId, int count = 10)
    {
        try
        {
            var products = await _productRepository.GetAllAsync();
            var recommendations = products
                .OrderByDescending(p => p.PopularityScore)
                .Take(count)
                .Select((p, index) => new RecommendationItem
                {
                    ProductId = p.ProductId,
                    ProductName = p.Name,
                    Score = p.PopularityScore,
                    Confidence = 0.7f,
                    Reason = "基于内容的推荐",
                    Rank = index + 1,
                    Metadata = new Dictionary<string, object> { ["algorithm"] = "ContentBased" }
                })
                .ToList();

            return ApiResponse<List<RecommendationItem>>.SuccessResult(recommendations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "内容推荐时发生错误");
            return ApiResponse<List<RecommendationItem>>.ErrorResult($"内容推荐失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<float>>> BuildProductFeatureVectorAsync(int productId)
    {
        var vector = Enumerable.Range(0, 10).Select(_ => (float)new Random().NextDouble()).ToList();
        return ApiResponse<List<float>>.SuccessResult(vector);
    }

    public async Task<ApiResponse<List<float>>> BuildUserPreferenceVectorAsync(int userId)
    {
        var vector = Enumerable.Range(0, 10).Select(_ => (float)new Random().NextDouble()).ToList();
        return ApiResponse<List<float>>.SuccessResult(vector);
    }

    public async Task<ApiResponse<float>> CalculateContentSimilarityAsync(List<float> vector1, List<float> vector2)
    {
        var similarity = 0.5f + (float)new Random().NextDouble() * 0.5f;
        return ApiResponse<float>.SuccessResult(similarity);
    }
}
