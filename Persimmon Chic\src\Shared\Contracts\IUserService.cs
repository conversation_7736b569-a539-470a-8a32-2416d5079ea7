using PersimmonChic.Shared.Models;

namespace PersimmonChic.Shared.Contracts;

/// <summary>
/// 用户服务接口
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录响应</returns>
    Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request);

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    /// <returns>注册结果</returns>
    Task<ApiResponse<User>> RegisterAsync(RegisterRequest request);

    /// <summary>
    /// 根据ID获取用户信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    Task<ApiResponse<User>> GetUserByIdAsync(int userId);

    /// <summary>
    /// 根据用户名获取用户信息
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户信息</returns>
    Task<ApiResponse<User>> GetUserByUsernameAsync(string username);

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<User>> UpdateUserAsync(User user);

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>删除结果</returns>
    Task<ApiResponse<bool>> DeleteUserAsync(int userId);

    /// <summary>
    /// 获取用户列表（分页）
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>用户列表</returns>
    Task<ApiResponse<PagedResponse<User>>> GetUsersAsync(PagedRequest request);

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="oldPassword">旧密码</param>
    /// <param name="newPassword">新密码</param>
    /// <returns>修改结果</returns>
    Task<ApiResponse<bool>> ChangePasswordAsync(int userId, string oldPassword, string newPassword);

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <returns>重置结果</returns>
    Task<ApiResponse<bool>> ResetPasswordAsync(string email);

    /// <summary>
    /// 验证用户令牌
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>验证结果</returns>
    Task<ApiResponse<User>> ValidateTokenAsync(string token);

    /// <summary>
    /// 刷新令牌
    /// </summary>
    /// <param name="token">当前令牌</param>
    /// <returns>新令牌</returns>
    Task<ApiResponse<LoginResponse>> RefreshTokenAsync(string token);

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>登出结果</returns>
    Task<ApiResponse<bool>> LogoutAsync(string token);

    /// <summary>
    /// 激活用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="activationCode">激活码</param>
    /// <returns>激活结果</returns>
    Task<ApiResponse<bool>> ActivateUserAsync(int userId, string activationCode);

    /// <summary>
    /// 禁用用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>禁用结果</returns>
    Task<ApiResponse<bool>> DeactivateUserAsync(int userId);

    /// <summary>
    /// 获取用户统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetUserStatisticsAsync();
}
