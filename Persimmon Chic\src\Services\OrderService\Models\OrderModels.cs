using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.OrderService.Models;

/// <summary>
/// 订单实体
/// </summary>
public class Order
{
    public int Id { get; set; }
    
    [Required]
    public string OrderNumber { get; set; } = string.Empty;
    
    public string? UserId { get; set; }
    
    public string? UserName { get; set; }
    
    public OrderStatus Status { get; set; } = OrderStatus.Pending;
    
    public decimal TotalAmount { get; set; }
    
    public decimal DiscountAmount { get; set; }
    
    public decimal ShippingFee { get; set; }
    
    public decimal FinalAmount { get; set; }
    
    public string? CouponCode { get; set; }
    
    public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Online;
    
    public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
    
    public string? PaymentTransactionId { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? PaidAt { get; set; }
    
    public DateTime? ShippedAt { get; set; }
    
    public DateTime? DeliveredAt { get; set; }
    
    public DateTime? CancelledAt { get; set; }
    
    public string? CancelReason { get; set; }
    
    public List<OrderItem> Items { get; set; } = new();
    
    public ShippingAddress? ShippingAddress { get; set; }
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 订单项
/// </summary>
public class OrderItem
{
    public int Id { get; set; }
    
    public int OrderId { get; set; }
    
    public int ProductId { get; set; }
    
    public string ProductName { get; set; } = string.Empty;
    
    public string? ProductSku { get; set; }
    
    public string? ProductImage { get; set; }
    
    public decimal UnitPrice { get; set; }
    
    public int Quantity { get; set; }
    
    public decimal TotalPrice { get; set; }
    
    public Dictionary<string, object> ProductAttributes { get; set; } = new();
    
    public Order? Order { get; set; }
}

/// <summary>
/// 配送地址
/// </summary>
public class ShippingAddress
{
    public int Id { get; set; }
    
    public int OrderId { get; set; }
    
    [Required]
    public string ReceiverName { get; set; } = string.Empty;
    
    [Required]
    public string Phone { get; set; } = string.Empty;
    
    [Required]
    public string Province { get; set; } = string.Empty;
    
    [Required]
    public string City { get; set; } = string.Empty;
    
    [Required]
    public string District { get; set; } = string.Empty;
    
    [Required]
    public string DetailAddress { get; set; } = string.Empty;
    
    public string? PostalCode { get; set; }
    
    public bool IsDefault { get; set; }
    
    public Order? Order { get; set; }
}

/// <summary>
/// 创建订单请求
/// </summary>
public class CreateOrderRequest
{
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public List<OrderItemRequest> Items { get; set; } = new();
    
    [Required]
    public ShippingAddressRequest ShippingAddress { get; set; } = new();
    
    public string? CouponCode { get; set; }
    
    public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Online;
    
    public string? Remarks { get; set; }
}

/// <summary>
/// 订单项请求
/// </summary>
public class OrderItemRequest
{
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    [Range(1, int.MaxValue)]
    public int Quantity { get; set; }
    
    public Dictionary<string, object> ProductAttributes { get; set; } = new();
}

/// <summary>
/// 配送地址请求
/// </summary>
public class ShippingAddressRequest
{
    [Required]
    public string ReceiverName { get; set; } = string.Empty;
    
    [Required]
    public string Phone { get; set; } = string.Empty;
    
    [Required]
    public string Province { get; set; } = string.Empty;
    
    [Required]
    public string City { get; set; } = string.Empty;
    
    [Required]
    public string District { get; set; } = string.Empty;
    
    [Required]
    public string DetailAddress { get; set; } = string.Empty;
    
    public string? PostalCode { get; set; }
}

/// <summary>
/// 订单响应
/// </summary>
public class OrderResponse
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public string? UserId { get; set; }
    public string? UserName { get; set; }
    public OrderStatus Status { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal ShippingFee { get; set; }
    public decimal FinalAmount { get; set; }
    public string? CouponCode { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public PaymentStatus PaymentStatus { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
    public DateTime? ShippedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public List<OrderItemResponse> Items { get; set; } = new();
    public ShippingAddressResponse? ShippingAddress { get; set; }
}

/// <summary>
/// 订单项响应
/// </summary>
public class OrderItemResponse
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string? ProductSku { get; set; }
    public string? ProductImage { get; set; }
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public decimal TotalPrice { get; set; }
    public Dictionary<string, object> ProductAttributes { get; set; } = new();
}

/// <summary>
/// 配送地址响应
/// </summary>
public class ShippingAddressResponse
{
    public int Id { get; set; }
    public string ReceiverName { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Province { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string District { get; set; } = string.Empty;
    public string DetailAddress { get; set; } = string.Empty;
    public string? PostalCode { get; set; }
}

/// <summary>
/// 订单状态枚举
/// </summary>
public enum OrderStatus
{
    Pending = 1,        // 待付款
    Paid = 2,           // 已付款
    Processing = 3,     // 处理中
    Shipped = 4,        // 已发货
    Delivered = 5,      // 已送达
    Completed = 6,      // 已完成
    Cancelled = 7,      // 已取消
    Refunded = 8        // 已退款
}

/// <summary>
/// 支付方式枚举
/// </summary>
public enum PaymentMethod
{
    Online = 1,         // 在线支付
    COD = 2,            // 货到付款
    BankTransfer = 3,   // 银行转账
    Wallet = 4          // 钱包支付
}

/// <summary>
/// 支付状态枚举
/// </summary>
public enum PaymentStatus
{
    Pending = 1,        // 待支付
    Processing = 2,     // 支付中
    Paid = 3,           // 已支付
    Failed = 4,         // 支付失败
    Refunded = 5,       // 已退款
    PartialRefunded = 6 // 部分退款
}
