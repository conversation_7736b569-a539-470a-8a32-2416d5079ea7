# Persimmon Chic 架构设计文档

## 1. 系统概述

Persimmon Chic 是一个基于微服务架构的电商平台演示应用，旨在展示现代分布式系统设计模式和 AntdUI 组件库在实际业务场景中的应用。

### 1.1 设计目标

- **微服务架构演示**: 展示完整的微服务设计模式和最佳实践
- **AntdUI 组件展示**: 在真实业务场景中展示 AntdUI 的各种组件
- **技术栈整合**: 演示 .NET 9.0 生态系统的现代开发方式
- **可扩展性**: 提供易于扩展的架构基础

### 1.2 核心特性

- 🏗️ **分层架构**: 清晰的分层设计，职责分离
- 🔄 **服务治理**: 服务发现、负载均衡、健康检查
- 🔐 **安全认证**: JWT 令牌认证和授权机制
- 📊 **监控告警**: 系统监控、日志记录、性能指标
- 🎨 **现代UI**: 基于 AntdUI 的美观用户界面

## 2. 架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                    │
│                   AntdUI WinForms 应用                      │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/REST
┌─────────────────────────────────────────────────────────────┐
│                  API Gateway 层                            │
│              统一入口、路由、认证、限流                        │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/REST
┌─────────────────────────────────────────────────────────────┐
│                    业务服务域                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  用户服务    │ │  商品服务    │ │  订单服务    │           │
│  │ UserService │ │ProductService│ │OrderService │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    平台服务域                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  认证服务    │ │  消息服务    │ │  监控服务    │           │
│  │ AuthService │ │MessageService││MonitorService│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   基础设施层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  数据访问    │ │  服务发现    │ │  负载均衡    │           │
│  │ DataAccess  │ │ServiceDiscov │ │LoadBalancer │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 技术架构

#### 2.2.1 技术栈选择

| 层次 | 技术选择 | 说明 |
|------|----------|------|
| 客户端 | AntdUI + WinForms | 现代化的桌面UI组件库 |
| API网关 | ASP.NET Core | 高性能Web框架 |
| 微服务 | ASP.NET Core Web API | RESTful API服务 |
| 数据访问 | 内存数据库 | 演示用轻量级存储 |
| 认证授权 | JWT Token | 无状态认证机制 |
| 通信协议 | HTTP/REST | 标准化API通信 |
| 文档 | Swagger/OpenAPI | 自动化API文档 |

#### 2.2.2 设计模式

- **微服务模式**: 按业务领域拆分服务
- **API网关模式**: 统一入口和路由
- **仓储模式**: 数据访问抽象
- **工作单元模式**: 事务管理
- **依赖注入**: 控制反转容器
- **中间件模式**: 请求处理管道

## 3. 服务设计

### 3.1 API Gateway (端口: 5000)

**职责**:
- 统一API入口
- 请求路由和转发
- 认证和授权
- 限流和熔断
- 日志记录

**核心功能**:
- 服务发现和负载均衡
- JWT令牌验证
- 请求/响应日志
- 错误处理和重试

### 3.2 用户服务 (端口: 5001)

**职责**:
- 用户注册和登录
- 用户信息管理
- 权限和角色管理
- 密码管理

**API端点**:
- `POST /api/users/login` - 用户登录
- `POST /api/users/register` - 用户注册
- `GET /api/users/{id}` - 获取用户信息
- `PUT /api/users` - 更新用户信息
- `POST /api/users/change-password` - 修改密码

### 3.3 商品服务 (端口: 5002) [规划中]

**职责**:
- 商品信息管理
- 分类管理
- 库存管理
- 商品搜索

### 3.4 订单服务 (端口: 5003) [规划中]

**职责**:
- 订单创建和管理
- 订单状态跟踪
- 支付处理
- 订单统计

## 4. 数据模型

### 4.1 核心实体

#### 用户实体 (User)
```csharp
public class User
{
    public int Id { get; set; }
    public string Username { get; set; }
    public string Email { get; set; }
    public string PasswordHash { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}
```

#### 商品实体 (Product)
```csharp
public class Product
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public decimal Price { get; set; }
    public int Stock { get; set; }
    public int CategoryId { get; set; }
    public ProductStatus Status { get; set; }
}
```

#### 订单实体 (Order)
```csharp
public class Order
{
    public int Id { get; set; }
    public string OrderNumber { get; set; }
    public int UserId { get; set; }
    public OrderStatus Status { get; set; }
    public decimal TotalAmount { get; set; }
    public DateTime CreatedAt { get; set; }
}
```

### 4.2 数据访问模式

- **仓储模式**: `IRepository<T>` 接口抽象数据访问
- **工作单元**: `IUnitOfWork` 管理事务边界
- **内存存储**: 演示用的内存数据库实现

## 5. 安全设计

### 5.1 认证机制

- **JWT令牌**: 无状态认证
- **令牌过期**: 24小时有效期
- **刷新机制**: 支持令牌刷新
- **角色授权**: 基于角色的访问控制

### 5.2 安全措施

- **密码加密**: BCrypt哈希算法
- **HTTPS通信**: 生产环境强制HTTPS
- **CORS配置**: 跨域请求控制
- **输入验证**: 数据注解验证

## 6. 监控和日志

### 6.1 日志记录

- **结构化日志**: 使用 ILogger 接口
- **请求追踪**: 每个请求分配唯一ID
- **错误日志**: 异常信息和堆栈跟踪
- **性能日志**: 请求响应时间

### 6.2 健康检查

- **服务健康**: `/health` 端点
- **依赖检查**: 数据库连接状态
- **自动恢复**: 不健康服务自动移除

## 7. 部署架构

### 7.1 开发环境

- **本地运行**: 多个控制台应用
- **端口分配**: 固定端口避免冲突
- **配置管理**: appsettings.json文件

### 7.2 生产环境 [规划]

- **容器化**: Docker容器部署
- **编排**: Kubernetes集群管理
- **负载均衡**: Nginx反向代理
- **数据库**: MySQL/PostgreSQL

## 8. 扩展性设计

### 8.1 水平扩展

- **无状态服务**: 支持多实例部署
- **负载均衡**: 请求分发到多个实例
- **数据库分片**: 支持数据水平分割

### 8.2 功能扩展

- **插件架构**: 支持功能模块插件化
- **事件驱动**: 异步事件处理机制
- **缓存层**: Redis分布式缓存

## 9. 性能优化

### 9.1 缓存策略

- **内存缓存**: 热点数据缓存
- **分布式缓存**: Redis集群
- **CDN**: 静态资源加速

### 9.2 数据库优化

- **索引优化**: 查询性能优化
- **连接池**: 数据库连接复用
- **读写分离**: 主从数据库架构

## 10. 总结

Persimmon Chic 项目展示了现代微服务架构的完整实现，从基础设施到业务逻辑，从前端UI到后端服务，提供了一个完整的学习和参考案例。

项目的设计遵循了微服务的最佳实践，包括服务拆分、数据隔离、通信机制、监控告警等各个方面，为实际项目开发提供了有价值的参考。
