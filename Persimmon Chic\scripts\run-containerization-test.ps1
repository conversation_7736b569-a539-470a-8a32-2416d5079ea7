# Persimmon Chic 完整容器化测试脚本
param(
    [switch]$SkipBuild,
    [switch]$SkipCleanup,
    [int]$TimeoutSeconds = 300
)

Write-Host "=== Persimmon Chic 完整容器化测试 ===" -ForegroundColor Green
Write-Host "开始时间: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# 测试结果记录
$testResults = @{
    "DockerEnvironment" = $false
    "ImageBuild" = $false
    "ContainerStartup" = $false
    "HealthChecks" = $false
    "ServiceConnectivity" = $false
    "DataPersistence" = $false
    "FailureRecovery" = $false
}

$totalTests = $testResults.Count
$passedTests = 0

function Test-DockerEnvironment {
    Write-Host "🔍 第一步：Docker环境检查" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 检查Docker版本
        $dockerVersion = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Docker已安装: $dockerVersion" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Docker未安装" -ForegroundColor Red
            return $false
        }
        
        # 检查Docker服务状态
        $dockerInfo = & docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Docker服务运行正常" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Docker服务未运行，请启动Docker Desktop" -ForegroundColor Red
            return $false
        }
        
        # 检查可用资源
        Write-Host "  ✓ Docker环境检查通过" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "  ✗ Docker环境检查失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ImageBuild {
    Write-Host ""
    Write-Host "🏗️ 第二步：镜像构建测试" -ForegroundColor Cyan
    Write-Host ""
    
    if ($SkipBuild) {
        Write-Host "  跳过镜像构建测试" -ForegroundColor Yellow
        return $true
    }
    
    try {
        # 构建Gateway服务作为测试
        Write-Host "  构建Gateway服务镜像..." -ForegroundColor Yellow
        $buildResult = & docker build -f src/Gateway/Dockerfile -t persimmonchic-gateway:test . 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Gateway镜像构建成功" -ForegroundColor Green
            
            # 检查镜像大小
            $imageInfo = & docker images persimmonchic-gateway:test --format "{{.Size}}"
            Write-Host "  ✓ 镜像大小: $imageInfo" -ForegroundColor Green
            
            return $true
        } else {
            Write-Host "  ✗ 镜像构建失败" -ForegroundColor Red
            Write-Host "  错误信息: $buildResult" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 镜像构建异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ContainerStartup {
    Write-Host ""
    Write-Host "🚀 第三步：容器启动测试" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 启动基础设施服务
        Write-Host "  启动Redis容器..." -ForegroundColor Yellow
        & docker run -d --name test-redis -p 6380:6379 redis:7-alpine > $null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Redis容器启动成功" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Redis容器启动失败" -ForegroundColor Red
            return $false
        }
        
        # 等待容器启动
        Start-Sleep -Seconds 5
        
        # 检查容器状态
        $containerStatus = & docker ps --filter "name=test-redis" --format "{{.Status}}"
        if ($containerStatus -like "*Up*") {
            Write-Host "  ✓ 容器运行状态正常: $containerStatus" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ✗ 容器状态异常: $containerStatus" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 容器启动测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-HealthChecks {
    Write-Host ""
    Write-Host "✅ 第四步：健康检查测试" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 测试Redis连接
        Write-Host "  测试Redis健康检查..." -ForegroundColor Yellow
        $redisHealth = & docker exec test-redis redis-cli ping 2>$null
        
        if ($redisHealth -eq "PONG") {
            Write-Host "  ✓ Redis健康检查通过" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ✗ Redis健康检查失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 健康检查测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ServiceConnectivity {
    Write-Host ""
    Write-Host "🔗 第五步：服务连接测试" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 创建测试网络
        Write-Host "  创建测试网络..." -ForegroundColor Yellow
        & docker network create test-network > $null
        
        # 将Redis连接到网络
        & docker network connect test-network test-redis > $null
        
        # 启动测试容器验证网络连接
        Write-Host "  测试容器间网络连接..." -ForegroundColor Yellow
        $networkTest = & docker run --rm --network test-network redis:7-alpine redis-cli -h test-redis ping 2>$null
        
        if ($networkTest -eq "PONG") {
            Write-Host "  ✓ 容器间网络连接正常" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ✗ 容器间网络连接失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 服务连接测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-DataPersistence {
    Write-Host ""
    Write-Host "💾 第六步：数据持久化测试" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 写入测试数据
        Write-Host "  写入测试数据..." -ForegroundColor Yellow
        & docker exec test-redis redis-cli set test:persistence "container-test-data" > $null
        
        # 读取数据验证
        $testData = & docker exec test-redis redis-cli get test:persistence 2>$null
        
        if ($testData -eq "container-test-data") {
            Write-Host "  ✓ 数据写入读取正常" -ForegroundColor Green
            
            # 重启容器测试持久化
            Write-Host "  重启容器测试持久化..." -ForegroundColor Yellow
            & docker restart test-redis > $null
            Start-Sleep -Seconds 3
            
            $persistedData = & docker exec test-redis redis-cli get test:persistence 2>$null
            if ($persistedData -eq "container-test-data") {
                Write-Host "  ✓ 数据持久化验证通过" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  ✗ 数据持久化验证失败" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "  ✗ 数据写入读取失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 数据持久化测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-FailureRecovery {
    Write-Host ""
    Write-Host "🛠️ 第七步：故障恢复测试" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 模拟容器故障
        Write-Host "  模拟容器故障..." -ForegroundColor Yellow
        & docker kill test-redis > $null
        
        # 检查容器状态
        $killedStatus = & docker ps -a --filter "name=test-redis" --format "{{.Status}}"
        Write-Host "  容器状态: $killedStatus" -ForegroundColor Gray
        
        # 重启容器
        Write-Host "  重启容器..." -ForegroundColor Yellow
        & docker start test-redis > $null
        Start-Sleep -Seconds 3
        
        # 验证恢复
        $recoveredStatus = & docker ps --filter "name=test-redis" --format "{{.Status}}"
        if ($recoveredStatus -like "*Up*") {
            Write-Host "  ✓ 容器故障恢复成功: $recoveredStatus" -ForegroundColor Green
            
            # 验证服务可用性
            $serviceTest = & docker exec test-redis redis-cli ping 2>$null
            if ($serviceTest -eq "PONG") {
                Write-Host "  ✓ 服务功能恢复正常" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  ✗ 服务功能恢复失败" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "  ✗ 容器故障恢复失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 故障恢复测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Cleanup-TestResources {
    if ($SkipCleanup) {
        Write-Host "跳过清理测试资源" -ForegroundColor Yellow
        return
    }
    
    Write-Host ""
    Write-Host "🧹 清理测试资源..." -ForegroundColor Yellow
    
    # 停止并删除测试容器
    & docker stop test-redis > $null 2>&1
    & docker rm test-redis > $null 2>&1
    
    # 删除测试网络
    & docker network rm test-network > $null 2>&1
    
    # 删除测试镜像
    & docker rmi persimmonchic-gateway:test > $null 2>&1
    
    Write-Host "  ✓ 测试资源清理完成" -ForegroundColor Green
}

# 主测试流程
try {
    # 执行所有测试
    $testResults["DockerEnvironment"] = Test-DockerEnvironment
    if ($testResults["DockerEnvironment"]) { $passedTests++ }
    
    $testResults["ImageBuild"] = Test-ImageBuild
    if ($testResults["ImageBuild"]) { $passedTests++ }
    
    $testResults["ContainerStartup"] = Test-ContainerStartup
    if ($testResults["ContainerStartup"]) { $passedTests++ }
    
    $testResults["HealthChecks"] = Test-HealthChecks
    if ($testResults["HealthChecks"]) { $passedTests++ }
    
    $testResults["ServiceConnectivity"] = Test-ServiceConnectivity
    if ($testResults["ServiceConnectivity"]) { $passedTests++ }
    
    $testResults["DataPersistence"] = Test-DataPersistence
    if ($testResults["DataPersistence"]) { $passedTests++ }
    
    $testResults["FailureRecovery"] = Test-FailureRecovery
    if ($testResults["FailureRecovery"]) { $passedTests++ }
}
finally {
    # 清理资源
    Cleanup-TestResources
}

# 显示测试总结
Write-Host ""
Write-Host "📊 测试总结" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

foreach ($test in $testResults.GetEnumerator()) {
    $status = if ($test.Value) { "✓ 通过" } else { "✗ 失败" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "$($test.Key): $status" -ForegroundColor $color
}

Write-Host ""
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
$overallStatus = if ($passedTests -eq $totalTests) { "✓ 全部通过" } else { "⚠ 部分失败" }
$overallColor = if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" }

Write-Host "总体结果: $overallStatus ($passedTests/$totalTests, $successRate%)" -ForegroundColor $overallColor
Write-Host "结束时间: $(Get-Date)" -ForegroundColor Yellow

# 提供下一步建议
Write-Host ""
Write-Host "🎯 下一步建议:" -ForegroundColor Cyan
if ($passedTests -eq $totalTests) {
    Write-Host "  1. 执行完整的服务构建: .\scripts\docker-build.ps1 -Service all" -ForegroundColor White
    Write-Host "  2. 启动完整开发环境: .\scripts\docker-run.ps1 -Environment development -Action up -Detach" -ForegroundColor White
    Write-Host "  3. 运行服务功能测试: .\scripts\test-services.ps1" -ForegroundColor White
    Write-Host "  4. 准备Kubernetes部署: .\scripts\k8s-deploy.ps1 -Action deploy" -ForegroundColor White
} else {
    Write-Host "  1. 检查Docker Desktop是否正常运行" -ForegroundColor White
    Write-Host "  2. 确保有足够的系统资源（8GB+ RAM）" -ForegroundColor White
    Write-Host "  3. 检查网络连接，确保可以拉取基础镜像" -ForegroundColor White
    Write-Host "  4. 重新运行测试: .\scripts\run-containerization-test.ps1" -ForegroundColor White
}

Write-Host ""

# 退出代码
exit $(if ($passedTests -eq $totalTests) { 0 } else { 1 })
