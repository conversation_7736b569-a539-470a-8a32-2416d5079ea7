{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "PersimmonChic.PricingService": "Debug"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "Per<PERSON><PERSON>on<PERSON><PERSON>_Super_Secret_Key_2024_Demo_Application", "Issuer": "PersimmonChic", "Audience": "PersimmonChic-Users", "ExpirationHours": 24}, "ConnectionStrings": {"Redis": "localhost:6379", "Database": "Data Source=pricing.db"}, "PricingSettings": {"EnableDynamicPricing": true, "EnableChannelPricing": true, "EnableUserTierPricing": true, "EnableRegionalPricing": true, "EnablePriceCache": true, "EnablePriceNotifications": true, "CacheExpirationMinutes": 15, "MaxPriceHistoryDays": 365, "DefaultCurrency": "CNY", "SupportedCurrencies": ["CNY", "USD", "EUR", "JPY"], "PriceCalculationSettings": {"MaxQuantityDiscount": 0.2, "MinPriceThreshold": 0.01, "MaxPriceMultiplier": 10.0, "RoundingPrecision": 2}, "DynamicPricingSettings": {"EnableRealTimePricing": true, "MaxPriceAdjustment": 0.5, "MinPriceAdjustment": -0.3, "RuleEvaluationIntervalMinutes": 5, "EnableDemandBasedPricing": true, "EnableInventoryBasedPricing": true, "EnableTimeBasedPricing": true}, "ChannelSettings": {"DefaultChannels": [{"Code": "online", "Name": "在线商城", "DiscountRate": 0.05}, {"Code": "retail", "Name": "零售门店", "DiscountRate": 0.0}, {"Code": "wholesale", "Name": "批发渠道", "DiscountRate": 0.15}]}, "UserTierSettings": {"TierDiscounts": {"Bronze": 0.0, "Silver": 0.05, "Gold": 0.1, "Platinum": 0.15, "Diamond": 0.2}}, "RegionalSettings": {"DefaultRegion": "CN", "TaxRates": {"CN": 0.13, "US": 0.08, "EU": 0.2, "JP": 0.1}, "ShippingCosts": {"CN": 10.0, "US": 25.0, "EU": 20.0, "JP": 15.0}}, "NotificationSettings": {"EnableEmailNotifications": false, "EnableSmsNotifications": false, "EnableWebhookNotifications": true, "WebhookTimeout": 10, "RetryAttempts": 3, "RetryDelaySeconds": 5, "NotificationCooldownMinutes": 5}, "CacheSettings": {"EnableDistributedCache": true, "DefaultExpirationMinutes": 15, "SlidingExpirationMinutes": 30, "MaxCacheSize": 1000, "EnableCacheWarmup": true, "WarmupProductCount": 100}}, "ServiceDiscovery": {"ServiceName": "PricingService", "Address": "localhost", "Port": 5005, "HealthCheckEndpoint": "/health", "GatewayUrl": "http://localhost:5000", "Tags": ["pricing", "dynamic-pricing", "channel-pricing", "cache"]}, "HealthChecks": {"UI": {"Enable": true, "Path": "/health-ui"}, "Checks": [{"Name": "Redis", "Type": "Redis", "ConnectionString": "localhost:6379"}, {"Name": "PricingEngine", "Type": "Custom", "Description": "价格引擎健康检查"}]}, "Monitoring": {"EnableMetrics": true, "MetricsEndpoint": "/metrics", "EnableTracing": true, "TracingEndpoint": "/trace", "SampleRate": 0.1, "CustomMetrics": ["price_calculations_total", "cache_hit_rate", "dynamic_pricing_adjustments", "notification_sent_total"]}, "Security": {"EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5000", "http://localhost:5001"], "EnableRateLimiting": true, "RateLimitSettings": {"MaxRequestsPerMinute": 1000, "MaxRequestsPerHour": 10000, "EnablePerUserLimiting": true}, "EnableRequestValidation": true, "MaxRequestSize": 1048576, "EnableAuditLogging": true}, "Features": {"DynamicPricing": {"Enabled": true, "Description": "基于需求和库存的动态定价"}, "ChannelPricing": {"Enabled": true, "Description": "不同销售渠道的差异化定价"}, "UserTierPricing": {"Enabled": true, "Description": "基于用户等级的会员定价"}, "RegionalPricing": {"Enabled": true, "Description": "基于地域的差异化定价"}, "PriceCache": {"Enabled": true, "Description": "高性能价格缓存系统"}, "PriceNotifications": {"Enabled": true, "Description": "价格变更实时通知"}, "BulkPriceUpdate": {"Enabled": true, "Description": "批量价格更新功能"}, "PriceHistory": {"Enabled": true, "Description": "价格历史记录和分析"}}, "Integration": {"ExternalServices": {"InventoryService": {"BaseUrl": "http://localhost:5006", "Timeout": 5000, "RetryCount": 3}, "DemandAnalysisService": {"BaseUrl": "http://localhost:5007", "Timeout": 10000, "RetryCount": 2}, "NotificationService": {"BaseUrl": "http://localhost:5008", "Timeout": 5000, "RetryCount": 3}}, "MessageQueue": {"Provider": "RabbitMQ", "ConnectionString": "amqp://localhost:5672", "ExchangeName": "pricing.events", "QueueName": "pricing.notifications"}}}