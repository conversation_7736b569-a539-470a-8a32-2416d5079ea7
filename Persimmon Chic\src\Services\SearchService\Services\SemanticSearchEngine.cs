using Microsoft.Extensions.Logging;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.SearchService.Models;
using PersimmonChic.Shared.Models;
using MathNet.Numerics.LinearAlgebra;
using System.Text.Json;

namespace PersimmonChic.SearchService.Services;

/// <summary>
/// 语义搜索引擎实现
/// </summary>
public class SemanticSearchEngine : ISemanticSearchEngine
{
    private readonly ILogger<SemanticSearchEngine> _logger;
    private readonly IRepository<SearchDocument> _documentRepository;
    private readonly IRepository<SemanticVector> _vectorRepository;
    private readonly ISearchCacheService _cacheService;
    
    // 模拟的预训练词向量模型
    private readonly Dictionary<string, List<float>> _wordVectors;
    private readonly int _vectorDimensions = 384; // 模拟BERT-base的维度
    private readonly Random _random = new(42);

    public SemanticSearchEngine(
        ILogger<SemanticSearchEngine> logger,
        IRepository<SearchDocument> documentRepository,
        IRepository<SemanticVector> vectorRepository,
        ISearchCacheService cacheService)
    {
        _logger = logger;
        _documentRepository = documentRepository;
        _vectorRepository = vectorRepository;
        _cacheService = cacheService;
        _wordVectors = InitializeWordVectors();
    }

    public async Task<ApiResponse<List<SearchResult>>> SemanticSearchAsync(string query, int topK = 20, float threshold = 0.7f)
    {
        try
        {
            _logger.LogInformation("开始语义搜索，查询: {Query}, TopK: {TopK}, 阈值: {Threshold}", query, topK, threshold);

            var startTime = DateTime.UtcNow;

            // 1. 生成查询向量
            var queryEmbeddingResponse = await GenerateEmbeddingAsync(query);
            if (!queryEmbeddingResponse.Success)
            {
                return ApiResponse<List<SearchResult>>.ErrorResult("生成查询向量失败");
            }

            var queryVector = queryEmbeddingResponse.Data;

            // 2. 获取所有文档
            var documents = await _documentRepository.GetAllAsync();
            var activeDocuments = documents.Where(d => d.IsActive).ToList();

            // 3. 计算相似度并排序
            var similarities = new List<(SearchDocument Document, float Similarity)>();

            foreach (var document in activeDocuments)
            {
                // 如果文档没有向量，生成一个
                if (!document.EmbeddingVector.Any())
                {
                    var docEmbeddingResponse = await GenerateEmbeddingAsync($"{document.Title} {document.Description}");
                    if (docEmbeddingResponse.Success)
                    {
                        document.EmbeddingVector = docEmbeddingResponse.Data;
                        await _documentRepository.UpdateAsync(document);
                    }
                }

                if (document.EmbeddingVector.Any())
                {
                    var similarityResponse = await CalculateSimilarityAsync(queryVector, document.EmbeddingVector);
                    if (similarityResponse.Success && similarityResponse.Data >= threshold)
                    {
                        similarities.Add((document, similarityResponse.Data));
                    }
                }
            }

            // 4. 按相似度排序并取前K个
            var topResults = similarities
                .OrderByDescending(x => x.Similarity)
                .Take(topK)
                .ToList();

            // 5. 构建搜索结果
            var searchResults = new List<SearchResult>();
            for (int i = 0; i < topResults.Count; i++)
            {
                var (document, similarity) = topResults[i];
                
                searchResults.Add(new SearchResult
                {
                    Id = document.Id,
                    Title = document.Title,
                    Description = document.Description,
                    Category = document.Category,
                    Brand = document.Brand,
                    Price = document.Price,
                    ImageUrl = $"/images/product_{document.Id}.jpg",
                    Url = $"/products/{document.Id}",
                    Score = similarity,
                    Relevance = similarity,
                    Tags = document.Tags,
                    Attributes = document.Attributes,
                    Highlights = GenerateHighlights(query, document),
                    LastUpdated = document.UpdatedAt
                });
            }

            var elapsedMs = (DateTime.UtcNow - startTime).TotalMilliseconds;
            
            _logger.LogInformation("语义搜索完成，查询: {Query}, 结果数: {Count}, 耗时: {ElapsedMs}ms", 
                query, searchResults.Count, elapsedMs);

            return ApiResponse<List<SearchResult>>.SuccessResult(searchResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "语义搜索时发生错误");
            return ApiResponse<List<SearchResult>>.ErrorResult($"语义搜索失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<float>>> GenerateEmbeddingAsync(string text)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return ApiResponse<List<float>>.ErrorResult("文本内容为空");
            }

            // 检查缓存
            var cacheKey = $"embedding:{text.GetHashCode()}";
            var cachedVector = await GetCachedVectorAsync(cacheKey);
            if (cachedVector != null)
            {
                return ApiResponse<List<float>>.SuccessResult(cachedVector);
            }

            // 模拟BERT嵌入生成过程
            var embedding = await GenerateMockEmbeddingAsync(text);

            // 缓存结果
            await CacheVectorAsync(cacheKey, embedding);

            _logger.LogDebug("生成文本嵌入向量，文本长度: {Length}, 向量维度: {Dimensions}", 
                text.Length, embedding.Count);

            return ApiResponse<List<float>>.SuccessResult(embedding);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成文本嵌入向量时发生错误");
            return ApiResponse<List<float>>.ErrorResult($"生成嵌入向量失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<float>> CalculateSimilarityAsync(List<float> vector1, List<float> vector2)
    {
        try
        {
            if (vector1.Count != vector2.Count)
            {
                return ApiResponse<float>.ErrorResult("向量维度不匹配");
            }

            if (!vector1.Any() || !vector2.Any())
            {
                return ApiResponse<float>.SuccessResult(0f);
            }

            // 计算余弦相似度
            var dotProduct = vector1.Zip(vector2, (a, b) => a * b).Sum();
            var magnitude1 = Math.Sqrt(vector1.Sum(x => x * x));
            var magnitude2 = Math.Sqrt(vector2.Sum(x => x * x));

            if (magnitude1 == 0 || magnitude2 == 0)
            {
                return ApiResponse<float>.SuccessResult(0f);
            }

            var similarity = (float)(dotProduct / (magnitude1 * magnitude2));
            
            // 确保相似度在[0,1]范围内
            similarity = Math.Max(0f, Math.Min(1f, similarity));

            return ApiResponse<float>.SuccessResult(similarity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算向量相似度时发生错误");
            return ApiResponse<float>.ErrorResult($"计算相似度失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> IndexDocumentsAsync(List<SearchDocument> documents)
    {
        try
        {
            _logger.LogInformation("开始批量索引文档，数量: {Count}", documents.Count);

            var successCount = 0;
            var failureCount = 0;

            foreach (var document in documents)
            {
                try
                {
                    // 生成文档嵌入向量
                    var text = $"{document.Title} {document.Description} {document.Content}";
                    var embeddingResponse = await GenerateEmbeddingAsync(text);
                    
                    if (embeddingResponse.Success)
                    {
                        document.EmbeddingVector = embeddingResponse.Data;
                        document.UpdatedAt = DateTime.UtcNow;
                        
                        await _documentRepository.AddAsync(document);
                        successCount++;
                    }
                    else
                    {
                        failureCount++;
                        _logger.LogWarning("为文档 {DocumentId} 生成嵌入向量失败", document.Id);
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    _logger.LogError(ex, "索引文档 {DocumentId} 时发生错误", document.Id);
                }
            }

            _logger.LogInformation("批量索引完成，成功: {SuccessCount}, 失败: {FailureCount}", 
                successCount, failureCount);

            return ApiResponse<bool>.SuccessResult(failureCount == 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量索引文档时发生错误");
            return ApiResponse<bool>.ErrorResult($"批量索引失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateDocumentAsync(SearchDocument document)
    {
        try
        {
            // 重新生成嵌入向量
            var text = $"{document.Title} {document.Description} {document.Content}";
            var embeddingResponse = await GenerateEmbeddingAsync(text);
            
            if (embeddingResponse.Success)
            {
                document.EmbeddingVector = embeddingResponse.Data;
                document.UpdatedAt = DateTime.UtcNow;
                
                await _documentRepository.UpdateAsync(document);
                
                _logger.LogInformation("更新文档索引成功: {DocumentId}", document.Id);
                return ApiResponse<bool>.SuccessResult(true);
            }
            else
            {
                return ApiResponse<bool>.ErrorResult("生成嵌入向量失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新文档索引时发生错误");
            return ApiResponse<bool>.ErrorResult($"更新文档索引失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> DeleteDocumentAsync(int documentId)
    {
        try
        {
            var document = await _documentRepository.GetByIdAsync(documentId);
            if (document != null)
            {
                await _documentRepository.DeleteAsync(document);
                _logger.LogInformation("删除文档索引成功: {DocumentId}", documentId);
            }
            
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文档索引时发生错误");
            return ApiResponse<bool>.ErrorResult($"删除文档索引失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private Dictionary<string, List<float>> InitializeWordVectors()
    {
        // 模拟预训练词向量
        var vectors = new Dictionary<string, List<float>>();
        var commonWords = new[]
        {
            "手机", "电脑", "耳机", "音响", "相机", "平板", "手表", "键盘", "鼠标", "显示器",
            "智能", "无线", "蓝牙", "高清", "便携", "专业", "游戏", "办公", "家用", "商务",
            "苹果", "华为", "小米", "三星", "联想", "戴尔", "索尼", "佳能", "尼康", "罗技"
        };

        foreach (var word in commonWords)
        {
            vectors[word] = GenerateRandomVector(_vectorDimensions);
        }

        return vectors;
    }

    private List<float> GenerateRandomVector(int dimensions)
    {
        var vector = new List<float>();
        for (int i = 0; i < dimensions; i++)
        {
            vector.Add((float)(_random.NextGaussian() * 0.1)); // 小的随机值
        }
        
        // 归一化向量
        var magnitude = Math.Sqrt(vector.Sum(x => x * x));
        if (magnitude > 0)
        {
            for (int i = 0; i < vector.Count; i++)
            {
                vector[i] = (float)(vector[i] / magnitude);
            }
        }
        
        return vector;
    }

    private async Task<List<float>> GenerateMockEmbeddingAsync(string text)
    {
        // 模拟BERT嵌入生成
        await Task.Delay(10); // 模拟计算时间

        var words = text.ToLower()
            .Split(new[] { ' ', '\t', '\n', '\r', ',', '.', '!', '?', ';', ':' }, 
                   StringSplitOptions.RemoveEmptyEntries);

        var embedding = new List<float>(new float[_vectorDimensions]);

        // 基于词向量的简单平均
        var matchedWords = 0;
        foreach (var word in words)
        {
            if (_wordVectors.ContainsKey(word))
            {
                var wordVector = _wordVectors[word];
                for (int i = 0; i < _vectorDimensions; i++)
                {
                    embedding[i] += wordVector[i];
                }
                matchedWords++;
            }
        }

        // 如果没有匹配的词，生成随机向量
        if (matchedWords == 0)
        {
            embedding = GenerateRandomVector(_vectorDimensions);
        }
        else
        {
            // 平均化
            for (int i = 0; i < _vectorDimensions; i++)
            {
                embedding[i] /= matchedWords;
            }
        }

        // 添加一些基于文本特征的调整
        var textLength = text.Length;
        var wordCount = words.Length;
        
        // 基于文本长度和词数的特征
        for (int i = 0; i < Math.Min(10, _vectorDimensions); i++)
        {
            embedding[i] += (float)(textLength % 100) / 1000f;
            embedding[i] += (float)(wordCount % 50) / 500f;
        }

        return embedding;
    }

    private List<SearchHighlight> GenerateHighlights(string query, SearchDocument document)
    {
        var highlights = new List<SearchHighlight>();
        var queryWords = query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);

        // 在标题中查找高亮
        var titleHighlights = FindHighlights(document.Title, queryWords);
        if (titleHighlights.Any())
        {
            highlights.Add(new SearchHighlight
            {
                Field = "title",
                Fragments = titleHighlights
            });
        }

        // 在描述中查找高亮
        var descriptionHighlights = FindHighlights(document.Description, queryWords);
        if (descriptionHighlights.Any())
        {
            highlights.Add(new SearchHighlight
            {
                Field = "description",
                Fragments = descriptionHighlights
            });
        }

        return highlights;
    }

    private List<string> FindHighlights(string text, string[] queryWords)
    {
        var highlights = new List<string>();
        var lowerText = text.ToLower();

        foreach (var word in queryWords)
        {
            var index = lowerText.IndexOf(word.ToLower());
            if (index >= 0)
            {
                var start = Math.Max(0, index - 20);
                var end = Math.Min(text.Length, index + word.Length + 20);
                var fragment = text.Substring(start, end - start);
                
                // 添加高亮标记
                fragment = fragment.Replace(word, $"<em>{word}</em>", StringComparison.OrdinalIgnoreCase);
                highlights.Add(fragment);
            }
        }

        return highlights;
    }

    private async Task<List<float>?> GetCachedVectorAsync(string cacheKey)
    {
        try
        {
            // 这里应该从缓存服务获取，简化实现
            return null;
        }
        catch
        {
            return null;
        }
    }

    private async Task CacheVectorAsync(string cacheKey, List<float> vector)
    {
        try
        {
            // 这里应该缓存到缓存服务，简化实现
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "缓存向量时发生错误");
        }
    }
}

/// <summary>
/// Random扩展方法，用于生成正态分布随机数
/// </summary>
public static class RandomExtensions2
{
    private static double _u1 = 0;
    private static double _u2 = 0;
    private static bool _hasSpare = false;
    private static readonly object _lock = new object();

    public static double NextGaussian(this Random random)
    {
        lock (_lock)
        {
            if (_hasSpare)
            {
                _hasSpare = false;
                return Math.Sqrt(-2.0 * Math.Log(_u1)) * Math.Sin(2.0 * Math.PI * _u2);
            }

            _hasSpare = true;
            _u1 = random.NextDouble();
            _u2 = random.NextDouble();
            return Math.Sqrt(-2.0 * Math.Log(_u1)) * Math.Cos(2.0 * Math.PI * _u2);
        }
    }
}
