apiVersion: v1
kind: Secret
metadata:
  name: persimmon-chic-secrets
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: persimmon-chic
    app.kubernetes.io/component: secrets
type: Opaque
data:
  # JWT密钥 (base64编码)
  JWT_SECRET_KEY: UGVyc2ltbW9uQ2hpY19TdXBlcl9TZWNyZXRfS2V5XzIwMjRfUHJvZHVjdGlvbg==
  
  # 数据库密码 (base64编码)
  SQL_PASSWORD: UGVyc2ltbW9uQ2hpYzEyMyE=
  
  # Redis密码 (如果需要)
  REDIS_PASSWORD: ""
  
  # API密钥
  API_KEY: UGVyc2ltbW9uQ2hpY0FQSUtleTIwMjQ=
  
  # 第三方服务密钥
  STRIPE_SECRET_KEY: ""
  ALIYUN_ACCESS_KEY: ""
  ALIYUN_SECRET_KEY: ""

---
apiVersion: v1
kind: Secret
metadata:
  name: persimmon-chic-dev-secrets
  namespace: persimmon-chic-dev
  labels:
    app.kubernetes.io/name: persimmon-chic
    app.kubernetes.io/component: secrets
    environment: development
type: Opaque
data:
  # JWT密钥 (base64编码)
  JWT_SECRET_KEY: UGVyc2ltbW9uQ2hpY19EZXZfU2VjcmV0X0tleV8yMDI0
  
  # 数据库密码 (base64编码)
  SQL_PASSWORD: RGV2UGFzc3dvcmQxMjMh
  
  # Redis密码
  REDIS_PASSWORD: ""
  
  # API密钥
  API_KEY: UGVyc2ltbW9uQ2hpY0RldkFQSUtleTIwMjQ=

---
# 用于拉取私有镜像的Secret (如果需要)
apiVersion: v1
kind: Secret
metadata:
  name: persimmon-chic-registry-secret
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: persimmon-chic
    app.kubernetes.io/component: registry
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************
