using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.RecommendationService.Models;
using PersimmonChic.Shared.Models;
using System.Text.Json;

namespace PersimmonChic.RecommendationService.Services;

/// <summary>
/// A/B测试服务实现
/// </summary>
public class ABTestService : IABTestService
{
    private readonly ILogger<ABTestService> _logger;
    private readonly IRepository<ABTestConfig> _testRepository;
    private readonly IDistributedCache _cache;
    private const string TEST_CACHE_PREFIX = "abtest:";
    private const string METRICS_CACHE_PREFIX = "abtest_metrics:";

    public ABTestService(
        ILogger<ABTestService> logger,
        IRepository<ABTestConfig> testRepository,
        IDistributedCache cache)
    {
        _logger = logger;
        _testRepository = testRepository;
        _cache = cache;
    }

    public async Task<ApiResponse<ABTestConfig>> CreateABTestAsync(ABTestConfig config)
    {
        try
        {
            _logger.LogInformation("创建A/B测试: {TestName}", config.Name);

            // 验证配置
            if (config.Variants.Count < 2)
            {
                return ApiResponse<ABTestConfig>.ErrorResult("A/B测试至少需要2个变体");
            }

            var totalWeight = config.Variants.Sum(v => v.Weight);
            if (Math.Abs(totalWeight - 1.0f) > 0.01f)
            {
                return ApiResponse<ABTestConfig>.ErrorResult("变体权重总和必须等于1.0");
            }

            // 保存配置
            await _testRepository.AddAsync(config);

            // 缓存配置
            var cacheKey = $"{TEST_CACHE_PREFIX}{config.Name}";
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(config),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });

            _logger.LogInformation("A/B测试创建成功: {TestName}, 变体数: {VariantCount}", 
                config.Name, config.Variants.Count);

            return ApiResponse<ABTestConfig>.SuccessResult(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建A/B测试时发生错误");
            return ApiResponse<ABTestConfig>.ErrorResult($"创建A/B测试失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<ABTestVariant>> GetUserVariantAsync(string testName, int userId)
    {
        try
        {
            // 从缓存获取测试配置
            var cacheKey = $"{TEST_CACHE_PREFIX}{testName}";
            var cachedConfig = await _cache.GetStringAsync(cacheKey);
            
            ABTestConfig? config = null;
            
            if (!string.IsNullOrEmpty(cachedConfig))
            {
                config = JsonSerializer.Deserialize<ABTestConfig>(cachedConfig);
            }
            else
            {
                // 从数据库获取
                var configs = await _testRepository.FindAsync(c => c.Name == testName && c.IsActive);
                config = configs.FirstOrDefault();
                
                if (config != null)
                {
                    await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(config),
                        new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });
                }
            }

            if (config == null || !config.IsActive)
            {
                return ApiResponse<ABTestVariant>.ErrorResult("A/B测试不存在或未激活");
            }

            // 检查测试是否在有效期内
            if (config.EndDate.HasValue && DateTime.UtcNow > config.EndDate.Value)
            {
                return ApiResponse<ABTestVariant>.ErrorResult("A/B测试已过期");
            }

            // 基于用户ID确定性地分配变体
            var variant = AssignUserToVariant(userId, config.Variants);
            
            _logger.LogDebug("用户 {UserId} 分配到变体: {VariantName} (测试: {TestName})", 
                userId, variant.Name, testName);

            return ApiResponse<ABTestVariant>.SuccessResult(variant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户A/B测试变体时发生错误");
            return ApiResponse<ABTestVariant>.ErrorResult($"获取测试变体失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> RecordMetricAsync(string testName, string variant, string metric, double value)
    {
        try
        {
            var metricsKey = $"{METRICS_CACHE_PREFIX}{testName}:{variant}:{metric}";
            
            // 获取现有指标
            var existingMetrics = await _cache.GetStringAsync(metricsKey);
            var metricsList = new List<double>();
            
            if (!string.IsNullOrEmpty(existingMetrics))
            {
                metricsList = JsonSerializer.Deserialize<List<double>>(existingMetrics) ?? new List<double>();
            }
            
            // 添加新指标值
            metricsList.Add(value);
            
            // 保持最近1000个指标值
            if (metricsList.Count > 1000)
            {
                metricsList = metricsList.TakeLast(1000).ToList();
            }
            
            // 更新缓存
            await _cache.SetStringAsync(metricsKey, JsonSerializer.Serialize(metricsList),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromDays(7) });

            _logger.LogDebug("记录A/B测试指标: {TestName}.{Variant}.{Metric} = {Value}", 
                testName, variant, metric, value);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录A/B测试指标时发生错误");
            return ApiResponse<bool>.ErrorResult($"记录指标失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<Dictionary<string, object>>> GetTestResultsAsync(string testName)
    {
        try
        {
            _logger.LogInformation("获取A/B测试结果: {TestName}", testName);

            // 获取测试配置
            var configs = await _testRepository.FindAsync(c => c.Name == testName);
            var config = configs.FirstOrDefault();
            
            if (config == null)
            {
                return ApiResponse<Dictionary<string, object>>.ErrorResult("A/B测试不存在");
            }

            var results = new Dictionary<string, object>
            {
                ["test_name"] = testName,
                ["start_date"] = config.StartDate,
                ["end_date"] = config.EndDate,
                ["is_active"] = config.IsActive,
                ["variants"] = new Dictionary<string, object>()
            };

            var variantResults = (Dictionary<string, object>)results["variants"];

            // 获取每个变体的指标
            foreach (var variant in config.Variants)
            {
                var variantMetrics = new Dictionary<string, object>();
                
                // 获取常见指标
                var commonMetrics = new[] { "recommendations_generated", "clicks", "conversions", "revenue" };
                
                foreach (var metric in commonMetrics)
                {
                    var metricsKey = $"{METRICS_CACHE_PREFIX}{testName}:{variant.Name}:{metric}";
                    var metricData = await _cache.GetStringAsync(metricsKey);
                    
                    if (!string.IsNullOrEmpty(metricData))
                    {
                        var values = JsonSerializer.Deserialize<List<double>>(metricData) ?? new List<double>();
                        
                        variantMetrics[metric] = new Dictionary<string, object>
                        {
                            ["count"] = values.Count,
                            ["sum"] = values.Sum(),
                            ["average"] = values.Any() ? values.Average() : 0,
                            ["min"] = values.Any() ? values.Min() : 0,
                            ["max"] = values.Any() ? values.Max() : 0
                        };
                    }
                    else
                    {
                        variantMetrics[metric] = new Dictionary<string, object>
                        {
                            ["count"] = 0,
                            ["sum"] = 0,
                            ["average"] = 0,
                            ["min"] = 0,
                            ["max"] = 0
                        };
                    }
                }
                
                variantResults[variant.Name] = variantMetrics;
            }

            return ApiResponse<Dictionary<string, object>>.SuccessResult(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取A/B测试结果时发生错误");
            return ApiResponse<Dictionary<string, object>>.ErrorResult($"获取测试结果失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> StopABTestAsync(string testName)
    {
        try
        {
            _logger.LogInformation("停止A/B测试: {TestName}", testName);

            var configs = await _testRepository.FindAsync(c => c.Name == testName);
            var config = configs.FirstOrDefault();
            
            if (config == null)
            {
                return ApiResponse<bool>.ErrorResult("A/B测试不存在");
            }

            // 停止测试
            config.IsActive = false;
            config.EndDate = DateTime.UtcNow;
            
            await _testRepository.UpdateAsync(config);

            // 清除缓存
            var cacheKey = $"{TEST_CACHE_PREFIX}{testName}";
            await _cache.RemoveAsync(cacheKey);

            _logger.LogInformation("A/B测试已停止: {TestName}", testName);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止A/B测试时发生错误");
            return ApiResponse<bool>.ErrorResult($"停止测试失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private ABTestVariant AssignUserToVariant(int userId, List<ABTestVariant> variants)
    {
        // 使用用户ID的哈希值确保一致性分配
        var hash = userId.GetHashCode();
        var normalizedHash = Math.Abs(hash % 10000) / 10000.0; // 0-1之间的值

        var cumulativeWeight = 0.0f;
        foreach (var variant in variants)
        {
            cumulativeWeight += variant.Weight;
            if (normalizedHash <= cumulativeWeight)
            {
                return variant;
            }
        }

        // 默认返回第一个变体
        return variants.First();
    }
}

/// <summary>
/// 推荐缓存服务实现
/// </summary>
public class RecommendationCacheService : IRecommendationCacheService
{
    private readonly ILogger<RecommendationCacheService> _logger;
    private readonly IDistributedCache _cache;
    private const string RECOMMENDATION_CACHE_PREFIX = "recommendation:";

    public RecommendationCacheService(
        ILogger<RecommendationCacheService> logger,
        IDistributedCache cache)
    {
        _logger = logger;
        _cache = cache;
    }

    public async Task<RecommendationResponse?> GetCachedRecommendationAsync(string cacheKey)
    {
        try
        {
            var cachedValue = await _cache.GetStringAsync(RECOMMENDATION_CACHE_PREFIX + cacheKey);
            
            if (!string.IsNullOrEmpty(cachedValue))
            {
                var recommendation = JsonSerializer.Deserialize<RecommendationResponse>(cachedValue);
                _logger.LogDebug("从缓存获取推荐: {CacheKey}", cacheKey);
                return recommendation;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存推荐时发生错误: {CacheKey}", cacheKey);
            return null;
        }
    }

    public async Task<bool> SetRecommendationCacheAsync(string cacheKey, RecommendationResponse recommendation, TimeSpan? expiration = null)
    {
        try
        {
            var options = new DistributedCacheEntryOptions();
            
            if (expiration.HasValue)
            {
                options.SlidingExpiration = expiration.Value;
            }
            else
            {
                options.SlidingExpiration = TimeSpan.FromMinutes(30); // 默认30分钟
            }

            var serializedRecommendation = JsonSerializer.Serialize(recommendation);
            await _cache.SetStringAsync(RECOMMENDATION_CACHE_PREFIX + cacheKey, serializedRecommendation, options);
            
            _logger.LogDebug("设置推荐缓存: {CacheKey}, 过期时间: {Expiration}", 
                cacheKey, expiration?.TotalMinutes ?? 30);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置推荐缓存时发生错误: {CacheKey}", cacheKey);
            return false;
        }
    }

    public async Task<bool> RemoveRecommendationCacheAsync(string cacheKey)
    {
        try
        {
            await _cache.RemoveAsync(RECOMMENDATION_CACHE_PREFIX + cacheKey);
            _logger.LogDebug("删除推荐缓存: {CacheKey}", cacheKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除推荐缓存时发生错误: {CacheKey}", cacheKey);
            return false;
        }
    }

    public async Task<bool> ClearUserRecommendationCacheAsync(int userId)
    {
        try
        {
            // 由于Redis不支持通配符删除，这里使用简化的实现
            // 实际生产环境中可能需要维护一个用户相关缓存键的集合
            
            var commonCacheKeys = new[]
            {
                GenerateRecommendationCacheKey(new RecommendationRequest { UserId = userId, Type = RecommendationType.Personalized }),
                GenerateRecommendationCacheKey(new RecommendationRequest { UserId = userId, Type = RecommendationType.Collaborative }),
                GenerateRecommendationCacheKey(new RecommendationRequest { UserId = userId, Type = RecommendationType.ContentBased }),
                GenerateRecommendationCacheKey(new RecommendationRequest { UserId = userId, Type = RecommendationType.Hybrid })
            };

            foreach (var key in commonCacheKeys)
            {
                await RemoveRecommendationCacheAsync(key);
            }

            _logger.LogInformation("清除用户 {UserId} 相关的推荐缓存", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除用户推荐缓存时发生错误: {UserId}", userId);
            return false;
        }
    }

    public string GenerateRecommendationCacheKey(RecommendationRequest request)
    {
        try
        {
            var keyParts = new List<string>
            {
                $"user_{request.UserId}",
                $"type_{request.Type}",
                $"count_{request.Count}"
            };

            if (!string.IsNullOrEmpty(request.Category))
            {
                keyParts.Add($"category_{request.Category}");
            }

            if (request.MinPrice.HasValue)
            {
                keyParts.Add($"minprice_{request.MinPrice.Value}");
            }

            if (request.MaxPrice.HasValue)
            {
                keyParts.Add($"maxprice_{request.MaxPrice.Value}");
            }

            if (request.ExcludeProductIds.Any())
            {
                var excludeIds = string.Join(",", request.ExcludeProductIds.OrderBy(x => x));
                keyParts.Add($"exclude_{excludeIds.GetHashCode()}");
            }

            // 添加时间戳（小时级别）以支持时间敏感的推荐
            var hourKey = DateTime.UtcNow.ToString("yyyyMMddHH");
            keyParts.Add($"time_{hourKey}");

            var cacheKey = string.Join(":", keyParts);
            
            // 确保缓存键长度不超过限制
            if (cacheKey.Length > 250)
            {
                var hash = cacheKey.GetHashCode().ToString("X");
                cacheKey = $"user_{request.UserId}:hash_{hash}";
            }

            return cacheKey;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成推荐缓存键时发生错误");
            return $"user_{request.UserId}:default";
        }
    }
}
