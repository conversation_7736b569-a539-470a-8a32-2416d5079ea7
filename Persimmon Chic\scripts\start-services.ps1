# Persimmon Chic 微服务启动脚本
# PowerShell 脚本用于启动所有微服务

Write-Host "🚀 启动 Persimmon Chic 微服务平台..." -ForegroundColor Green
Write-Host ""

# 检查 .NET SDK
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未找到 .NET SDK，请先安装 .NET 9.0 SDK" -ForegroundColor Red
    exit 1
}

# 设置工作目录
$rootPath = Split-Path -Parent $PSScriptRoot
Set-Location $rootPath

Write-Host "📁 工作目录: $rootPath" -ForegroundColor Cyan
Write-Host ""

# 构建解决方案
Write-Host "🔨 构建解决方案..." -ForegroundColor Yellow
try {
    dotnet build PersimmonChic.sln --configuration Release --verbosity quiet
    Write-Host "✅ 构建成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 构建失败" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 定义服务配置
$services = @(
    @{
        Name = "API Gateway"
        Path = "src/Gateway/PersimmonChic.Gateway.csproj"
        Port = 5000
        Url = "http://localhost:5000"
        Color = "Magenta"
    },
    @{
        Name = "User Service"
        Path = "src/Services/UserService/PersimmonChic.UserService.csproj"
        Port = 5001
        Url = "http://localhost:5001"
        Color = "Blue"
    }
)

# 启动服务的作业列表
$jobs = @()

# 启动每个服务
foreach ($service in $services) {
    Write-Host "🚀 启动 $($service.Name)..." -ForegroundColor $service.Color
    
    $job = Start-Job -ScriptBlock {
        param($projectPath, $serviceName)
        
        try {
            Set-Location $using:rootPath
            dotnet run --project $projectPath --configuration Release
        } catch {
            Write-Error "启动 $serviceName 失败: $_"
        }
    } -ArgumentList $service.Path, $service.Name
    
    $jobs += @{
        Job = $job
        Service = $service
    }
    
    # 等待一下让服务启动
    Start-Sleep -Seconds 2
}

Write-Host ""
Write-Host "⏳ 等待服务启动完成..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# 检查服务健康状态
Write-Host ""
Write-Host "🔍 检查服务健康状态..." -ForegroundColor Cyan

foreach ($service in $services) {
    try {
        $healthUrl = "$($service.Url)/health"
        $response = Invoke-RestMethod -Uri $healthUrl -Method Get -TimeoutSec 5
        Write-Host "✅ $($service.Name): 健康" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  $($service.Name): 可能未完全启动" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Persimmon Chic 微服务平台启动完成!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 服务访问地址:" -ForegroundColor Cyan

foreach ($service in $services) {
    Write-Host "   • $($service.Name): $($service.Url)" -ForegroundColor White
    if ($service.Name -eq "API Gateway") {
        Write-Host "     - Swagger UI: $($service.Url)" -ForegroundColor Gray
        Write-Host "     - 服务列表: $($service.Url)/services" -ForegroundColor Gray
    } else {
        Write-Host "     - Swagger UI: $($service.Url)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Yellow
Write-Host "   • 按 Ctrl+C 停止所有服务" -ForegroundColor Gray
Write-Host "   • 查看日志请检查各个终端窗口" -ForegroundColor Gray
Write-Host "   • API 文档可通过 Swagger UI 访问" -ForegroundColor Gray

Write-Host ""
Write-Host "🌐 打开浏览器访问 API Gateway..." -ForegroundColor Cyan

# 尝试打开浏览器
try {
    Start-Process "http://localhost:5000"
} catch {
    Write-Host "⚠️  无法自动打开浏览器，请手动访问: http://localhost:5000" -ForegroundColor Yellow
}

# 等待用户输入来停止服务
Write-Host ""
Write-Host "按任意键停止所有服务..." -ForegroundColor Red
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 停止所有作业
Write-Host ""
Write-Host "🛑 停止所有服务..." -ForegroundColor Red

foreach ($jobInfo in $jobs) {
    Stop-Job -Job $jobInfo.Job -ErrorAction SilentlyContinue
    Remove-Job -Job $jobInfo.Job -ErrorAction SilentlyContinue
    Write-Host "✅ 已停止 $($jobInfo.Service.Name)" -ForegroundColor Green
}

Write-Host ""
Write-Host "👋 Persimmon Chic 微服务平台已停止" -ForegroundColor Green
