using AntdUI;
using PersimmonChic.Client.Services;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.Client.Forms;

/// <summary>
/// 主窗体 - 展示 Persimmon Chic 电商平台
/// </summary>
public partial class MainForm : Window
{
    private readonly ApiService _apiService;
    private User? _currentUser;
    
    // UI 组件
    private AntdUI.Menu? _mainMenu;
    private System.Windows.Forms.Panel? _contentPanel;
    private System.Windows.Forms.Panel? _headerPanel;
    private AntdUI.Label? _titleLabel;
    private AntdUI.Button? _loginButton;
    private AntdUI.Avatar? _userAvatar;
    private AntdUI.Label? _userNameLabel;

    public MainForm()
    {
        _apiService = new ApiService("http://localhost:5000");
        InitializeComponent();
        InitializeUI();
    }

    private void InitializeComponent()
    {
        // 窗体基本设置
        Text = "Persimmon Chic - 微服务电商平台";
        Size = new Size(1200, 800);
        StartPosition = FormStartPosition.CenterScreen;
        MinimumSize = new Size(1000, 600);
        
        // 设置窗体样式
        BackColor = Color.FromArgb(240, 242, 245);
        BorderWidth = 1;
        Radius = 8;
        Shadow = 10;
    }

    private void InitializeUI()
    {
        SuspendLayout();

        // 创建头部面板
        CreateHeaderPanel();
        
        // 创建主菜单
        CreateMainMenu();
        
        // 创建内容面板
        CreateContentPanel();
        
        // 显示欢迎页面
        ShowWelcomePage();

        ResumeLayout(false);
    }

    private void CreateHeaderPanel()
    {
        _headerPanel = new System.Windows.Forms.Panel
        {
            Dock = DockStyle.Top,
            Height = 60,
            BackColor = Color.White,
            Padding = new Padding(20, 10, 20, 10)
        };

        // 标题标签
        _titleLabel = new AntdUI.Label
        {
            Text = "🍂 Persimmon Chic",
            Font = new Font("Microsoft YaHei UI", 18F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(20, 15)
        };

        // 登录按钮
        _loginButton = new AntdUI.Button
        {
            Text = "登录",
            Size = new Size(80, 32),
            Location = new Point(_headerPanel.Width - 120, 14),
            Anchor = AnchorStyles.Top | AnchorStyles.Right,
            Type = TTypeMini.Primary
        };
        _loginButton.Click += LoginButton_Click;

        // 用户头像（初始隐藏）
        _userAvatar = new AntdUI.Avatar
        {
            Size = new Size(32, 32),
            Location = new Point(_headerPanel.Width - 160, 14),
            Anchor = AnchorStyles.Top | AnchorStyles.Right,
            Visible = false
        };

        // 用户名标签（初始隐藏）
        _userNameLabel = new AntdUI.Label
        {
            Text = "",
            AutoSize = true,
            Location = new Point(_headerPanel.Width - 120, 20),
            Anchor = AnchorStyles.Top | AnchorStyles.Right,
            Visible = false,
            ForeColor = Color.FromArgb(64, 64, 64)
        };

        _headerPanel.Controls.AddRange(new Control[] { _titleLabel, _loginButton, _userAvatar, _userNameLabel });
        Controls.Add(_headerPanel);
    }

    private void CreateMainMenu()
    {
        _mainMenu = new AntdUI.Menu
        {
            Dock = DockStyle.Left,
            Width = 200,
            BackColor = Color.FromArgb(250, 250, 250),
            Items = new MenuItem[]
            {
                new MenuItem("dashboard", "🏠 首页") { IconSvg = "HomeOutlined" },
                new MenuItem("users", "👥 用户管理") { IconSvg = "UserOutlined" },
                new MenuItem("products", "📦 商品管理") { IconSvg = "ShoppingOutlined" },
                new MenuItem("orders", "📋 订单管理") { IconSvg = "FileTextOutlined" },
                new MenuItem("analytics", "📊 数据分析") { IconSvg = "BarChartOutlined" },
                new MenuItem("settings", "⚙️ 系统设置") { IconSvg = "SettingOutlined" }
            }
        };

        _mainMenu.SelectChanged += MainMenu_SelectChanged;
        Controls.Add(_mainMenu);
    }

    private void CreateContentPanel()
    {
        _contentPanel = new System.Windows.Forms.Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.FromArgb(240, 242, 245),
            Padding = new Padding(20)
        };

        Controls.Add(_contentPanel);
    }

    private void ShowWelcomePage()
    {
        _contentPanel?.Controls.Clear();

        var welcomePanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(40)
        };

        var welcomeLabel = new Label
        {
            Text = "欢迎使用 Persimmon Chic 微服务电商平台",
            Font = new Font("Microsoft YaHei UI", 24F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(40, 40)
        };

        var descriptionLabel = new Label
        {
            Text = "这是一个基于 AntdUI 的完整微服务电商平台演示应用程序\n" +
                   "展示了现代微服务架构设计模式和 AntdUI 组件在实际业务场景中的应用",
            Font = new Font("Microsoft YaHei UI", 12F),
            ForeColor = Color.FromArgb(64, 64, 64),
            AutoSize = true,
            Location = new Point(40, 100),
            MaximumSize = new Size(800, 0)
        };

        var featuresLabel = new Label
        {
            Text = "✨ 主要功能模块：\n\n" +
                   "👥 用户管理 - 展示表单、表格、对话框等组件\n" +
                   "📦 商品管理 - 展示图片预览、分页、搜索等组件\n" +
                   "📋 订单管理 - 展示时间轴、进度条、状态标签等组件\n" +
                   "📊 系统监控 - 展示图表、仪表盘、通知等组件",
            Font = new Font("Microsoft YaHei UI", 11F),
            ForeColor = Color.FromArgb(64, 64, 64),
            AutoSize = true,
            Location = new Point(40, 180),
            MaximumSize = new Size(800, 0)
        };

        var startButton = new Button
        {
            Text = "开始体验",
            Size = new Size(120, 40),
            Location = new Point(40, 350),
            Type = TTypeMini.Primary,
            Font = new Font("Microsoft YaHei UI", 12F)
        };
        startButton.Click += (s, e) => _mainMenu?.Select("users");

        welcomePanel.Controls.AddRange(new Control[] { welcomeLabel, descriptionLabel, featuresLabel, startButton });
        _contentPanel?.Controls.Add(welcomePanel);
    }

    private void MainMenu_SelectChanged(object sender, MenuSelectEventArgs e)
    {
        switch (e.Value?.ToString())
        {
            case "dashboard":
                ShowWelcomePage();
                break;
            case "users":
                ShowUserManagement();
                break;
            case "products":
                ShowProductManagement();
                break;
            case "orders":
                ShowOrderManagement();
                break;
            case "analytics":
                ShowAnalytics();
                break;
            case "settings":
                ShowSettings();
                break;
        }
    }

    private void ShowUserManagement()
    {
        _contentPanel?.Controls.Clear();

        var userPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(20)
        };

        var titleLabel = new Label
        {
            Text = "👥 用户管理",
            Font = new Font("Microsoft YaHei UI", 18F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(20, 20)
        };

        var searchInput = new Input
        {
            Location = new Point(20, 60),
            Size = new Size(300, 32),
            PlaceholderText = "搜索用户..."
        };

        var addButton = new Button
        {
            Text = "添加用户",
            Location = new Point(340, 60),
            Size = new Size(100, 32),
            Type = TTypeMini.Primary
        };

        // 创建用户表格
        var userTable = new Table
        {
            Location = new Point(20, 110),
            Size = new Size(userPanel.Width - 40, userPanel.Height - 150),
            Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
        };

        // 设置表格列
        userTable.Columns = new ColumnCollection
        {
            new Column("id", "ID", ColumnAlign.Center) { Width = 60 },
            new Column("username", "用户名", ColumnAlign.Left) { Width = 120 },
            new Column("email", "邮箱", ColumnAlign.Left) { Width = 200 },
            new Column("role", "角色", ColumnAlign.Center) { Width = 80 },
            new Column("status", "状态", ColumnAlign.Center) { Width = 80 },
            new Column("createdAt", "创建时间", ColumnAlign.Center) { Width = 150 },
            new Column("actions", "操作", ColumnAlign.Center) { Width = 120 }
        };

        // 模拟用户数据
        var userData = new object[][]
        {
            new object[] { 1, "admin", "<EMAIL>", "管理员", "正常", "2024-01-01", "编辑 | 删除" },
            new object[] { 2, "john_doe", "<EMAIL>", "客户", "正常", "2024-01-15", "编辑 | 删除" },
            new object[] { 3, "jane_smith", "<EMAIL>", "客户", "正常", "2024-01-20", "编辑 | 删除" }
        };

        userTable.DataSource = userData;

        userPanel.Controls.AddRange(new Control[] { titleLabel, searchInput, addButton, userTable });
        _contentPanel?.Controls.Add(userPanel);
    }

    private void ShowProductManagement()
    {
        _contentPanel?.Controls.Clear();

        var productPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(20)
        };

        var titleLabel = new Label
        {
            Text = "📦 商品管理",
            Font = new Font("Microsoft YaHei UI", 18F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(20, 20)
        };

        var comingSoonLabel = new Label
        {
            Text = "🚧 商品管理功能正在开发中...\n\n" +
                   "将展示以下 AntdUI 组件：\n" +
                   "• 图片预览组件\n" +
                   "• 分页组件\n" +
                   "• 搜索筛选组件\n" +
                   "• 标签组件\n" +
                   "• 评分组件",
            Font = new Font("Microsoft YaHei UI", 12F),
            ForeColor = Color.FromArgb(128, 128, 128),
            AutoSize = true,
            Location = new Point(20, 80),
            MaximumSize = new Size(600, 0)
        };

        productPanel.Controls.AddRange(new Control[] { titleLabel, comingSoonLabel });
        _contentPanel?.Controls.Add(productPanel);
    }

    private void ShowOrderManagement()
    {
        _contentPanel?.Controls.Clear();

        var orderPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(20)
        };

        var titleLabel = new Label
        {
            Text = "📋 订单管理",
            Font = new Font("Microsoft YaHei UI", 18F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(20, 20)
        };

        var comingSoonLabel = new Label
        {
            Text = "🚧 订单管理功能正在开发中...\n\n" +
                   "将展示以下 AntdUI 组件：\n" +
                   "• 时间轴组件\n" +
                   "• 进度条组件\n" +
                   "• 状态标签组件\n" +
                   "• 表格组件\n" +
                   "• 对话框组件",
            Font = new Font("Microsoft YaHei UI", 12F),
            ForeColor = Color.FromArgb(128, 128, 128),
            AutoSize = true,
            Location = new Point(20, 80),
            MaximumSize = new Size(600, 0)
        };

        orderPanel.Controls.AddRange(new Control[] { titleLabel, comingSoonLabel });
        _contentPanel?.Controls.Add(orderPanel);
    }

    private void ShowAnalytics()
    {
        _contentPanel?.Controls.Clear();

        var analyticsPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(20)
        };

        var titleLabel = new Label
        {
            Text = "📊 数据分析",
            Font = new Font("Microsoft YaHei UI", 18F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(20, 20)
        };

        var comingSoonLabel = new Label
        {
            Text = "🚧 数据分析功能正在开发中...\n\n" +
                   "将展示以下 AntdUI 组件：\n" +
                   "• 图表组件\n" +
                   "• 仪表盘组件\n" +
                   "• 统计卡片组件\n" +
                   "• 通知组件\n" +
                   "• 警告提示组件",
            Font = new Font("Microsoft YaHei UI", 12F),
            ForeColor = Color.FromArgb(128, 128, 128),
            AutoSize = true,
            Location = new Point(20, 80),
            MaximumSize = new Size(600, 0)
        };

        analyticsPanel.Controls.AddRange(new Control[] { titleLabel, comingSoonLabel });
        _contentPanel?.Controls.Add(analyticsPanel);
    }

    private void ShowSettings()
    {
        _contentPanel?.Controls.Clear();

        var settingsPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(20)
        };

        var titleLabel = new Label
        {
            Text = "⚙️ 系统设置",
            Font = new Font("Microsoft YaHei UI", 18F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(20, 20)
        };

        var themeLabel = new Label
        {
            Text = "主题设置:",
            Font = new Font("Microsoft YaHei UI", 12F),
            AutoSize = true,
            Location = new Point(20, 80)
        };

        var themeSwitch = new Switch
        {
            Location = new Point(100, 78),
            Size = new Size(50, 24),
            Checked = false
        };
        themeSwitch.CheckedChanged += (s, e) =>
        {
            // 切换主题的逻辑
            if (themeSwitch.Checked)
            {
                // 深色主题
                BackColor = Color.FromArgb(20, 20, 20);
            }
            else
            {
                // 浅色主题
                BackColor = Color.FromArgb(240, 242, 245);
            }
        };

        var themeHintLabel = new Label
        {
            Text = "切换到深色主题",
            Font = new Font("Microsoft YaHei UI", 10F),
            ForeColor = Color.FromArgb(128, 128, 128),
            AutoSize = true,
            Location = new Point(160, 82)
        };

        settingsPanel.Controls.AddRange(new Control[] { titleLabel, themeLabel, themeSwitch, themeHintLabel });
        _contentPanel?.Controls.Add(settingsPanel);
    }

    private void LoginButton_Click(object? sender, EventArgs e)
    {
        // 显示登录对话框
        var loginForm = new LoginForm(_apiService);
        if (loginForm.ShowDialog() == DialogResult.OK)
        {
            _currentUser = loginForm.CurrentUser;
            UpdateUserUI();
        }
    }

    private void UpdateUserUI()
    {
        if (_currentUser != null)
        {
            _loginButton!.Visible = false;
            _userAvatar!.Visible = true;
            _userNameLabel!.Visible = true;
            _userNameLabel.Text = _currentUser.Username;
            
            // 可以设置用户头像
            if (!string.IsNullOrEmpty(_currentUser.Avatar))
            {
                // _userAvatar.Image = LoadImageFromUrl(_currentUser.Avatar);
            }
        }
        else
        {
            _loginButton!.Visible = true;
            _userAvatar!.Visible = false;
            _userNameLabel!.Visible = false;
        }
    }
}
