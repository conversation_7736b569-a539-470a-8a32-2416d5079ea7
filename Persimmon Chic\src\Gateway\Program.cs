using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using PersimmonChic.Gateway.Middleware;
using PersimmonChic.Gateway.Services;
using PersimmonChic.Shared.Common;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo 
    { 
        Title = "Persimmon Chic API Gateway", 
        Version = "v1",
        Description = "微服务电商平台API网关"
    });
    
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? "PersimmonChic_Super_Secret_Key_2024_Demo_Application";
var key = Encoding.ASCII.GetBytes(secretKey);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register services
builder.Services.AddSingleton<ServiceRegistry>();
builder.Services.AddSingleton<LoadBalancer>();
builder.Services.AddSingleton<HttpClientFactory>();
builder.Services.AddScoped<IGatewayService, GatewayService>();

// Configure HTTP clients
builder.Services.AddHttpClient();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Persimmon Chic API Gateway v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

// Add custom middleware
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseMiddleware<ErrorHandlingMiddleware>();
app.UseMiddleware<RateLimitingMiddleware>();

app.MapControllers();

// Health check endpoint
app.MapGet("/health", () => new { Status = "Healthy", Timestamp = DateTime.UtcNow });

// Service discovery endpoint
app.MapGet("/services", (ServiceRegistry registry) => 
{
    var services = registry.GetServiceNames().Select(name => new
    {
        Name = name,
        Instances = registry.GetAllServices(name).Select(instance => new
        {
            instance.Id,
            instance.Address,
            instance.Port,
            instance.Version,
            instance.IsHealthy,
            instance.RegisteredAt,
            instance.LastHeartbeat
        })
    });
    
    return Results.Ok(services);
});

// Register this gateway instance
var serviceRegistry = app.Services.GetRequiredService<ServiceRegistry>();
var gatewayInstance = new ServiceInstance
{
    Address = "localhost",
    Port = 5000,
    Version = "1.0.0"
};
serviceRegistry.RegisterService("gateway", gatewayInstance);

Console.WriteLine("🚀 Persimmon Chic API Gateway is starting...");
Console.WriteLine($"📍 Gateway URL: http://localhost:5000");
Console.WriteLine($"📖 Swagger UI: http://localhost:5000");
Console.WriteLine($"🔍 Health Check: http://localhost:5000/health");
Console.WriteLine($"🌐 Services: http://localhost:5000/services");

app.Run();
