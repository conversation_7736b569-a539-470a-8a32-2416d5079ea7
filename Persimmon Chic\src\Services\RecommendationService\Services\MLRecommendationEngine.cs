using Microsoft.Extensions.Logging;
using Microsoft.ML;
using Microsoft.ML.Data;
using Microsoft.ML.Trainers;
using PersimmonChic.RecommendationService.Models;
using PersimmonChic.Shared.Models;
using System.Text.Json;

namespace PersimmonChic.RecommendationService.Services;

/// <summary>
/// 机器学习推荐引擎实现
/// </summary>
public class MLRecommendationEngine : IMLRecommendationEngine
{
    private readonly ILogger<MLRecommendationEngine> _logger;
    private readonly MLContext _mlContext;
    private ITransformer? _model;
    private readonly string _modelPath;
    private readonly object _modelLock = new();
    private DataViewSchema? _modelSchema;

    // 性能统计
    private long _predictionCount = 0;
    private double _totalPredictionTime = 0;
    private long _trainingCount = 0;

    public MLRecommendationEngine(ILogger<MLRecommendationEngine> logger)
    {
        _logger = logger;
        _mlContext = new MLContext(seed: 42);
        _modelPath = Path.Combine(AppContext.BaseDirectory, "Models", "recommendation_model.zip");
        
        // 确保模型目录存在
        Directory.CreateDirectory(Path.GetDirectoryName(_modelPath)!);
        
        // 尝试加载现有模型
        LoadExistingModel();
    }

    public async Task<ApiResponse<bool>> TrainModelAsync(List<RecommendationTrainingData> trainingData)
    {
        try
        {
            _logger.LogInformation("开始训练推荐模型，训练数据量: {Count}", trainingData.Count);

            if (trainingData.Count < 10)
            {
                return ApiResponse<bool>.ErrorResult("训练数据量不足，至少需要10条数据");
            }

            // 转换训练数据
            var dataView = _mlContext.Data.LoadFromEnumerable(trainingData);

            // 定义训练管道
            var options = new MatrixFactorizationTrainer.Options
            {
                MatrixColumnIndexColumnName = nameof(RecommendationTrainingData.UserId),
                MatrixRowIndexColumnName = nameof(RecommendationTrainingData.ProductId),
                LabelColumnName = nameof(RecommendationTrainingData.Rating),
                NumberOfIterations = 20,
                ApproximationRank = 100,
                Quiet = true
            };

            var pipeline = _mlContext.Recommendation().Trainers.MatrixFactorization(options);

            // 训练模型
            _logger.LogInformation("开始矩阵分解训练...");
            var trainedModel = pipeline.Fit(dataView);

            // 保存模型
            lock (_modelLock)
            {
                _mlContext.Model.Save(trainedModel, dataView.Schema, _modelPath);
                _model = trainedModel;
                _modelSchema = dataView.Schema;
                _trainingCount++;
            }

            _logger.LogInformation("推荐模型训练完成并已保存，训练次数: {Count}", _trainingCount);
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练推荐模型时发生错误");
            return ApiResponse<bool>.ErrorResult($"模型训练失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<float>> PredictRatingAsync(int userId, int productId)
    {
        try
        {
            if (_model == null)
            {
                _logger.LogWarning("推荐模型未加载，返回默认评分");
                return ApiResponse<float>.SuccessResult(await CalculateDefaultRatingAsync(userId, productId));
            }

            var startTime = DateTime.UtcNow;

            // 创建预测数据
            var input = new RecommendationTrainingData
            {
                UserId = (uint)userId,
                ProductId = (uint)productId,
                Rating = 0f // 预测时不需要实际评分
            };

            // 创建预测引擎
            var predictionEngine = _mlContext.Model.CreatePredictionEngine<RecommendationTrainingData, RecommendationPrediction>(_model);
            
            // 进行预测
            var prediction = predictionEngine.Predict(input);
            
            // 确保预测结果在合理范围内 (0-5)
            var rating = Math.Max(0f, Math.Min(5f, prediction.Score));

            // 更新性能统计
            var elapsedMs = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Interlocked.Increment(ref _predictionCount);
            _totalPredictionTime += elapsedMs;

            _logger.LogDebug("ML模型预测评分: {Rating} (用户: {UserId}, 产品: {ProductId}, 耗时: {ElapsedMs}ms)", 
                rating, userId, productId, elapsedMs);

            return ApiResponse<float>.SuccessResult(rating);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测评分时发生错误");
            
            // 发生错误时使用默认评分
            var defaultRating = await CalculateDefaultRatingAsync(userId, productId);
            return ApiResponse<float>.SuccessResult(defaultRating);
        }
    }

    public async Task<ApiResponse<List<RecommendationItem>>> GetUserRecommendationsAsync(int userId, int count = 10)
    {
        try
        {
            _logger.LogInformation("为用户 {UserId} 生成 {Count} 个推荐", userId, count);

            var recommendations = new List<RecommendationItem>();

            // 模拟产品ID范围 (实际应该从产品服务获取)
            var productIds = Enumerable.Range(1, 100).ToList();
            var userRatings = new List<(int ProductId, float Rating)>();

            // 为每个产品预测评分
            foreach (var productId in productIds)
            {
                var ratingResponse = await PredictRatingAsync(userId, productId);
                if (ratingResponse.Success)
                {
                    userRatings.Add((productId, ratingResponse.Data));
                }
            }

            // 按评分排序并取前N个
            var topRatings = userRatings
                .OrderByDescending(x => x.Rating)
                .Take(count)
                .ToList();

            // 构建推荐项目
            for (int i = 0; i < topRatings.Count; i++)
            {
                var (productId, rating) = topRatings[i];
                
                recommendations.Add(new RecommendationItem
                {
                    ProductId = productId,
                    ProductName = $"Product {productId}",
                    Score = rating,
                    Confidence = CalculateConfidence(rating),
                    Reason = "基于机器学习模型的个性化推荐",
                    Rank = i + 1,
                    Metadata = new Dictionary<string, object>
                    {
                        ["algorithm"] = "MatrixFactorization",
                        ["model_version"] = _trainingCount,
                        ["prediction_time"] = DateTime.UtcNow
                    }
                });
            }

            _logger.LogInformation("为用户 {UserId} 生成了 {Count} 个推荐", userId, recommendations.Count);
            return ApiResponse<List<RecommendationItem>>.SuccessResult(recommendations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成用户推荐时发生错误");
            return ApiResponse<List<RecommendationItem>>.ErrorResult($"生成推荐失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateModelAsync(List<RecommendationTrainingData> newData)
    {
        try
        {
            _logger.LogInformation("开始增量更新推荐模型，新数据量: {Count}", newData.Count);

            // 对于矩阵分解，通常需要重新训练整个模型
            // 实际生产环境中可能需要更复杂的增量学习策略
            return await TrainModelAsync(newData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新推荐模型时发生错误");
            return ApiResponse<bool>.ErrorResult($"模型更新失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<Dictionary<string, double>>> GetModelMetricsAsync()
    {
        try
        {
            var metrics = new Dictionary<string, double>();

            if (_model == null)
            {
                metrics["model_loaded"] = 0;
                metrics["training_count"] = 0;
                return ApiResponse<Dictionary<string, double>>.SuccessResult(metrics);
            }

            // 获取模型文件信息
            if (File.Exists(_modelPath))
            {
                var fileInfo = new FileInfo(_modelPath);
                metrics["model_loaded"] = 1;
                metrics["model_size_bytes"] = fileInfo.Length;
                metrics["last_training_time"] = new DateTimeOffset(fileInfo.LastWriteTime).ToUnixTimeSeconds();
            }

            // 添加运行时指标
            metrics["training_count"] = _trainingCount;
            metrics["prediction_count"] = _predictionCount;
            metrics["average_prediction_time_ms"] = _predictionCount > 0 ? _totalPredictionTime / _predictionCount : 0;

            // 模拟模型质量指标
            metrics["estimated_rmse"] = 0.85 + (new Random().NextDouble() * 0.3); // 0.85-1.15
            metrics["estimated_mae"] = 0.65 + (new Random().NextDouble() * 0.2);  // 0.65-0.85

            return ApiResponse<Dictionary<string, double>>.SuccessResult(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模型性能指标时发生错误");
            return ApiResponse<Dictionary<string, double>>.ErrorResult($"获取模型指标失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private void LoadExistingModel()
    {
        try
        {
            if (File.Exists(_modelPath))
            {
                lock (_modelLock)
                {
                    _model = _mlContext.Model.Load(_modelPath, out _modelSchema);
                }
                _logger.LogInformation("已加载现有推荐模型");
            }
            else
            {
                _logger.LogInformation("未找到现有模型文件，将在首次训练时创建");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载现有模型时发生错误");
        }
    }

    private async Task<float> CalculateDefaultRatingAsync(int userId, int productId)
    {
        // 当ML模型不可用时的默认评分逻辑
        await Task.Delay(1); // 模拟异步操作

        // 基于用户ID和产品ID的简单哈希算法
        var hash = (userId * 31 + productId) % 100;
        var baseRating = 2.5f + (hash / 100f) * 2.5f; // 2.5-5.0 范围

        // 添加一些随机性
        var random = new Random(userId + productId);
        var noise = (float)(random.NextDouble() - 0.5) * 0.5f; // -0.25 到 +0.25

        return Math.Max(1f, Math.Min(5f, baseRating + noise));
    }

    private float CalculateConfidence(float rating)
    {
        // 基于评分计算置信度
        // 评分越高或越低，置信度越高
        var normalizedRating = Math.Abs(rating - 2.5f) / 2.5f; // 0-1 范围
        return Math.Min(1f, 0.6f + normalizedRating * 0.4f); // 0.6-1.0 范围
    }

    /// <summary>
    /// 生成模拟训练数据用于演示
    /// </summary>
    /// <param name="userCount">用户数量</param>
    /// <param name="productCount">产品数量</param>
    /// <param name="ratingCount">评分数量</param>
    /// <returns>训练数据</returns>
    public List<RecommendationTrainingData> GenerateMockTrainingData(int userCount = 100, int productCount = 50, int ratingCount = 1000)
    {
        var random = new Random(42);
        var trainingData = new List<RecommendationTrainingData>();

        for (int i = 0; i < ratingCount; i++)
        {
            var userId = random.Next(1, userCount + 1);
            var productId = random.Next(1, productCount + 1);
            
            // 生成符合正态分布的评分 (均值3.5，标准差1.0)
            var rating = Math.Max(1f, Math.Min(5f, (float)(random.NextGaussian() * 1.0 + 3.5)));

            trainingData.Add(new RecommendationTrainingData
            {
                UserId = (uint)userId,
                ProductId = (uint)productId,
                Rating = rating
            });
        }

        return trainingData;
    }
}

/// <summary>
/// Random扩展方法，用于生成正态分布随机数
/// </summary>
public static class RandomExtensions
{
    private static double _u1 = 0;
    private static double _u2 = 0;
    private static bool _hasSpare = false;
    private static readonly object _lock = new object();

    public static double NextGaussian(this Random random)
    {
        lock (_lock)
        {
            // Box-Muller变换生成正态分布随机数
            if (_hasSpare)
            {
                _hasSpare = false;
                return Math.Sqrt(-2.0 * Math.Log(_u1)) * Math.Sin(2.0 * Math.PI * _u2);
            }

            _hasSpare = true;
            _u1 = random.NextDouble();
            _u2 = random.NextDouble();
            return Math.Sqrt(-2.0 * Math.Log(_u1)) * Math.Cos(2.0 * Math.PI * _u2);
        }
    }
}
