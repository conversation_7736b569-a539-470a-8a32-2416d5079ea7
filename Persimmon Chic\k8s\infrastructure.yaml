# Redis部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
    app.kubernetes.io/part-of: persimmon-chic
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: redis
  template:
    metadata:
      labels:
        app.kubernetes.io/name: redis
        app.kubernetes.io/component: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - --appendonly
        - "yes"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc

---
# Redis服务
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
spec:
  selector:
    app.kubernetes.io/name: redis
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  type: ClusterIP

---
# Redis持久化卷声明
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
# SQL Server部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sqlserver
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: sqlserver
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: persimmon-chic
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: sqlserver
  template:
    metadata:
      labels:
        app.kubernetes.io/name: sqlserver
        app.kubernetes.io/component: database
    spec:
      containers:
      - name: sqlserver
        image: mcr.microsoft.com/mssql/server:2022-latest
        ports:
        - containerPort: 1433
          name: sqlserver
        env:
        - name: ACCEPT_EULA
          value: "Y"
        - name: SA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: persimmon-chic-secrets
              key: SQL_PASSWORD
        - name: MSSQL_PID
          value: "Express"
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          exec:
            command:
            - /opt/mssql-tools/bin/sqlcmd
            - -S
            - localhost
            - -U
            - sa
            - -P
            - $(SA_PASSWORD)
            - -Q
            - SELECT 1
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - /opt/mssql-tools/bin/sqlcmd
            - -S
            - localhost
            - -U
            - sa
            - -P
            - $(SA_PASSWORD)
            - -Q
            - SELECT 1
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: sqlserver-data
          mountPath: /var/opt/mssql
      volumes:
      - name: sqlserver-data
        persistentVolumeClaim:
          claimName: sqlserver-pvc

---
# SQL Server服务
apiVersion: v1
kind: Service
metadata:
  name: sqlserver-service
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: sqlserver
    app.kubernetes.io/component: database
spec:
  selector:
    app.kubernetes.io/name: sqlserver
  ports:
  - port: 1433
    targetPort: 1433
    name: sqlserver
  type: ClusterIP

---
# SQL Server持久化卷声明
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: sqlserver-pvc
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: sqlserver
    app.kubernetes.io/component: database
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: standard
