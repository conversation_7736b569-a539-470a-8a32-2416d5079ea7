using PersimmonChic.Shared.Models;

namespace PersimmonChic.Shared.Contracts;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// 生成JWT令牌
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>JWT令牌</returns>
    Task<string> GenerateTokenAsync(User user);

    /// <summary>
    /// 验证JWT令牌
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> ValidateTokenAsync(string token);

    /// <summary>
    /// 刷新JWT令牌
    /// </summary>
    /// <param name="token">当前令牌</param>
    /// <returns>新令牌</returns>
    Task<string?> RefreshTokenAsync(string token);

    /// <summary>
    /// 撤销令牌
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>撤销结果</returns>
    Task<bool> RevokeTokenAsync(string token);

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">明文密码</param>
    /// <param name="hash">密码哈希</param>
    /// <returns>验证结果</returns>
    bool VerifyPassword(string password, string hash);

    /// <summary>
    /// 生成密码哈希
    /// </summary>
    /// <param name="password">明文密码</param>
    /// <returns>密码哈希</returns>
    string HashPassword(string password);

    /// <summary>
    /// 生成随机密码
    /// </summary>
    /// <param name="length">密码长度</param>
    /// <returns>随机密码</returns>
    string GenerateRandomPassword(int length = 12);

    /// <summary>
    /// 验证权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permission">权限名称</param>
    /// <returns>验证结果</returns>
    Task<bool> HasPermissionAsync(int userId, string permission);

    /// <summary>
    /// 获取用户权限列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>权限列表</returns>
    Task<List<string>> GetUserPermissionsAsync(int userId);
}

/// <summary>
/// 消息服务接口
/// </summary>
public interface IMessageService
{
    /// <summary>
    /// 发送邮件
    /// </summary>
    /// <param name="to">收件人</param>
    /// <param name="subject">主题</param>
    /// <param name="content">内容</param>
    /// <param name="isHtml">是否HTML格式</param>
    /// <returns>发送结果</returns>
    Task<ApiResponse<bool>> SendEmailAsync(string to, string subject, string content, bool isHtml = true);

    /// <summary>
    /// 批量发送邮件
    /// </summary>
    /// <param name="recipients">收件人列表</param>
    /// <param name="subject">主题</param>
    /// <param name="content">内容</param>
    /// <param name="isHtml">是否HTML格式</param>
    /// <returns>发送结果</returns>
    Task<ApiResponse<bool>> SendBulkEmailAsync(List<string> recipients, string subject, string content, bool isHtml = true);

    /// <summary>
    /// 发送短信
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="message">短信内容</param>
    /// <returns>发送结果</returns>
    Task<ApiResponse<bool>> SendSmsAsync(string phoneNumber, string message);

    /// <summary>
    /// 发送推送通知
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="title">标题</param>
    /// <param name="content">内容</param>
    /// <param name="data">附加数据</param>
    /// <returns>发送结果</returns>
    Task<ApiResponse<bool>> SendPushNotificationAsync(int userId, string title, string content, Dictionary<string, object>? data = null);

    /// <summary>
    /// 发送系统消息
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <returns>发送结果</returns>
    Task<ApiResponse<bool>> SendMessageAsync(Message message);

    /// <summary>
    /// 获取消息状态
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>消息状态</returns>
    Task<ApiResponse<Message>> GetMessageStatusAsync(string messageId);

    /// <summary>
    /// 获取消息历史
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>消息列表</returns>
    Task<ApiResponse<PagedResponse<Message>>> GetMessageHistoryAsync(PagedRequest request);

    /// <summary>
    /// 重试失败的消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>重试结果</returns>
    Task<ApiResponse<bool>> RetryMessageAsync(string messageId);

    /// <summary>
    /// 取消消息发送
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>取消结果</returns>
    Task<ApiResponse<bool>> CancelMessageAsync(string messageId);
}

/// <summary>
/// 监控服务接口
/// </summary>
public interface IMonitoringService
{
    /// <summary>
    /// 获取服务健康状态
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <returns>健康状态</returns>
    Task<ApiResponse<HealthStatus>> GetHealthStatusAsync(string serviceName);

    /// <summary>
    /// 获取所有服务健康状态
    /// </summary>
    /// <returns>健康状态列表</returns>
    Task<ApiResponse<List<HealthStatus>>> GetAllHealthStatusAsync();

    /// <summary>
    /// 记录系统指标
    /// </summary>
    /// <param name="metrics">系统指标</param>
    /// <returns>记录结果</returns>
    Task<ApiResponse<bool>> RecordMetricsAsync(SystemMetrics metrics);

    /// <summary>
    /// 获取系统指标
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>系统指标列表</returns>
    Task<ApiResponse<List<SystemMetrics>>> GetMetricsAsync(string serviceName, DateTime startTime, DateTime endTime);

    /// <summary>
    /// 记录审计日志
    /// </summary>
    /// <param name="auditLog">审计日志</param>
    /// <returns>记录结果</returns>
    Task<ApiResponse<bool>> LogAuditAsync(AuditLog auditLog);

    /// <summary>
    /// 获取审计日志
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>审计日志列表</returns>
    Task<ApiResponse<PagedResponse<AuditLog>>> GetAuditLogsAsync(PagedRequest request);

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="exception">异常信息</param>
    /// <param name="context">上下文信息</param>
    /// <returns>记录结果</returns>
    Task<ApiResponse<bool>> LogErrorAsync(string serviceName, Exception exception, Dictionary<string, object>? context = null);

    /// <summary>
    /// 获取错误日志
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="request">分页请求</param>
    /// <returns>错误日志列表</returns>
    Task<ApiResponse<PagedResponse<Dictionary<string, object>>>> GetErrorLogsAsync(string serviceName, PagedRequest request);

    /// <summary>
    /// 获取系统概览
    /// </summary>
    /// <returns>系统概览信息</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetSystemOverviewAsync();

    /// <summary>
    /// 获取性能报告
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>性能报告</returns>
    Task<ApiResponse<Dictionary<string, object>>> GetPerformanceReportAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 设置告警规则
    /// </summary>
    /// <param name="rule">告警规则</param>
    /// <returns>设置结果</returns>
    Task<ApiResponse<bool>> SetAlertRuleAsync(AlertRule rule);

    /// <summary>
    /// 获取告警列表
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>告警列表</returns>
    Task<ApiResponse<PagedResponse<Alert>>> GetAlertsAsync(PagedRequest request);
}

/// <summary>
/// 告警规则模型
/// </summary>
public class AlertRule
{
    public string Name { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 告警模型
/// </summary>
public class Alert
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string RuleName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public AlertLevel Level { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsResolved { get; set; }
    public DateTime? ResolvedAt { get; set; }
}

/// <summary>
/// 告警级别枚举
/// </summary>
public enum AlertLevel
{
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3
}
