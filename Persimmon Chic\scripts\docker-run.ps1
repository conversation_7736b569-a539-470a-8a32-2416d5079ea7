# Persimmon Chic Docker 运行脚本
param(
    [string]$Environment = "production",
    [string]$Action = "up",
    [switch]$Build,
    [switch]$Detach,
    [switch]$Logs,
    [string]$Service = ""
)

Write-Host "=== Persimmon Chic Docker 运行脚本 ===" -ForegroundColor Green
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host "操作: $Action" -ForegroundColor Yellow
Write-Host ""

# 选择Docker Compose文件
$composeFile = if ($Environment -eq "development") { "docker-compose.dev.yml" } else { "docker-compose.yml" }

if (-not (Test-Path $composeFile)) {
    Write-Host "错误: Docker Compose文件不存在: $composeFile" -ForegroundColor Red
    exit 1
}

Write-Host "使用配置文件: $composeFile" -ForegroundColor Cyan
Write-Host ""

# 构建Docker Compose命令
$composeArgs = @("compose", "-f", $composeFile)

try {
    switch ($Action.ToLower()) {
        "up" {
            Write-Host "启动服务..." -ForegroundColor Green
            
            $upArgs = @("up")
            
            if ($Build) {
                $upArgs += "--build"
                Write-Host "将重新构建镜像" -ForegroundColor Yellow
            }
            
            if ($Detach) {
                $upArgs += "-d"
                Write-Host "后台运行模式" -ForegroundColor Yellow
            }
            
            if ($Service) {
                $upArgs += $Service
                Write-Host "仅启动服务: $Service" -ForegroundColor Yellow
            }
            
            $composeArgs += $upArgs
            
            Write-Host "执行命令: docker $($composeArgs -join ' ')" -ForegroundColor Gray
            Write-Host ""
            
            & docker @composeArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host ""
                Write-Host "✓ 服务启动成功！" -ForegroundColor Green
                
                if ($Environment -eq "development") {
                    Write-Host ""
                    Write-Host "=== 开发环境访问地址 ===" -ForegroundColor Cyan
                    Write-Host "API Gateway: http://localhost:5000" -ForegroundColor White
                    Write-Host "用户服务: http://localhost:5001" -ForegroundColor White
                    Write-Host "风险控制服务: http://localhost:5004" -ForegroundColor White
                    Write-Host "动态定价服务: http://localhost:5005" -ForegroundColor White
                    Write-Host "推荐服务: http://localhost:5006" -ForegroundColor White
                    Write-Host "搜索服务: http://localhost:5007" -ForegroundColor White
                    Write-Host "AI客服机器人: http://localhost:5008" -ForegroundColor White
                    Write-Host ""
                    Write-Host "=== 管理界面 ===" -ForegroundColor Cyan
                    Write-Host "Portainer: http://localhost:9000" -ForegroundColor White
                    Write-Host "Redis Commander: http://localhost:8081" -ForegroundColor White
                }
            } else {
                Write-Host "✗ 服务启动失败" -ForegroundColor Red
                exit 1
            }
        }
        
        "down" {
            Write-Host "停止服务..." -ForegroundColor Yellow
            
            $downArgs = @("down")
            
            if ($Service) {
                Write-Host "停止特定服务: $Service" -ForegroundColor Yellow
                $composeArgs += @("stop", $Service)
            } else {
                $composeArgs += $downArgs
            }
            
            & docker @composeArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ 服务停止成功" -ForegroundColor Green
            } else {
                Write-Host "✗ 服务停止失败" -ForegroundColor Red
                exit 1
            }
        }
        
        "restart" {
            Write-Host "重启服务..." -ForegroundColor Yellow
            
            if ($Service) {
                $composeArgs += @("restart", $Service)
                Write-Host "重启服务: $Service" -ForegroundColor Yellow
            } else {
                $composeArgs += "restart"
            }
            
            & docker @composeArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ 服务重启成功" -ForegroundColor Green
            } else {
                Write-Host "✗ 服务重启失败" -ForegroundColor Red
                exit 1
            }
        }
        
        "logs" {
            Write-Host "查看服务日志..." -ForegroundColor Cyan
            
            $logsArgs = @("logs", "-f")
            
            if ($Service) {
                $logsArgs += $Service
                Write-Host "查看服务日志: $Service" -ForegroundColor Yellow
            }
            
            $composeArgs += $logsArgs
            
            & docker @composeArgs
        }
        
        "ps" {
            Write-Host "查看服务状态..." -ForegroundColor Cyan
            
            $composeArgs += "ps"
            
            & docker @composeArgs
        }
        
        "build" {
            Write-Host "构建服务镜像..." -ForegroundColor Cyan
            
            $buildArgs = @("build")
            
            if ($Service) {
                $buildArgs += $Service
                Write-Host "构建服务: $Service" -ForegroundColor Yellow
            }
            
            $composeArgs += $buildArgs
            
            & docker @composeArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ 镜像构建成功" -ForegroundColor Green
            } else {
                Write-Host "✗ 镜像构建失败" -ForegroundColor Red
                exit 1
            }
        }
        
        "pull" {
            Write-Host "拉取最新镜像..." -ForegroundColor Cyan
            
            $composeArgs += "pull"
            
            & docker @composeArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ 镜像拉取成功" -ForegroundColor Green
            } else {
                Write-Host "✗ 镜像拉取失败" -ForegroundColor Red
                exit 1
            }
        }
        
        "clean" {
            Write-Host "清理Docker资源..." -ForegroundColor Yellow
            
            # 停止所有容器
            & docker @composeArgs down
            
            # 清理未使用的镜像
            Write-Host "清理未使用的镜像..." -ForegroundColor Cyan
            & docker image prune -f
            
            # 清理未使用的卷
            Write-Host "清理未使用的卷..." -ForegroundColor Cyan
            & docker volume prune -f
            
            # 清理未使用的网络
            Write-Host "清理未使用的网络..." -ForegroundColor Cyan
            & docker network prune -f
            
            Write-Host "✓ 清理完成" -ForegroundColor Green
        }
        
        default {
            Write-Host "错误: 未知操作 '$Action'" -ForegroundColor Red
            Write-Host ""
            Write-Host "可用操作:" -ForegroundColor Yellow
            Write-Host "  up      - 启动服务" -ForegroundColor White
            Write-Host "  down    - 停止服务" -ForegroundColor White
            Write-Host "  restart - 重启服务" -ForegroundColor White
            Write-Host "  logs    - 查看日志" -ForegroundColor White
            Write-Host "  ps      - 查看状态" -ForegroundColor White
            Write-Host "  build   - 构建镜像" -ForegroundColor White
            Write-Host "  pull    - 拉取镜像" -ForegroundColor White
            Write-Host "  clean   - 清理资源" -ForegroundColor White
            exit 1
        }
    }
}
catch {
    Write-Host "执行过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 使用提示 ===" -ForegroundColor Cyan
Write-Host "1. 启动开发环境: .\docker-run.ps1 -Environment development -Action up -Detach"
Write-Host "2. 查看服务状态: .\docker-run.ps1 -Action ps"
Write-Host "3. 查看服务日志: .\docker-run.ps1 -Action logs -Service gateway"
Write-Host "4. 重启特定服务: .\docker-run.ps1 -Action restart -Service user-service"
Write-Host "5. 停止所有服务: .\docker-run.ps1 -Action down"
Write-Host ""
