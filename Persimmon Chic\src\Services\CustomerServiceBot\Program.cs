using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.CustomerServiceBot.Models;
using PersimmonChic.CustomerServiceBot.Services;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllers();

// 配置Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Persimmon Chic Customer Service Bot",
        Version = "v1",
        Description = "AI客服机器人服务API - 提供智能对话、意图识别和知识库问答功能",
        Contact = new OpenApiContact
        {
            Name = "Persimmon Chic Team",
            Email = "<EMAIL>"
        }
    });

    // 添加JWT认证配置
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// 配置JWT认证
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5000", "http://localhost:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// 配置SignalR
builder.Services.AddSignalR();

// 注册数据访问层
builder.Services.AddScoped<IRepository<ChatSession>, InMemoryRepository<ChatSession>>();
builder.Services.AddScoped<IRepository<ChatMessage>, InMemoryRepository<ChatMessage>>();
builder.Services.AddScoped<IRepository<ConversationContext>, InMemoryRepository<ConversationContext>>();
builder.Services.AddScoped<IRepository<KnowledgeBaseEntry>, InMemoryRepository<KnowledgeBaseEntry>>();
builder.Services.AddScoped<IRepository<CustomerServiceAgent>, InMemoryRepository<CustomerServiceAgent>>();

// 注册AI服务
builder.Services.AddScoped<IChatBotService, ChatBotService>();
builder.Services.AddScoped<IIntentRecognitionService, IntentRecognitionService>();
builder.Services.AddScoped<IKnowledgeBaseService, KnowledgeBaseService>();

// 注册HTTP客户端工厂
builder.Services.AddHttpClient();

// 配置日志
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    logging.AddDebug();
    
    if (builder.Environment.IsDevelopment())
    {
        logging.SetMinimumLevel(LogLevel.Debug);
    }
    else
    {
        logging.SetMinimumLevel(LogLevel.Information);
    }
});

// 配置健康检查
builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Customer Service Bot V1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}

app.UseHttpsRedirection();

app.UseCors();

app.UseAuthentication();
app.UseAuthorization();

// 添加请求日志中间件
app.Use(async (context, next) =>
{
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    var requestId = Guid.NewGuid().ToString("N")[..8];
    
    using (logger.BeginScope(new Dictionary<string, object> { ["RequestId"] = requestId }))
    {
        logger.LogInformation("开始处理请求: {Method} {Path}", 
            context.Request.Method, context.Request.Path);
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            await next();
        }
        finally
        {
            stopwatch.Stop();
            logger.LogInformation("请求处理完成: {Method} {Path} - {StatusCode} - {ElapsedMs}ms",
                context.Request.Method, context.Request.Path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
        }
    }
});

app.MapControllers();

// 配置健康检查端点
app.MapHealthChecks("/health");

// 添加服务信息端点
app.MapGet("/info", () => new
{
    Service = "PersimmonChic.CustomerServiceBot",
    Version = "1.0.0",
    Environment = app.Environment.EnvironmentName,
    StartTime = DateTime.UtcNow,
    Features = new[]
    {
        "AI智能对话引擎",
        "自然语言理解",
        "意图识别与分类",
        "实体提取与解析",
        "知识库智能问答",
        "多轮对话管理",
        "上下文理解",
        "情感分析",
        "人工客服转接",
        "会话状态管理",
        "快速回复模板",
        "实时消息推送"
    }
});

// 初始化默认数据
await InitializeDefaultDataAsync(app.Services);

app.Run();

/// <summary>
/// 初始化默认数据
/// </summary>
static async Task InitializeDefaultDataAsync(IServiceProvider services)
{
    using var scope = services.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    
    try
    {
        logger.LogInformation("开始初始化AI客服机器人默认数据...");
        
        // 初始化默认客服代理
        var agentRepository = scope.ServiceProvider.GetRequiredService<IRepository<CustomerServiceAgent>>();
        
        var defaultAgents = new List<CustomerServiceAgent>
        {
            new CustomerServiceAgent
            {
                Id = 1,
                Name = "AI助手小柿",
                Email = "<EMAIL>",
                Status = AgentStatus.Available,
                Skills = new List<string> { "产品咨询", "订单查询", "售后服务", "技术支持" },
                MaxSessions = 100,
                Metadata = new Dictionary<string, object>
                {
                    ["type"] = "ai_bot",
                    ["language"] = "zh-CN",
                    ["version"] = "1.0.0"
                }
            },
            new CustomerServiceAgent
            {
                Id = 2,
                Name = "人工客服-小王",
                Email = "<EMAIL>",
                Status = AgentStatus.Available,
                Skills = new List<string> { "复杂问题处理", "投诉处理", "VIP服务" },
                MaxSessions = 5,
                Metadata = new Dictionary<string, object>
                {
                    ["type"] = "human_agent",
                    ["department"] = "customer_service",
                    ["experience_years"] = 3
                }
            }
        };

        foreach (var agent in defaultAgents)
        {
            await agentRepository.AddAsync(agent);
        }

        // 创建一些示例会话和消息
        var sessionRepository = scope.ServiceProvider.GetRequiredService<IRepository<ChatSession>>();
        var messageRepository = scope.ServiceProvider.GetRequiredService<IRepository<ChatMessage>>();
        
        var demoSession = new ChatSession
        {
            Id = 1,
            SessionId = "demo-session-001",
            UserId = "demo-user",
            UserName = "演示用户",
            Status = SessionStatus.Closed,
            StartTime = DateTime.UtcNow.AddHours(-2),
            EndTime = DateTime.UtcNow.AddHours(-1),
            MessageCount = 6,
            AssignedAgent = "AI助手小柿"
        };

        await sessionRepository.AddAsync(demoSession);

        var demoMessages = new List<ChatMessage>
        {
            new ChatMessage
            {
                Id = 1,
                SessionId = "demo-session-001",
                UserId = "demo-user",
                Content = "你好，我想咨询一下iPhone的价格",
                Type = MessageType.Text,
                Sender = MessageSender.User,
                Timestamp = DateTime.UtcNow.AddHours(-2)
            },
            new ChatMessage
            {
                Id = 2,
                SessionId = "demo-session-001",
                Content = "您好！欢迎来到柿子时尚，我是您的专属AI客服助手。关于iPhone产品，我们有多款型号可供选择。请问您想了解哪个具体型号呢？",
                Type = MessageType.Text,
                Sender = MessageSender.Bot,
                Timestamp = DateTime.UtcNow.AddHours(-2).AddMinutes(1)
            },
            new ChatMessage
            {
                Id = 3,
                SessionId = "demo-session-001",
                UserId = "demo-user",
                Content = "iPhone 15 Pro Max多少钱？",
                Type = MessageType.Text,
                Sender = MessageSender.User,
                Timestamp = DateTime.UtcNow.AddHours(-2).AddMinutes(2)
            },
            new ChatMessage
            {
                Id = 4,
                SessionId = "demo-session-001",
                Content = "iPhone 15 Pro Max目前的价格是9999元起（256GB版本）。我们还提供以下服务：\n1. 免费配送\n2. 7天无理由退换\n3. 官方保修\n4. 分期付款选项\n\n您还想了解其他信息吗？",
                Type = MessageType.Text,
                Sender = MessageSender.Bot,
                Timestamp = DateTime.UtcNow.AddHours(-2).AddMinutes(3)
            },
            new ChatMessage
            {
                Id = 5,
                SessionId = "demo-session-001",
                UserId = "demo-user",
                Content = "可以分期吗？",
                Type = MessageType.Text,
                Sender = MessageSender.User,
                Timestamp = DateTime.UtcNow.AddHours(-2).AddMinutes(4)
            },
            new ChatMessage
            {
                Id = 6,
                SessionId = "demo-session-001",
                Content = "当然可以！我们支持多种分期方式：\n1. 花呗分期：3/6/12期免息\n2. 信用卡分期：最长24期\n3. 京东白条：最长12期\n\n分期购买还可享受额外优惠，您可以在结算时选择合适的分期方案。还有其他问题吗？",
                Type = MessageType.Text,
                Sender = MessageSender.Bot,
                Timestamp = DateTime.UtcNow.AddHours(-2).AddMinutes(5)
            }
        };

        foreach (var message in demoMessages)
        {
            await messageRepository.AddAsync(message);
        }

        logger.LogInformation("AI客服机器人默认数据初始化完成");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "初始化默认数据时发生错误");
    }
}
