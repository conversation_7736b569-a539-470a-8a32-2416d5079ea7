using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.CustomerServiceBot.Models;

/// <summary>
/// 聊天消息
/// </summary>
public class ChatMessage
{
    public int Id { get; set; }
    
    [Required]
    public string SessionId { get; set; } = string.Empty;
    
    public string? UserId { get; set; }
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    public MessageType Type { get; set; } = MessageType.Text;
    
    public MessageSender Sender { get; set; } = MessageSender.User;
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Metadata { get; set; } = new();
    
    public List<MessageAttachment> Attachments { get; set; } = new();
    
    public string? ReplyToMessageId { get; set; }
    
    public MessageStatus Status { get; set; } = MessageStatus.Sent;
}

/// <summary>
/// 聊天会话
/// </summary>
public class ChatSession
{
    public int Id { get; set; }
    
    [Required]
    public string SessionId { get; set; } = Guid.NewGuid().ToString();
    
    public string? UserId { get; set; }
    
    public string? UserName { get; set; }
    
    public SessionStatus Status { get; set; } = SessionStatus.Active;
    
    public DateTime StartTime { get; set; } = DateTime.UtcNow;
    
    public DateTime? EndTime { get; set; }
    
    public List<ChatMessage> Messages { get; set; } = new();
    
    public Dictionary<string, object> Context { get; set; } = new();
    
    public string? AssignedAgent { get; set; }
    
    public int MessageCount { get; set; }
    
    public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.UtcNow.Subtract(StartTime);
}

/// <summary>
/// 聊天请求
/// </summary>
public class ChatRequest
{
    [Required]
    public string SessionId { get; set; } = string.Empty;
    
    [Required]
    public string Message { get; set; } = string.Empty;
    
    public string? UserId { get; set; }
    
    public MessageType Type { get; set; } = MessageType.Text;
    
    public Dictionary<string, object> Context { get; set; } = new();
    
    public List<MessageAttachment> Attachments { get; set; } = new();
}

/// <summary>
/// 聊天响应
/// </summary>
public class ChatResponse
{
    public string SessionId { get; set; } = string.Empty;
    
    public string Response { get; set; } = string.Empty;
    
    public MessageType Type { get; set; } = MessageType.Text;
    
    public float Confidence { get; set; }
    
    public List<string> SuggestedActions { get; set; } = new();
    
    public List<QuickReply> QuickReplies { get; set; } = new();
    
    public Dictionary<string, object> Metadata { get; set; } = new();
    
    public bool RequiresHumanAgent { get; set; }
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 意图识别结果
/// </summary>
public class IntentRecognitionResult
{
    public string Intent { get; set; } = string.Empty;
    
    public float Confidence { get; set; }
    
    public Dictionary<string, object> Entities { get; set; } = new();
    
    public List<IntentCandidate> Candidates { get; set; } = new();
}

/// <summary>
/// 意图候选
/// </summary>
public class IntentCandidate
{
    public string Intent { get; set; } = string.Empty;
    
    public float Score { get; set; }
    
    public Dictionary<string, object> Entities { get; set; } = new();
}

/// <summary>
/// 消息附件
/// </summary>
public class MessageAttachment
{
    public string Type { get; set; } = string.Empty;
    
    public string Url { get; set; } = string.Empty;
    
    public string? Title { get; set; }
    
    public string? Description { get; set; }
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 快速回复
/// </summary>
public class QuickReply
{
    public string Text { get; set; } = string.Empty;
    
    public string Value { get; set; } = string.Empty;
    
    public string? Icon { get; set; }
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 知识库条目
/// </summary>
public class KnowledgeBaseEntry
{
    public int Id { get; set; }
    
    [Required]
    public string Question { get; set; } = string.Empty;
    
    [Required]
    public string Answer { get; set; } = string.Empty;
    
    public List<string> Keywords { get; set; } = new();
    
    public string Category { get; set; } = string.Empty;
    
    public List<string> Tags { get; set; } = new();
    
    public float Confidence { get; set; } = 1.0f;
    
    public int UsageCount { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 对话上下文
/// </summary>
public class ConversationContext
{
    public string SessionId { get; set; } = string.Empty;
    
    public string? CurrentIntent { get; set; }
    
    public Dictionary<string, object> Entities { get; set; } = new();
    
    public List<string> ConversationHistory { get; set; } = new();
    
    public Dictionary<string, object> UserProfile { get; set; } = new();
    
    public string? LastBotAction { get; set; }
    
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;
    
    public int TurnCount { get; set; }
}

/// <summary>
/// 客服代理
/// </summary>
public class CustomerServiceAgent
{
    public int Id { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Email { get; set; }
    
    public AgentStatus Status { get; set; } = AgentStatus.Available;
    
    public List<string> Skills { get; set; } = new();
    
    public int ActiveSessions { get; set; }
    
    public int MaxSessions { get; set; } = 5;
    
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 消息类型枚举
/// </summary>
public enum MessageType
{
    Text = 1,
    Image = 2,
    File = 3,
    Audio = 4,
    Video = 5,
    Location = 6,
    Card = 7,
    QuickReply = 8,
    System = 9
}

/// <summary>
/// 消息发送者枚举
/// </summary>
public enum MessageSender
{
    User = 1,
    Bot = 2,
    Agent = 3,
    System = 4
}

/// <summary>
/// 消息状态枚举
/// </summary>
public enum MessageStatus
{
    Sent = 1,
    Delivered = 2,
    Read = 3,
    Failed = 4
}

/// <summary>
/// 会话状态枚举
/// </summary>
public enum SessionStatus
{
    Active = 1,
    Waiting = 2,
    Closed = 3,
    Transferred = 4,
    Escalated = 5
}

/// <summary>
/// 代理状态枚举
/// </summary>
public enum AgentStatus
{
    Available = 1,
    Busy = 2,
    Away = 3,
    Offline = 4
}
