{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "Per<PERSON><PERSON>on<PERSON><PERSON>_Super_Secret_Key_2024_Demo_Application", "Issuer": "PersimmonChic", "Audience": "PersimmonChic-Users", "ExpirationHours": 24}, "Database": {"Type": "InMemory", "ConnectionString": ""}, "ServiceDiscovery": {"ServiceName": "UserService", "Address": "localhost", "Port": 5001, "HealthCheckEndpoint": "/health", "GatewayUrl": "http://localhost:5000"}}