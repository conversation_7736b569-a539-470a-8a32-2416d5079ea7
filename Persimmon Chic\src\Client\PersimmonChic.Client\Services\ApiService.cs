using PersimmonChic.Shared.Models;
using System.Text;
using System.Text.Json;

namespace PersimmonChic.Client.Services;

/// <summary>
/// API 服务类 - 负责与后端微服务通信
/// </summary>
public class ApiService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;
    private string? _authToken;

    public ApiService(string baseUrl)
    {
        _httpClient = new HttpClient
        {
            BaseAddress = new Uri(baseUrl),
            Timeout = TimeSpan.FromSeconds(30)
        };

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };

        // 设置默认请求头
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "PersimmonChic-Client/1.0");
    }

    /// <summary>
    /// 设置认证令牌
    /// </summary>
    /// <param name="token">JWT令牌</param>
    public void SetAuthToken(string? token)
    {
        _authToken = token;
        
        if (!string.IsNullOrEmpty(token))
        {
            _httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        }
        else
        {
            _httpClient.DefaultRequestHeaders.Authorization = null;
        }
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录响应</returns>
    public async Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request)
    {
        try
        {
            var response = await PostAsync<LoginRequest, LoginResponse>("/api/users/login", request);
            
            if (response.Success && response.Data != null)
            {
                SetAuthToken(response.Data.Token);
            }
            
            return response;
        }
        catch (Exception ex)
        {
            return ApiResponse<LoginResponse>.ErrorResult($"登录请求失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    /// <returns>注册响应</returns>
    public async Task<ApiResponse<User>> RegisterAsync(RegisterRequest request)
    {
        try
        {
            return await PostAsync<RegisterRequest, User>("/api/users/register", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<User>.ErrorResult($"注册请求失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>用户列表</returns>
    public async Task<ApiResponse<PagedResponse<User>>> GetUsersAsync(PagedRequest request)
    {
        try
        {
            return await PostAsync<PagedRequest, PagedResponse<User>>("/api/users/list", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<PagedResponse<User>>.ErrorResult($"获取用户列表失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取商品列表
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>商品列表</returns>
    public async Task<ApiResponse<PagedResponse<Product>>> GetProductsAsync(PagedRequest request)
    {
        try
        {
            return await PostAsync<PagedRequest, PagedResponse<Product>>("/api/products/list", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<PagedResponse<Product>>.ErrorResult($"获取商品列表失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="request">分页请求</param>
    /// <returns>订单列表</returns>
    public async Task<ApiResponse<PagedResponse<Order>>> GetOrdersAsync(PagedRequest request)
    {
        try
        {
            return await PostAsync<PagedRequest, PagedResponse<Order>>("/api/orders/list", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<PagedResponse<Order>>.ErrorResult($"获取订单列表失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查服务健康状态
    /// </summary>
    /// <returns>健康状态</returns>
    public async Task<ApiResponse<List<HealthStatus>>> GetServiceHealthAsync()
    {
        try
        {
            return await GetAsync<List<HealthStatus>>("/api/gateway/health");
        }
        catch (Exception ex)
        {
            return ApiResponse<List<HealthStatus>>.ErrorResult($"获取服务健康状态失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 发送GET请求
    /// </summary>
    /// <typeparam name="T">响应类型</typeparam>
    /// <param name="endpoint">端点</param>
    /// <returns>响应结果</returns>
    private async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ApiResponse<T>>(content, _jsonOptions);
                return result ?? ApiResponse<T>.ErrorResult("响应反序列化失败");
            }
            else
            {
                return ApiResponse<T>.ErrorResult($"请求失败: {response.StatusCode}");
            }
        }
        catch (HttpRequestException ex)
        {
            return ApiResponse<T>.ErrorResult($"网络请求失败: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            return ApiResponse<T>.ErrorResult($"请求超时: {ex.Message}");
        }
        catch (JsonException ex)
        {
            return ApiResponse<T>.ErrorResult($"响应解析失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 发送POST请求
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="endpoint">端点</param>
    /// <param name="request">请求数据</param>
    /// <returns>响应结果</returns>
    private async Task<ApiResponse<TResponse>> PostAsync<TRequest, TResponse>(string endpoint, TRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(endpoint, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ApiResponse<TResponse>>(responseContent, _jsonOptions);
                return result ?? ApiResponse<TResponse>.ErrorResult("响应反序列化失败");
            }
            else
            {
                return ApiResponse<TResponse>.ErrorResult($"请求失败: {response.StatusCode}");
            }
        }
        catch (HttpRequestException ex)
        {
            return ApiResponse<TResponse>.ErrorResult($"网络请求失败: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            return ApiResponse<TResponse>.ErrorResult($"请求超时: {ex.Message}");
        }
        catch (JsonException ex)
        {
            return ApiResponse<TResponse>.ErrorResult($"响应解析失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
