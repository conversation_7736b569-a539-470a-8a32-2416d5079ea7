using PersimmonChic.Shared.Models;
using System.Linq.Expressions;

namespace PersimmonChic.Infrastructure.DataAccess;

/// <summary>
/// 通用仓储接口
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public interface IRepository<T> where T : class
{
    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>实体</returns>
    Task<T?> GetByIdAsync(object id);

    /// <summary>
    /// 获取所有实体
    /// </summary>
    /// <returns>实体列表</returns>
    Task<List<T>> GetAllAsync();

    /// <summary>
    /// 根据条件查找实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>实体列表</returns>
    Task<List<T>> FindAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// 根据条件查找单个实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>实体</returns>
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="predicate">查询条件</param>
    /// <param name="orderBy">排序条件</param>
    /// <param name="ascending">是否升序</param>
    /// <returns>分页结果</returns>
    Task<PagedResponse<T>> GetPagedAsync<TKey>(
        int page, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, TKey>>? orderBy = null,
        bool ascending = true);

    /// <summary>
    /// 添加实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>添加的实体</returns>
    Task<T> AddAsync(T entity);

    /// <summary>
    /// 批量添加实体
    /// </summary>
    /// <param name="entities">实体列表</param>
    /// <returns>添加的实体列表</returns>
    Task<List<T>> AddRangeAsync(IEnumerable<T> entities);

    /// <summary>
    /// 更新实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>更新的实体</returns>
    Task<T> UpdateAsync(T entity);

    /// <summary>
    /// 删除实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteAsync(T entity);

    /// <summary>
    /// 根据ID删除实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteByIdAsync(object id);

    /// <summary>
    /// 根据条件删除实体
    /// </summary>
    /// <param name="predicate">删除条件</param>
    /// <returns>删除的数量</returns>
    Task<int> DeleteAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// 获取实体数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>实体数量</returns>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <returns>影响的行数</returns>
    Task<int> SaveChangesAsync();
}

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 用户仓储
    /// </summary>
    IRepository<User> Users { get; }

    /// <summary>
    /// 商品仓储
    /// </summary>
    IRepository<Product> Products { get; }

    /// <summary>
    /// 分类仓储
    /// </summary>
    IRepository<Category> Categories { get; }

    /// <summary>
    /// 订单仓储
    /// </summary>
    IRepository<Order> Orders { get; }

    /// <summary>
    /// 订单项仓储
    /// </summary>
    IRepository<OrderItem> OrderItems { get; }

    /// <summary>
    /// 订单状态历史仓储
    /// </summary>
    IRepository<OrderStatusHistory> OrderStatusHistory { get; }

    /// <summary>
    /// 商品图片仓储
    /// </summary>
    IRepository<ProductImage> ProductImages { get; }

    /// <summary>
    /// 商品属性仓储
    /// </summary>
    IRepository<ProductAttribute> ProductAttributes { get; }

    /// <summary>
    /// 审计日志仓储
    /// </summary>
    IRepository<AuditLog> AuditLogs { get; }

    /// <summary>
    /// 消息仓储
    /// </summary>
    IRepository<Message> Messages { get; }

    /// <summary>
    /// 配置项仓储
    /// </summary>
    IRepository<ConfigurationItem> ConfigurationItems { get; }

    /// <summary>
    /// 开始事务
    /// </summary>
    /// <returns>事务</returns>
    Task<IDbTransaction> BeginTransactionAsync();

    /// <summary>
    /// 提交事务
    /// </summary>
    /// <returns>提交结果</returns>
    Task<int> CommitAsync();

    /// <summary>
    /// 回滚事务
    /// </summary>
    Task RollbackAsync();
}

/// <summary>
/// 数据库事务接口
/// </summary>
public interface IDbTransaction : IDisposable
{
    /// <summary>
    /// 提交事务
    /// </summary>
    Task CommitAsync();

    /// <summary>
    /// 回滚事务
    /// </summary>
    Task RollbackAsync();
}
