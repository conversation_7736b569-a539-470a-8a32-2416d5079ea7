using Microsoft.Extensions.Logging;
using Microsoft.ML;
using Microsoft.ML.Data;
using PersimmonChic.RiskControlService.Models;
using PersimmonChic.Shared.Models;
using System.Text.Json;

namespace PersimmonChic.RiskControlService.Services;

/// <summary>
/// 机器学习风险评估服务实现
/// </summary>
public class MLRiskAssessmentService : IMLRiskAssessmentService
{
    private readonly ILogger<MLRiskAssessmentService> _logger;
    private readonly MLContext _mlContext;
    private ITransformer? _model;
    private readonly string _modelPath;
    private readonly object _modelLock = new();

    public MLRiskAssessmentService(ILogger<MLRiskAssessmentService> logger)
    {
        _logger = logger;
        _mlContext = new MLContext(seed: 0);
        _modelPath = Path.Combine(AppContext.BaseDirectory, "Models", "risk_assessment_model.zip");
        
        // 确保模型目录存在
        Directory.CreateDirectory(Path.GetDirectoryName(_modelPath)!);
        
        // 尝试加载现有模型
        LoadExistingModel();
    }

    public async Task<ApiResponse<bool>> TrainModelAsync(List<UserBehaviorEvent> trainingData)
    {
        try
        {
            _logger.LogInformation("开始训练风险评估模型，训练数据量: {Count}", trainingData.Count);

            if (trainingData.Count < 10)
            {
                return ApiResponse<bool>.ErrorResult("训练数据量不足，至少需要10条数据");
            }

            // 转换训练数据
            var mlData = ConvertToMLData(trainingData);
            var dataView = _mlContext.Data.LoadFromEnumerable(mlData);

            // 定义数据处理管道
            var pipeline = _mlContext.Transforms.Text.FeaturizeText("EventTypeFeatures", nameof(RiskTrainingData.EventType))
                .Append(_mlContext.Transforms.Text.FeaturizeText("IpAddressFeatures", nameof(RiskTrainingData.IpAddress)))
                .Append(_mlContext.Transforms.Text.FeaturizeText("UserAgentFeatures", nameof(RiskTrainingData.UserAgent)))
                .Append(_mlContext.Transforms.Categorical.OneHotEncoding("HourOfDayEncoded", nameof(RiskTrainingData.HourOfDay)))
                .Append(_mlContext.Transforms.Categorical.OneHotEncoding("DayOfWeekEncoded", nameof(RiskTrainingData.DayOfWeek)))
                .Append(_mlContext.Transforms.Concatenate("Features", 
                    "EventTypeFeatures", "IpAddressFeatures", "UserAgentFeatures", 
                    "HourOfDayEncoded", "DayOfWeekEncoded",
                    nameof(RiskTrainingData.UserId), nameof(RiskTrainingData.FrequencyScore)))
                .Append(_mlContext.Regression.Trainers.FastTree(labelColumnName: nameof(RiskTrainingData.RiskScore)));

            // 训练模型
            var model = pipeline.Fit(dataView);

            // 保存模型
            lock (_modelLock)
            {
                _mlContext.Model.Save(model, dataView.Schema, _modelPath);
                _model = model;
            }

            _logger.LogInformation("风险评估模型训练完成并已保存");
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练风险评估模型时发生错误");
            return ApiResponse<bool>.ErrorResult($"模型训练失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<double>> PredictRiskScoreAsync(UserBehaviorEvent behaviorEvent)
    {
        try
        {
            if (_model == null)
            {
                _logger.LogWarning("风险评估模型未加载，使用默认评分");
                return ApiResponse<double>.SuccessResult(await CalculateDefaultRiskScoreAsync(behaviorEvent));
            }

            // 转换输入数据
            var inputData = ConvertToMLInput(behaviorEvent);
            
            // 创建预测引擎
            var predictionEngine = _mlContext.Model.CreatePredictionEngine<RiskTrainingData, RiskPrediction>(_model);
            
            // 进行预测
            var prediction = predictionEngine.Predict(inputData);
            
            // 确保预测结果在合理范围内
            var riskScore = Math.Max(0, Math.Min(100, prediction.RiskScore));
            
            _logger.LogDebug("ML模型预测风险分数: {RiskScore} (用户: {UserId}, 事件: {EventType})", 
                riskScore, behaviorEvent.UserId, behaviorEvent.EventType);

            return ApiResponse<double>.SuccessResult(riskScore);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测风险分数时发生错误");
            
            // 发生错误时使用默认评分
            var defaultScore = await CalculateDefaultRiskScoreAsync(behaviorEvent);
            return ApiResponse<double>.SuccessResult(defaultScore);
        }
    }

    public async Task<ApiResponse<Dictionary<string, double>>> GetModelMetricsAsync()
    {
        try
        {
            var metrics = new Dictionary<string, double>();

            if (_model == null)
            {
                metrics["model_loaded"] = 0;
                metrics["last_training_time"] = 0;
                return ApiResponse<Dictionary<string, double>>.SuccessResult(metrics);
            }

            // 获取模型文件信息
            if (File.Exists(_modelPath))
            {
                var fileInfo = new FileInfo(_modelPath);
                metrics["model_loaded"] = 1;
                metrics["model_size_bytes"] = fileInfo.Length;
                metrics["last_training_time"] = new DateTimeOffset(fileInfo.LastWriteTime).ToUnixTimeSeconds();
            }

            // 添加运行时指标
            metrics["prediction_count"] = _predictionCount;
            metrics["average_prediction_time_ms"] = _totalPredictionTime / Math.Max(1, _predictionCount);
            metrics["error_count"] = _errorCount;

            return ApiResponse<Dictionary<string, double>>.SuccessResult(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模型性能指标时发生错误");
            return ApiResponse<Dictionary<string, double>>.ErrorResult($"获取模型指标失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateModelAsync(List<UserBehaviorEvent> newData)
    {
        try
        {
            _logger.LogInformation("开始更新风险评估模型，新数据量: {Count}", newData.Count);

            // 对于增量学习，这里简化为重新训练
            // 实际生产环境中可能需要更复杂的增量学习策略
            return await TrainModelAsync(newData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新风险评估模型时发生错误");
            return ApiResponse<bool>.ErrorResult($"模型更新失败: {ex.Message}");
        }
    }

    // 私有字段用于统计
    private long _predictionCount = 0;
    private double _totalPredictionTime = 0;
    private long _errorCount = 0;

    private void LoadExistingModel()
    {
        try
        {
            if (File.Exists(_modelPath))
            {
                lock (_modelLock)
                {
                    _model = _mlContext.Model.Load(_modelPath, out _);
                }
                _logger.LogInformation("已加载现有风险评估模型");
            }
            else
            {
                _logger.LogInformation("未找到现有模型文件，将在首次训练时创建");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载现有模型时发生错误");
        }
    }

    private List<RiskTrainingData> ConvertToMLData(List<UserBehaviorEvent> behaviorEvents)
    {
        return behaviorEvents.Select(e => ConvertToMLInput(e, CalculateActualRiskScore(e))).ToList();
    }

    private RiskTrainingData ConvertToMLInput(UserBehaviorEvent behaviorEvent, float? actualRiskScore = null)
    {
        return new RiskTrainingData
        {
            UserId = behaviorEvent.UserId,
            EventType = behaviorEvent.EventType ?? "unknown",
            IpAddress = HashString(behaviorEvent.IpAddress), // 哈希化IP地址保护隐私
            UserAgent = ExtractUserAgentFeatures(behaviorEvent.UserAgent),
            HourOfDay = behaviorEvent.Timestamp.Hour,
            DayOfWeek = (int)behaviorEvent.Timestamp.DayOfWeek,
            FrequencyScore = CalculateFrequencyScore(behaviorEvent),
            RiskScore = actualRiskScore ?? 0f
        };
    }

    private float CalculateActualRiskScore(UserBehaviorEvent behaviorEvent)
    {
        // 基于规则的风险评分，用于训练数据标注
        float score = 0f;

        // 基于事件类型的基础分数
        score += behaviorEvent.EventType?.ToLower() switch
        {
            "login" => 10f,
            "password_change" => 30f,
            "payment" => 40f,
            "admin_action" => 50f,
            _ => 5f
        };

        // 基于时间的风险评分
        var hour = behaviorEvent.Timestamp.Hour;
        if (hour >= 2 && hour <= 6) // 凌晨时段
        {
            score += 15f;
        }

        // 基于元数据的风险评分
        if (behaviorEvent.Metadata.ContainsKey("failed_attempts"))
        {
            if (int.TryParse(behaviorEvent.Metadata["failed_attempts"].ToString(), out var attempts))
            {
                score += attempts * 5f;
            }
        }

        return Math.Min(100f, score);
    }

    private async Task<double> CalculateDefaultRiskScoreAsync(UserBehaviorEvent behaviorEvent)
    {
        // 当ML模型不可用时的默认风险评分逻辑
        double score = 0;

        // 基于事件类型
        score += behaviorEvent.EventType?.ToLower() switch
        {
            "login" => 10,
            "password_change" => 25,
            "payment" => 35,
            "admin_action" => 45,
            _ => 5
        };

        // 基于时间
        var hour = behaviorEvent.Timestamp.Hour;
        if (hour >= 2 && hour <= 6)
        {
            score += 15;
        }

        // 基于频率（简化实现）
        score += CalculateFrequencyScore(behaviorEvent);

        return await Task.FromResult(Math.Min(100, score));
    }

    private string HashString(string input)
    {
        if (string.IsNullOrEmpty(input)) return "unknown";
        
        // 简单哈希，实际应该使用更安全的哈希算法
        return input.GetHashCode().ToString();
    }

    private string ExtractUserAgentFeatures(string userAgent)
    {
        if (string.IsNullOrEmpty(userAgent)) return "unknown";

        // 提取用户代理的关键特征
        var features = new List<string>();

        if (userAgent.Contains("Chrome")) features.Add("chrome");
        if (userAgent.Contains("Firefox")) features.Add("firefox");
        if (userAgent.Contains("Safari")) features.Add("safari");
        if (userAgent.Contains("Edge")) features.Add("edge");
        if (userAgent.Contains("Mobile")) features.Add("mobile");
        if (userAgent.Contains("Windows")) features.Add("windows");
        if (userAgent.Contains("Mac")) features.Add("mac");
        if (userAgent.Contains("Linux")) features.Add("linux");

        return features.Any() ? string.Join(",", features) : "unknown";
    }

    private float CalculateFrequencyScore(UserBehaviorEvent behaviorEvent)
    {
        // 简化的频率评分，实际应该基于历史数据
        // 这里返回一个基于用户ID和时间的简单评分
        var timeScore = (DateTime.UtcNow.Hour % 6) * 2f;
        var userScore = (behaviorEvent.UserId % 10) * 1f;
        
        return timeScore + userScore;
    }
}

/// <summary>
/// ML训练数据模型
/// </summary>
public class RiskTrainingData
{
    public float UserId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public float HourOfDay { get; set; }
    public float DayOfWeek { get; set; }
    public float FrequencyScore { get; set; }
    public float RiskScore { get; set; } // 标签
}

/// <summary>
/// ML预测结果模型
/// </summary>
public class RiskPrediction
{
    [ColumnName("Score")]
    public float RiskScore { get; set; }
}
