using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PersimmonChic.PricingService.Models;
using PersimmonChic.PricingService.Services;
using PersimmonChic.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.PricingService.Controllers;

/// <summary>
/// 价格服务控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PricingController : ControllerBase
{
    private readonly IPricingService _pricingService;
    private readonly IDynamicPricingService _dynamicPricingService;
    private readonly IPriceCacheService _priceCacheService;
    private readonly IPriceNotificationService _priceNotificationService;
    private readonly ILogger<PricingController> _logger;

    public PricingController(
        IPricingService pricingService,
        IDynamicPricingService dynamicPricingService,
        IPriceCacheService priceCacheService,
        IPriceNotificationService priceNotificationService,
        ILogger<PricingController> logger)
    {
        _pricingService = pricingService;
        _dynamicPricingService = dynamicPricingService;
        _priceCacheService = priceCacheService;
        _priceNotificationService = priceNotificationService;
        _logger = logger;
    }

    /// <summary>
    /// 计算商品价格
    /// </summary>
    /// <param name="request">价格计算请求</param>
    /// <returns>价格计算响应</returns>
    [HttpPost("calculate")]
    [ProducesResponseType(typeof(ApiResponse<PriceCalculationResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<PriceCalculationResponse>), 400)]
    public async Task<ActionResult<ApiResponse<PriceCalculationResponse>>> CalculatePrice(
        [FromBody] PriceCalculationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<PriceCalculationResponse>.ErrorResult("请求参数无效"));
            }

            var result = await _pricingService.CalculatePriceAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算商品价格时发生错误");
            return StatusCode(500, ApiResponse<PriceCalculationResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取商品基础价格
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="currency">货币代码</param>
    /// <returns>商品价格</returns>
    [HttpGet("product/{productId}")]
    [ProducesResponseType(typeof(ApiResponse<ProductPrice>), 200)]
    [ProducesResponseType(typeof(ApiResponse<ProductPrice>), 404)]
    public async Task<ActionResult<ApiResponse<ProductPrice>>> GetProductPrice(
        [FromRoute] int productId,
        [FromQuery] string currency = "CNY")
    {
        try
        {
            if (productId <= 0)
            {
                return BadRequest(ApiResponse<ProductPrice>.ErrorResult("商品ID无效"));
            }

            var result = await _pricingService.GetProductPriceAsync(productId, currency);
            
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(ApiResponse<ProductPrice>.ErrorResult("商品价格不存在"));
                }
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品价格时发生错误");
            return StatusCode(500, ApiResponse<ProductPrice>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 更新商品价格
    /// </summary>
    /// <param name="productPrice">商品价格</param>
    /// <returns>操作结果</returns>
    [HttpPut("product")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin,PriceManager")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateProductPrice(
        [FromBody] ProductPrice productPrice)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _pricingService.UpdateProductPriceAsync(productPrice);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新商品价格时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 批量更新商品价格
    /// </summary>
    /// <param name="request">批量更新请求</param>
    /// <returns>操作结果</returns>
    [HttpPost("bulk-update")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin,PriceManager")]
    public async Task<ActionResult<ApiResponse<bool>>> BulkUpdatePrices(
        [FromBody] BulkPriceUpdateRequest request)
    {
        try
        {
            if (!ModelState.IsValid || !request.Updates.Any())
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _pricingService.BulkUpdatePricesAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新商品价格时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取渠道价格
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="channelCode">渠道代码</param>
    /// <returns>渠道价格</returns>
    [HttpGet("channel/{productId}/{channelCode}")]
    [ProducesResponseType(typeof(ApiResponse<ChannelPrice>), 200)]
    [ProducesResponseType(typeof(ApiResponse<ChannelPrice>), 404)]
    public async Task<ActionResult<ApiResponse<ChannelPrice>>> GetChannelPrice(
        [FromRoute] int productId,
        [FromRoute] string channelCode)
    {
        try
        {
            if (productId <= 0 || string.IsNullOrWhiteSpace(channelCode))
            {
                return BadRequest(ApiResponse<ChannelPrice>.ErrorResult("参数无效"));
            }

            var result = await _pricingService.GetChannelPriceAsync(productId, channelCode);
            
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(ApiResponse<ChannelPrice>.ErrorResult("渠道价格不存在"));
                }
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取渠道价格时发生错误");
            return StatusCode(500, ApiResponse<ChannelPrice>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 设置渠道价格
    /// </summary>
    /// <param name="channelPrice">渠道价格</param>
    /// <returns>操作结果</returns>
    [HttpPost("channel")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin,PriceManager")]
    public async Task<ActionResult<ApiResponse<bool>>> SetChannelPrice(
        [FromBody] ChannelPrice channelPrice)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _pricingService.SetChannelPriceAsync(channelPrice);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置渠道价格时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取用户等级价格
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="userTier">用户等级</param>
    /// <returns>用户等级价格</returns>
    [HttpGet("user-tier/{productId}/{userTier}")]
    [ProducesResponseType(typeof(ApiResponse<UserTierPrice>), 200)]
    [ProducesResponseType(typeof(ApiResponse<UserTierPrice>), 404)]
    public async Task<ActionResult<ApiResponse<UserTierPrice>>> GetUserTierPrice(
        [FromRoute] int productId,
        [FromRoute] UserTier userTier)
    {
        try
        {
            if (productId <= 0)
            {
                return BadRequest(ApiResponse<UserTierPrice>.ErrorResult("商品ID无效"));
            }

            var result = await _pricingService.GetUserTierPriceAsync(productId, userTier);
            
            if (result.Success)
            {
                if (result.Data == null)
                {
                    return NotFound(ApiResponse<UserTierPrice>.ErrorResult("用户等级价格不存在"));
                }
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户等级价格时发生错误");
            return StatusCode(500, ApiResponse<UserTierPrice>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取价格历史
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>价格历史列表</returns>
    [HttpGet("history/{productId}")]
    [ProducesResponseType(typeof(ApiResponse<List<PriceHistory>>), 200)]
    public async Task<ActionResult<ApiResponse<List<PriceHistory>>>> GetPriceHistory(
        [FromRoute] int productId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            if (productId <= 0)
            {
                return BadRequest(ApiResponse<List<PriceHistory>>.ErrorResult("商品ID无效"));
            }

            var result = await _pricingService.GetPriceHistoryAsync(productId, startDate, endDate);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取价格历史时发生错误");
            return StatusCode(500, ApiResponse<List<PriceHistory>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取动态价格规则
    /// </summary>
    /// <returns>动态价格规则列表</returns>
    [HttpGet("dynamic-rules")]
    [ProducesResponseType(typeof(ApiResponse<List<DynamicPricingRule>>), 200)]
    public async Task<ActionResult<ApiResponse<List<DynamicPricingRule>>>> GetDynamicPricingRules()
    {
        try
        {
            var result = await _dynamicPricingService.GetDynamicPricingRulesAsync();
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取动态价格规则时发生错误");
            return StatusCode(500, ApiResponse<List<DynamicPricingRule>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 创建动态价格规则
    /// </summary>
    /// <param name="rule">动态价格规则</param>
    /// <returns>创建的规则</returns>
    [HttpPost("dynamic-rules")]
    [ProducesResponseType(typeof(ApiResponse<DynamicPricingRule>), 201)]
    [ProducesResponseType(typeof(ApiResponse<DynamicPricingRule>), 400)]
    [Authorize(Roles = "Admin,PriceManager")]
    public async Task<ActionResult<ApiResponse<DynamicPricingRule>>> CreateDynamicPricingRule(
        [FromBody] DynamicPricingRule rule)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<DynamicPricingRule>.ErrorResult("请求参数无效"));
            }

            var result = await _dynamicPricingService.CreateDynamicPricingRuleAsync(rule);
            
            if (result.Success)
            {
                return CreatedAtAction(nameof(GetDynamicPricingRules), result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建动态价格规则时发生错误");
            return StatusCode(500, ApiResponse<DynamicPricingRule>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 订阅价格变更通知
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="subscriberId">订阅者ID</param>
    /// <param name="notificationMethod">通知方式</param>
    /// <returns>操作结果</returns>
    [HttpPost("subscribe/{productId}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    public async Task<ActionResult<ApiResponse<bool>>> SubscribePriceChangeNotification(
        [FromRoute] int productId,
        [FromQuery, Required] string subscriberId,
        [FromQuery, Required] string notificationMethod)
    {
        try
        {
            if (productId <= 0 || string.IsNullOrWhiteSpace(subscriberId) || string.IsNullOrWhiteSpace(notificationMethod))
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _priceNotificationService.SubscribePriceChangeNotificationAsync(
                productId, subscriberId, notificationMethod);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅价格变更通知时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 清除价格缓存
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("cache/{productId}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [Authorize(Roles = "Admin,PriceManager")]
    public async Task<ActionResult<ApiResponse<bool>>> ClearPriceCache([FromRoute] int productId)
    {
        try
        {
            if (productId <= 0)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("商品ID无效"));
            }

            var result = await _priceCacheService.ClearProductPriceCacheAsync(productId);
            
            return Ok(ApiResponse<bool>.SuccessResult(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除价格缓存时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>服务健康状态</returns>
    [HttpGet("health")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Service = "PricingService",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Features = new[]
            {
                "动态定价",
                "渠道价格管理",
                "用户等级定价",
                "地域价格差异",
                "价格缓存",
                "价格变更通知"
            }
        });
    }
}
