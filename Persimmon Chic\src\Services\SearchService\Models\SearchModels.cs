using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.SearchService.Models;

/// <summary>
/// 搜索请求
/// </summary>
public class SearchRequest
{
    [Required]
    public string Query { get; set; } = string.Empty;
    
    public int Page { get; set; } = 1;
    
    public int PageSize { get; set; } = 20;
    
    public SearchType SearchType { get; set; } = SearchType.Semantic;
    
    public List<string> Categories { get; set; } = new();
    
    public List<string> Brands { get; set; } = new();
    
    public decimal? MinPrice { get; set; }
    
    public decimal? MaxPrice { get; set; }
    
    public SortBy SortBy { get; set; } = SortBy.Relevance;
    
    public SortOrder SortOrder { get; set; } = SortOrder.Descending;
    
    public bool EnableFacets { get; set; } = true;
    
    public bool EnableHighlighting { get; set; } = true;
    
    public bool EnableAutoComplete { get; set; } = false;
    
    public Dictionary<string, object> Filters { get; set; } = new();
    
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 搜索响应
/// </summary>
public class SearchResponse
{
    public string Query { get; set; } = string.Empty;
    
    public List<SearchResult> Results { get; set; } = new();
    
    public SearchMetadata Metadata { get; set; } = new();
    
    public List<SearchFacet> Facets { get; set; } = new();
    
    public List<string> Suggestions { get; set; } = new();
    
    public SearchAnalytics Analytics { get; set; } = new();
    
    public DateTime SearchTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 搜索结果项
/// </summary>
public class SearchResult
{
    public int Id { get; set; }
    
    public string Title { get; set; } = string.Empty;
    
    public string Description { get; set; } = string.Empty;
    
    public string Category { get; set; } = string.Empty;
    
    public string Brand { get; set; } = string.Empty;
    
    public decimal Price { get; set; }
    
    public string ImageUrl { get; set; } = string.Empty;
    
    public string Url { get; set; } = string.Empty;
    
    public float Score { get; set; }
    
    public float Relevance { get; set; }
    
    public List<string> Tags { get; set; } = new();
    
    public Dictionary<string, object> Attributes { get; set; } = new();
    
    public List<SearchHighlight> Highlights { get; set; } = new();
    
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// 搜索元数据
/// </summary>
public class SearchMetadata
{
    public int TotalResults { get; set; }
    
    public int Page { get; set; }
    
    public int PageSize { get; set; }
    
    public int TotalPages { get; set; }
    
    public long SearchTimeMs { get; set; }
    
    public string SearchId { get; set; } = Guid.NewGuid().ToString();
    
    public SearchType SearchType { get; set; }
    
    public string Algorithm { get; set; } = string.Empty;
    
    public Dictionary<string, object> Debug { get; set; } = new();
}

/// <summary>
/// 搜索分面
/// </summary>
public class SearchFacet
{
    public string Name { get; set; } = string.Empty;
    
    public string DisplayName { get; set; } = string.Empty;
    
    public FacetType Type { get; set; }
    
    public List<FacetValue> Values { get; set; } = new();
}

/// <summary>
/// 分面值
/// </summary>
public class FacetValue
{
    public string Value { get; set; } = string.Empty;
    
    public string DisplayValue { get; set; } = string.Empty;
    
    public int Count { get; set; }
    
    public bool Selected { get; set; }
}

/// <summary>
/// 搜索高亮
/// </summary>
public class SearchHighlight
{
    public string Field { get; set; } = string.Empty;
    
    public List<string> Fragments { get; set; } = new();
}

/// <summary>
/// 搜索分析数据
/// </summary>
public class SearchAnalytics
{
    public string SessionId { get; set; } = string.Empty;
    
    public string UserId { get; set; } = string.Empty;
    
    public string UserAgent { get; set; } = string.Empty;
    
    public string IpAddress { get; set; } = string.Empty;
    
    public string Referrer { get; set; } = string.Empty;
    
    public Dictionary<string, object> CustomData { get; set; } = new();
}

/// <summary>
/// 自动补全请求
/// </summary>
public class AutoCompleteRequest
{
    [Required]
    public string Query { get; set; } = string.Empty;
    
    public int MaxSuggestions { get; set; } = 10;
    
    public List<string> Categories { get; set; } = new();
    
    public bool IncludePopular { get; set; } = true;
    
    public bool IncludeHistory { get; set; } = true;
    
    public string? UserId { get; set; }
}

/// <summary>
/// 自动补全响应
/// </summary>
public class AutoCompleteResponse
{
    public string Query { get; set; } = string.Empty;
    
    public List<AutoCompleteSuggestion> Suggestions { get; set; } = new();
    
    public long ResponseTimeMs { get; set; }
}

/// <summary>
/// 自动补全建议
/// </summary>
public class AutoCompleteSuggestion
{
    public string Text { get; set; } = string.Empty;
    
    public string DisplayText { get; set; } = string.Empty;
    
    public SuggestionType Type { get; set; }
    
    public float Score { get; set; }
    
    public int Frequency { get; set; }
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 搜索索引文档
/// </summary>
public class SearchDocument
{
    public int Id { get; set; }
    
    public string Title { get; set; } = string.Empty;
    
    public string Description { get; set; } = string.Empty;
    
    public string Content { get; set; } = string.Empty;
    
    public string Category { get; set; } = string.Empty;
    
    public string Brand { get; set; } = string.Empty;
    
    public decimal Price { get; set; }
    
    public List<string> Tags { get; set; } = new();
    
    public List<float> EmbeddingVector { get; set; } = new();
    
    public Dictionary<string, object> Attributes { get; set; } = new();
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 搜索查询日志
/// </summary>
public class SearchQueryLog
{
    public int Id { get; set; }
    
    public string Query { get; set; } = string.Empty;
    
    public string? UserId { get; set; }
    
    public string SessionId { get; set; } = string.Empty;
    
    public SearchType SearchType { get; set; }
    
    public int ResultCount { get; set; }
    
    public long ResponseTimeMs { get; set; }
    
    public bool HasResults { get; set; }
    
    public List<int> ClickedResults { get; set; } = new();
    
    public Dictionary<string, object> Filters { get; set; } = new();
    
    public Dictionary<string, object> Context { get; set; } = new();
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 语义向量
/// </summary>
public class SemanticVector
{
    public int Id { get; set; }
    
    public string Text { get; set; } = string.Empty;
    
    public List<float> Vector { get; set; } = new();
    
    public VectorType Type { get; set; }
    
    public int Dimensions { get; set; }
    
    public string Model { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 搜索配置
/// </summary>
public class SearchConfiguration
{
    public int Id { get; set; }
    
    public string Name { get; set; } = string.Empty;
    
    public SearchType DefaultSearchType { get; set; } = SearchType.Semantic;
    
    public int DefaultPageSize { get; set; } = 20;
    
    public int MaxPageSize { get; set; } = 100;
    
    public bool EnableFacets { get; set; } = true;
    
    public bool EnableHighlighting { get; set; } = true;
    
    public bool EnableAutoComplete { get; set; } = true;
    
    public float SemanticThreshold { get; set; } = 0.7f;
    
    public Dictionary<string, object> Settings { get; set; } = new();
    
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 搜索类型枚举
/// </summary>
public enum SearchType
{
    Keyword = 1,
    Semantic = 2,
    Hybrid = 3,
    Fuzzy = 4,
    Exact = 5
}

/// <summary>
/// 排序方式枚举
/// </summary>
public enum SortBy
{
    Relevance = 1,
    Price = 2,
    Date = 3,
    Popularity = 4,
    Rating = 5,
    Name = 6
}

/// <summary>
/// 排序顺序枚举
/// </summary>
public enum SortOrder
{
    Ascending = 1,
    Descending = 2
}

/// <summary>
/// 分面类型枚举
/// </summary>
public enum FacetType
{
    Terms = 1,
    Range = 2,
    Date = 3,
    Numeric = 4
}

/// <summary>
/// 建议类型枚举
/// </summary>
public enum SuggestionType
{
    Query = 1,
    Product = 2,
    Category = 3,
    Brand = 4,
    Popular = 5,
    History = 6
}

/// <summary>
/// 向量类型枚举
/// </summary>
public enum VectorType
{
    Query = 1,
    Document = 2,
    Product = 3,
    Category = 4
}
