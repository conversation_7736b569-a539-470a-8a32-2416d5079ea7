using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.PricingService.Models;
using PersimmonChic.Shared.Models;
using System.Text.Json;

namespace PersimmonChic.PricingService.Services;

/// <summary>
/// 价格服务实现
/// </summary>
public class PricingService : IPricingService
{
    private readonly ILogger<PricingService> _logger;
    private readonly IRepository<ProductPrice> _productPriceRepository;
    private readonly IRepository<ChannelPrice> _channelPriceRepository;
    private readonly IRepository<UserTierPrice> _userTierPriceRepository;
    private readonly IRepository<RegionalPrice> _regionalPriceRepository;
    private readonly IRepository<PriceHistory> _priceHistoryRepository;
    private readonly IDynamicPricingService _dynamicPricingService;
    private readonly IPriceCacheService _priceCacheService;
    private readonly IPriceNotificationService _priceNotificationService;

    public PricingService(
        ILogger<PricingService> logger,
        IRepository<ProductPrice> productPriceRepository,
        IRepository<ChannelPrice> channelPriceRepository,
        IRepository<UserTierPrice> userTierPriceRepository,
        IRepository<RegionalPrice> regionalPriceRepository,
        IRepository<PriceHistory> priceHistoryRepository,
        IDynamicPricingService dynamicPricingService,
        IPriceCacheService priceCacheService,
        IPriceNotificationService priceNotificationService)
    {
        _logger = logger;
        _productPriceRepository = productPriceRepository;
        _channelPriceRepository = channelPriceRepository;
        _userTierPriceRepository = userTierPriceRepository;
        _regionalPriceRepository = regionalPriceRepository;
        _priceHistoryRepository = priceHistoryRepository;
        _dynamicPricingService = dynamicPricingService;
        _priceCacheService = priceCacheService;
        _priceNotificationService = priceNotificationService;
    }

    public async Task<ApiResponse<PriceCalculationResponse>> CalculatePriceAsync(PriceCalculationRequest request)
    {
        try
        {
            _logger.LogInformation("开始计算商品 {ProductId} 的价格", request.ProductId);

            // 1. 检查缓存
            var cacheKey = _priceCacheService.GeneratePriceCacheKey(request.ProductId, request.Context);
            var cachedPrice = await _priceCacheService.GetCachedPriceAsync(cacheKey);
            
            if (cachedPrice.HasValue)
            {
                _logger.LogDebug("从缓存获取到价格: {Price}", cachedPrice.Value);
                return ApiResponse<PriceCalculationResponse>.SuccessResult(new PriceCalculationResponse
                {
                    ProductId = request.ProductId,
                    BasePrice = cachedPrice.Value,
                    FinalPrice = cachedPrice.Value,
                    Currency = request.Currency,
                    Metadata = new Dictionary<string, object> { ["source"] = "cache" }
                });
            }

            // 2. 获取基础价格
            var basePriceResponse = await GetProductPriceAsync(request.ProductId, request.Currency);
            if (!basePriceResponse.Success || basePriceResponse.Data == null)
            {
                return ApiResponse<PriceCalculationResponse>.ErrorResult("无法获取商品基础价格");
            }

            var basePrice = basePriceResponse.Data.SalePrice ?? basePriceResponse.Data.BasePrice;
            var adjustments = new List<PriceAdjustment>();
            var finalPrice = basePrice;

            // 3. 应用渠道价格
            if (!string.IsNullOrEmpty(request.ChannelCode))
            {
                var channelPriceResponse = await GetChannelPriceAsync(request.ProductId, request.ChannelCode);
                if (channelPriceResponse.Success && channelPriceResponse.Data != null)
                {
                    var channelPrice = channelPriceResponse.Data;
                    finalPrice = channelPrice.Price;
                    
                    adjustments.Add(new PriceAdjustment
                    {
                        Type = "Channel",
                        Description = $"渠道价格 ({request.ChannelCode})",
                        Amount = channelPrice.Price - basePrice,
                        Source = "ChannelPrice"
                    });
                }
            }

            // 4. 应用用户等级价格
            if (request.UserTier.HasValue)
            {
                var tierPriceResponse = await GetUserTierPriceAsync(request.ProductId, request.UserTier.Value);
                if (tierPriceResponse.Success && tierPriceResponse.Data != null)
                {
                    var tierPrice = tierPriceResponse.Data;
                    var discount = finalPrice - tierPrice.Price;
                    
                    if (discount > 0)
                    {
                        finalPrice = tierPrice.Price;
                        adjustments.Add(new PriceAdjustment
                        {
                            Type = "UserTier",
                            Description = $"会员等级折扣 ({request.UserTier})",
                            Amount = -discount,
                            Percentage = tierPrice.DiscountPercentage ?? 0,
                            Source = "UserTierPrice"
                        });
                    }
                }
            }

            // 5. 应用地域价格
            if (!string.IsNullOrEmpty(request.RegionCode))
            {
                var regionalPriceResponse = await GetRegionalPriceAsync(request.ProductId, request.RegionCode);
                if (regionalPriceResponse.Success && regionalPriceResponse.Data != null)
                {
                    var regionalPrice = regionalPriceResponse.Data;
                    var adjustment = regionalPrice.Price - finalPrice;
                    
                    finalPrice = regionalPrice.Price;
                    adjustments.Add(new PriceAdjustment
                    {
                        Type = "Regional",
                        Description = $"地域价格调整 ({request.RegionCode})",
                        Amount = adjustment,
                        Source = "RegionalPrice"
                    });
                }
            }

            // 6. 应用动态价格规则
            var dynamicPriceResponse = await _dynamicPricingService.ApplyDynamicPricingAsync(
                request.ProductId, finalPrice, request.Context);
            
            if (dynamicPriceResponse.Success)
            {
                var dynamicAdjustment = dynamicPriceResponse.Data - finalPrice;
                if (Math.Abs(dynamicAdjustment) > 0.01m)
                {
                    finalPrice = dynamicPriceResponse.Data;
                    adjustments.Add(new PriceAdjustment
                    {
                        Type = "Dynamic",
                        Description = "动态价格调整",
                        Amount = dynamicAdjustment,
                        Source = "DynamicPricing"
                    });
                }
            }

            // 7. 应用数量折扣
            if (request.Quantity > 1)
            {
                var quantityDiscount = CalculateQuantityDiscount(finalPrice, request.Quantity);
                if (quantityDiscount > 0)
                {
                    finalPrice -= quantityDiscount;
                    adjustments.Add(new PriceAdjustment
                    {
                        Type = "Quantity",
                        Description = $"数量折扣 (数量: {request.Quantity})",
                        Amount = -quantityDiscount,
                        Source = "QuantityDiscount"
                    });
                }
            }

            // 8. 计算税费
            var taxAmount = CalculateTax(finalPrice, request.RegionCode);

            // 9. 构建响应
            var response = new PriceCalculationResponse
            {
                ProductId = request.ProductId,
                BasePrice = basePrice,
                FinalPrice = finalPrice,
                TotalDiscount = basePrice - finalPrice,
                TaxAmount = taxAmount,
                Currency = request.Currency,
                Adjustments = adjustments,
                Metadata = new Dictionary<string, object>
                {
                    ["calculation_time"] = DateTime.UtcNow,
                    ["quantity"] = request.Quantity,
                    ["user_tier"] = request.UserTier?.ToString() ?? "none",
                    ["channel"] = request.ChannelCode ?? "default",
                    ["region"] = request.RegionCode ?? "default"
                }
            };

            // 10. 缓存结果
            await _priceCacheService.SetPriceCacheAsync(cacheKey, finalPrice, TimeSpan.FromMinutes(15));

            _logger.LogInformation("商品 {ProductId} 价格计算完成: 基础价格 {BasePrice}, 最终价格 {FinalPrice}", 
                request.ProductId, basePrice, finalPrice);

            return ApiResponse<PriceCalculationResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算商品价格时发生错误");
            return ApiResponse<PriceCalculationResponse>.ErrorResult($"价格计算失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<ProductPrice?>> GetProductPriceAsync(int productId, string currency = "CNY")
    {
        try
        {
            var prices = await _productPriceRepository.FindAsync(p => 
                p.ProductId == productId && 
                p.Currency == currency && 
                p.IsActive &&
                p.EffectiveFrom <= DateTime.UtcNow &&
                (p.EffectiveTo == null || p.EffectiveTo > DateTime.UtcNow));

            var price = prices.OrderByDescending(p => p.EffectiveFrom).FirstOrDefault();
            return ApiResponse<ProductPrice?>.SuccessResult(price);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品价格时发生错误");
            return ApiResponse<ProductPrice?>.ErrorResult($"获取商品价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateProductPriceAsync(ProductPrice productPrice)
    {
        try
        {
            // 获取当前价格用于历史记录
            var currentPriceResponse = await GetProductPriceAsync(productPrice.ProductId, productPrice.Currency);
            var currentPrice = currentPriceResponse.Data?.BasePrice ?? 0;

            // 更新价格
            productPrice.UpdatedAt = DateTime.UtcNow;
            
            var existingPrices = await _productPriceRepository.FindAsync(p => 
                p.ProductId == productPrice.ProductId && p.Currency == productPrice.Currency);
            var existingPrice = existingPrices.FirstOrDefault();

            if (existingPrice != null)
            {
                existingPrice.BasePrice = productPrice.BasePrice;
                existingPrice.SalePrice = productPrice.SalePrice;
                existingPrice.EffectiveFrom = productPrice.EffectiveFrom;
                existingPrice.EffectiveTo = productPrice.EffectiveTo;
                existingPrice.IsActive = productPrice.IsActive;
                existingPrice.UpdatedAt = productPrice.UpdatedAt;
                
                await _productPriceRepository.UpdateAsync(existingPrice);
            }
            else
            {
                await _productPriceRepository.AddAsync(productPrice);
            }

            // 记录价格历史
            if (currentPrice != productPrice.BasePrice)
            {
                var priceHistory = new PriceHistory
                {
                    ProductId = productPrice.ProductId,
                    OldPrice = currentPrice,
                    NewPrice = productPrice.BasePrice,
                    Currency = productPrice.Currency,
                    ChangeReason = PriceChangeReason.Manual,
                    ChangeDescription = "价格更新",
                    ChangedAt = DateTime.UtcNow
                };

                await _priceHistoryRepository.AddAsync(priceHistory);

                // 发送价格变更通知
                await _priceNotificationService.SendPriceChangeNotificationAsync(
                    productPrice.ProductId, currentPrice, productPrice.BasePrice, "价格更新");
            }

            // 清除相关缓存
            await _priceCacheService.ClearProductPriceCacheAsync(productPrice.ProductId);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新商品价格时发生错误");
            return ApiResponse<bool>.ErrorResult($"更新商品价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> BulkUpdatePricesAsync(BulkPriceUpdateRequest request)
    {
        try
        {
            _logger.LogInformation("开始批量更新价格，更新数量: {Count}", request.Updates.Count);

            var successCount = 0;
            var failureCount = 0;

            foreach (var update in request.Updates)
            {
                try
                {
                    var productPrice = new ProductPrice
                    {
                        ProductId = update.ProductId,
                        BasePrice = update.NewPrice,
                        Currency = update.Currency,
                        EffectiveFrom = update.EffectiveFrom ?? DateTime.UtcNow,
                        IsActive = true
                    };

                    var result = await UpdateProductPriceAsync(productPrice);
                    if (result.Success)
                    {
                        successCount++;
                    }
                    else
                    {
                        failureCount++;
                        _logger.LogWarning("更新商品 {ProductId} 价格失败: {Error}", 
                            update.ProductId, result.Message);
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    _logger.LogError(ex, "更新商品 {ProductId} 价格时发生异常", update.ProductId);
                }
            }

            _logger.LogInformation("批量价格更新完成: 成功 {SuccessCount}, 失败 {FailureCount}", 
                successCount, failureCount);

            return ApiResponse<bool>.SuccessResult(failureCount == 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新价格时发生错误");
            return ApiResponse<bool>.ErrorResult($"批量更新价格失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private decimal CalculateQuantityDiscount(decimal price, int quantity)
    {
        // 简化的数量折扣计算
        if (quantity >= 100) return price * 0.15m; // 15% 折扣
        if (quantity >= 50) return price * 0.10m;  // 10% 折扣
        if (quantity >= 20) return price * 0.05m;  // 5% 折扣
        
        return 0;
    }

    private decimal CalculateTax(decimal price, string? regionCode)
    {
        // 简化的税费计算
        var taxRate = regionCode?.ToUpper() switch
        {
            "CN" => 0.13m,  // 中国增值税
            "US" => 0.08m,  // 美国销售税
            "EU" => 0.20m,  // 欧盟增值税
            _ => 0.06m       // 默认税率
        };

        return price * taxRate;
    }

    // 其他接口方法的实现...
    public async Task<ApiResponse<ChannelPrice?>> GetChannelPriceAsync(int productId, string channelCode)
    {
        try
        {
            var prices = await _channelPriceRepository.FindAsync(p => 
                p.ProductId == productId && 
                p.ChannelCode == channelCode && 
                p.IsActive &&
                p.EffectiveFrom <= DateTime.UtcNow &&
                (p.EffectiveTo == null || p.EffectiveTo > DateTime.UtcNow));

            var price = prices.OrderByDescending(p => p.EffectiveFrom).FirstOrDefault();
            return ApiResponse<ChannelPrice?>.SuccessResult(price);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取渠道价格时发生错误");
            return ApiResponse<ChannelPrice?>.ErrorResult($"获取渠道价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> SetChannelPriceAsync(ChannelPrice channelPrice)
    {
        try
        {
            await _channelPriceRepository.AddAsync(channelPrice);
            await _priceCacheService.ClearProductPriceCacheAsync(channelPrice.ProductId);
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置渠道价格时发生错误");
            return ApiResponse<bool>.ErrorResult($"设置渠道价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserTierPrice?>> GetUserTierPriceAsync(int productId, UserTier userTier)
    {
        try
        {
            var prices = await _userTierPriceRepository.FindAsync(p => 
                p.ProductId == productId && 
                p.UserTier == userTier && 
                p.IsActive &&
                p.EffectiveFrom <= DateTime.UtcNow &&
                (p.EffectiveTo == null || p.EffectiveTo > DateTime.UtcNow));

            var price = prices.OrderByDescending(p => p.EffectiveFrom).FirstOrDefault();
            return ApiResponse<UserTierPrice?>.SuccessResult(price);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户等级价格时发生错误");
            return ApiResponse<UserTierPrice?>.ErrorResult($"获取用户等级价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> SetUserTierPriceAsync(UserTierPrice userTierPrice)
    {
        try
        {
            await _userTierPriceRepository.AddAsync(userTierPrice);
            await _priceCacheService.ClearProductPriceCacheAsync(userTierPrice.ProductId);
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置用户等级价格时发生错误");
            return ApiResponse<bool>.ErrorResult($"设置用户等级价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<RegionalPrice?>> GetRegionalPriceAsync(int productId, string regionCode)
    {
        try
        {
            var prices = await _regionalPriceRepository.FindAsync(p => 
                p.ProductId == productId && 
                p.RegionCode == regionCode && 
                p.IsActive &&
                p.EffectiveFrom <= DateTime.UtcNow &&
                (p.EffectiveTo == null || p.EffectiveTo > DateTime.UtcNow));

            var price = prices.OrderByDescending(p => p.EffectiveFrom).FirstOrDefault();
            return ApiResponse<RegionalPrice?>.SuccessResult(price);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取地域价格时发生错误");
            return ApiResponse<RegionalPrice?>.ErrorResult($"获取地域价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> SetRegionalPriceAsync(RegionalPrice regionalPrice)
    {
        try
        {
            await _regionalPriceRepository.AddAsync(regionalPrice);
            await _priceCacheService.ClearProductPriceCacheAsync(regionalPrice.ProductId);
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置地域价格时发生错误");
            return ApiResponse<bool>.ErrorResult($"设置地域价格失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<PriceHistory>>> GetPriceHistoryAsync(int productId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var allHistory = await _priceHistoryRepository.GetAllAsync();
            var query = allHistory.Where(h => h.ProductId == productId);

            if (startDate.HasValue)
            {
                query = query.Where(h => h.ChangedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(h => h.ChangedAt <= endDate.Value);
            }

            var history = query.OrderByDescending(h => h.ChangedAt).Take(100).ToList();
            return ApiResponse<List<PriceHistory>>.SuccessResult(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取价格历史时发生错误");
            return ApiResponse<List<PriceHistory>>.ErrorResult($"获取价格历史失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 动态价格服务实现
/// </summary>
public class DynamicPricingService : IDynamicPricingService
{
    private readonly ILogger<DynamicPricingService> _logger;
    private readonly IRepository<DynamicPricingRule> _ruleRepository;
    private readonly IDistributedCache _cache;

    public DynamicPricingService(
        ILogger<DynamicPricingService> logger,
        IRepository<DynamicPricingRule> ruleRepository,
        IDistributedCache cache)
    {
        _logger = logger;
        _ruleRepository = ruleRepository;
        _cache = cache;
    }

    public async Task<ApiResponse<decimal>> ApplyDynamicPricingAsync(int productId, decimal basePrice, Dictionary<string, object> context)
    {
        try
        {
            var rules = await GetActiveRulesAsync(productId);
            var adjustedPrice = basePrice;

            foreach (var rule in rules.OrderBy(r => r.Priority))
            {
                if (EvaluateRuleCondition(rule, context))
                {
                    adjustedPrice = ApplyPriceAdjustment(adjustedPrice, rule);

                    // 确保价格在允许范围内
                    if (rule.MinPrice.HasValue && adjustedPrice < rule.MinPrice.Value)
                    {
                        adjustedPrice = rule.MinPrice.Value;
                    }

                    if (rule.MaxPrice.HasValue && adjustedPrice > rule.MaxPrice.Value)
                    {
                        adjustedPrice = rule.MaxPrice.Value;
                    }
                }
            }

            return ApiResponse<decimal>.SuccessResult(adjustedPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用动态价格规则时发生错误");
            return ApiResponse<decimal>.SuccessResult(basePrice); // 出错时返回原价格
        }
    }

    private async Task<List<DynamicPricingRule>> GetActiveRulesAsync(int productId)
    {
        var allRules = await _ruleRepository.FindAsync(r => r.IsActive);
        return allRules.OrderBy(r => r.Priority).ToList();
    }

    private bool EvaluateRuleCondition(DynamicPricingRule rule, Dictionary<string, object> context)
    {
        // 简化的规则评估逻辑
        return rule.Condition.ToLower() switch
        {
            "high_demand" => context.ContainsKey("demand") && Convert.ToDouble(context["demand"]) > 0.8,
            "low_stock" => context.ContainsKey("stock") && Convert.ToInt32(context["stock"]) < 10,
            "weekend" => DateTime.Now.DayOfWeek == DayOfWeek.Saturday || DateTime.Now.DayOfWeek == DayOfWeek.Sunday,
            "peak_hours" => DateTime.Now.Hour >= 18 && DateTime.Now.Hour <= 22,
            _ => false
        };
    }

    private decimal ApplyPriceAdjustment(decimal price, DynamicPricingRule rule)
    {
        return rule.AdjustmentType switch
        {
            PriceAdjustmentType.Percentage => price * (1 + rule.AdjustmentValue / 100),
            PriceAdjustmentType.FixedAmount => price + rule.AdjustmentValue,
            PriceAdjustmentType.SetPrice => rule.AdjustmentValue,
            _ => price
        };
    }

    public async Task<ApiResponse<List<DynamicPricingRule>>> GetDynamicPricingRulesAsync(int? productId = null)
    {
        try
        {
            var rules = await _ruleRepository.GetAllAsync();
            return ApiResponse<List<DynamicPricingRule>>.SuccessResult(rules.ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取动态价格规则时发生错误");
            return ApiResponse<List<DynamicPricingRule>>.ErrorResult($"获取动态价格规则失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<DynamicPricingRule>> CreateDynamicPricingRuleAsync(DynamicPricingRule rule)
    {
        try
        {
            rule.CreatedAt = DateTime.UtcNow;
            await _ruleRepository.AddAsync(rule);
            return ApiResponse<DynamicPricingRule>.SuccessResult(rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建动态价格规则时发生错误");
            return ApiResponse<DynamicPricingRule>.ErrorResult($"创建动态价格规则失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> UpdateDynamicPricingRuleAsync(DynamicPricingRule rule)
    {
        try
        {
            rule.UpdatedAt = DateTime.UtcNow;
            await _ruleRepository.UpdateAsync(rule);
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新动态价格规则时发生错误");
            return ApiResponse<bool>.ErrorResult($"更新动态价格规则失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> DeleteDynamicPricingRuleAsync(int ruleId)
    {
        try
        {
            var rule = await _ruleRepository.GetByIdAsync(ruleId);
            if (rule != null)
            {
                await _ruleRepository.DeleteAsync(rule);
            }
            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除动态价格规则时发生错误");
            return ApiResponse<bool>.ErrorResult($"删除动态价格规则失败: {ex.Message}");
        }
    }
}
