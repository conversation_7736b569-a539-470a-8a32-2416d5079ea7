using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.RiskControlService.Models;
using PersimmonChic.Shared.Models;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace PersimmonChic.RiskControlService.Services;

/// <summary>
/// 设备指纹服务实现
/// </summary>
public class DeviceFingerprintService : IDeviceFingerprintService
{
    private readonly ILogger<DeviceFingerprintService> _logger;
    private readonly IRepository<DeviceFingerprint> _deviceFingerprintRepository;
    private readonly IDistributedCache _cache;

    public DeviceFingerprintService(
        ILogger<DeviceFingerprintService> logger,
        IRepository<DeviceFingerprint> deviceFingerprintRepository,
        IDistributedCache cache)
    {
        _logger = logger;
        _deviceFingerprintRepository = deviceFingerprintRepository;
        _cache = cache;
    }

    public async Task<ApiResponse<string>> GenerateFingerprintAsync(string userAgent, Dictionary<string, object> additionalData)
    {
        try
        {
            _logger.LogDebug("开始生成设备指纹");

            // 提取设备特征
            var features = ExtractDeviceFeatures(userAgent, additionalData);
            
            // 生成指纹哈希
            var fingerprintHash = GenerateFingerprintHash(features);
            
            _logger.LogDebug("设备指纹生成完成: {FingerprintHash}", fingerprintHash);
            
            return ApiResponse<string>.SuccessResult(fingerprintHash);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成设备指纹时发生错误");
            return ApiResponse<string>.ErrorResult($"生成设备指纹失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> ValidateFingerprintAsync(int userId, string fingerprintHash)
    {
        try
        {
            _logger.LogDebug("验证用户 {UserId} 的设备指纹: {FingerprintHash}", userId, fingerprintHash);

            // 先从缓存查找
            var cacheKey = $"device_fingerprint:{fingerprintHash}";
            var cachedFingerprint = await _cache.GetStringAsync(cacheKey);
            
            DeviceFingerprint? fingerprint = null;
            
            if (!string.IsNullOrEmpty(cachedFingerprint))
            {
                fingerprint = JsonSerializer.Deserialize<DeviceFingerprint>(cachedFingerprint);
            }
            else
            {
                // 从数据库查找
                var fingerprints = await _deviceFingerprintRepository.FindAsync(f => f.FingerprintHash == fingerprintHash);
                fingerprint = fingerprints.FirstOrDefault();
                
                // 缓存结果
                if (fingerprint != null)
                {
                    await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(fingerprint),
                        new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });
                }
            }

            if (fingerprint == null)
            {
                _logger.LogInformation("设备指纹不存在: {FingerprintHash}", fingerprintHash);
                return ApiResponse<bool>.SuccessResult(false);
            }

            // 检查是否被列入黑名单
            if (fingerprint.IsBlacklisted)
            {
                _logger.LogWarning("设备指纹已被列入黑名单: {FingerprintHash}", fingerprintHash);
                return ApiResponse<bool>.SuccessResult(false);
            }

            // 检查是否属于该用户
            var isValid = fingerprint.UserId == userId || fingerprint.IsTrusted;
            
            // 更新最后访问时间和登录次数
            if (isValid)
            {
                fingerprint.LastSeen = DateTime.UtcNow;
                fingerprint.LoginCount++;
                await _deviceFingerprintRepository.UpdateAsync(fingerprint);
                
                // 更新缓存
                await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(fingerprint),
                    new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });
            }

            _logger.LogDebug("设备指纹验证结果: {IsValid} (用户: {UserId}, 指纹: {FingerprintHash})", 
                isValid, userId, fingerprintHash);

            return ApiResponse<bool>.SuccessResult(isValid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证设备指纹时发生错误");
            return ApiResponse<bool>.ErrorResult($"验证设备指纹失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> MarkDeviceAsTrustedAsync(int userId, string fingerprintHash)
    {
        try
        {
            _logger.LogInformation("标记设备为可信: 用户 {UserId}, 指纹 {FingerprintHash}", userId, fingerprintHash);

            var fingerprints = await _deviceFingerprintRepository.FindAsync(f => f.FingerprintHash == fingerprintHash);
            var fingerprint = fingerprints.FirstOrDefault();

            if (fingerprint == null)
            {
                // 创建新的设备指纹记录
                fingerprint = new DeviceFingerprint
                {
                    FingerprintHash = fingerprintHash,
                    UserId = userId,
                    FirstSeen = DateTime.UtcNow,
                    LastSeen = DateTime.UtcNow,
                    LoginCount = 1,
                    IsTrusted = true,
                    IsBlacklisted = false
                };

                await _deviceFingerprintRepository.AddAsync(fingerprint);
            }
            else
            {
                // 更新现有记录
                fingerprint.IsTrusted = true;
                fingerprint.LastSeen = DateTime.UtcNow;
                await _deviceFingerprintRepository.UpdateAsync(fingerprint);
            }

            // 更新缓存
            var cacheKey = $"device_fingerprint:{fingerprintHash}";
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(fingerprint),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });

            // 更新用户可信设备缓存
            await UpdateUserTrustedDevicesCacheAsync(userId);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标记设备为可信时发生错误");
            return ApiResponse<bool>.ErrorResult($"标记设备为可信失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<DeviceFingerprint>>> GetTrustedDevicesAsync(int userId)
    {
        try
        {
            _logger.LogDebug("获取用户 {UserId} 的可信设备列表", userId);

            // 先从缓存获取
            var cacheKey = $"user_trusted_devices:{userId}";
            var cachedDevices = await _cache.GetStringAsync(cacheKey);
            
            if (!string.IsNullOrEmpty(cachedDevices))
            {
                var devices = JsonSerializer.Deserialize<List<DeviceFingerprint>>(cachedDevices);
                return ApiResponse<List<DeviceFingerprint>>.SuccessResult(devices ?? new List<DeviceFingerprint>());
            }

            // 从数据库获取
            var trustedDevices = await _deviceFingerprintRepository.FindAsync(f => 
                f.UserId == userId && f.IsTrusted && !f.IsBlacklisted);
            
            var deviceList = trustedDevices.OrderByDescending(d => d.LastSeen).ToList();

            // 缓存结果
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(deviceList),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(30) });

            return ApiResponse<List<DeviceFingerprint>>.SuccessResult(deviceList);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可信设备列表时发生错误");
            return ApiResponse<List<DeviceFingerprint>>.ErrorResult($"获取可信设备列表失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private Dictionary<string, string> ExtractDeviceFeatures(string userAgent, Dictionary<string, object> additionalData)
    {
        var features = new Dictionary<string, string>();

        // 解析User-Agent
        if (!string.IsNullOrEmpty(userAgent))
        {
            features["user_agent"] = userAgent;
            features["browser"] = ExtractBrowser(userAgent);
            features["os"] = ExtractOperatingSystem(userAgent);
            features["device_type"] = ExtractDeviceType(userAgent);
        }

        // 处理附加数据
        foreach (var kvp in additionalData)
        {
            if (kvp.Value != null)
            {
                features[kvp.Key] = kvp.Value.ToString() ?? string.Empty;
            }
        }

        // 添加默认特征
        if (!features.ContainsKey("screen_resolution"))
        {
            features["screen_resolution"] = "unknown";
        }
        
        if (!features.ContainsKey("timezone"))
        {
            features["timezone"] = TimeZoneInfo.Local.Id;
        }
        
        if (!features.ContainsKey("language"))
        {
            features["language"] = "unknown";
        }

        return features;
    }

    private string GenerateFingerprintHash(Dictionary<string, string> features)
    {
        // 按键排序以确保一致性
        var sortedFeatures = features.OrderBy(kvp => kvp.Key).ToList();
        
        // 构建指纹字符串
        var fingerprintString = string.Join("|", sortedFeatures.Select(kvp => $"{kvp.Key}:{kvp.Value}"));
        
        // 生成SHA256哈希
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(fingerprintString));
        
        // 转换为十六进制字符串
        return Convert.ToHexString(hashBytes).ToLower();
    }

    private string ExtractBrowser(string userAgent)
    {
        if (string.IsNullOrEmpty(userAgent)) return "unknown";

        var ua = userAgent.ToLower();
        
        if (ua.Contains("chrome") && !ua.Contains("edge")) return "chrome";
        if (ua.Contains("firefox")) return "firefox";
        if (ua.Contains("safari") && !ua.Contains("chrome")) return "safari";
        if (ua.Contains("edge")) return "edge";
        if (ua.Contains("opera")) return "opera";
        if (ua.Contains("ie") || ua.Contains("trident")) return "ie";
        
        return "unknown";
    }

    private string ExtractOperatingSystem(string userAgent)
    {
        if (string.IsNullOrEmpty(userAgent)) return "unknown";

        var ua = userAgent.ToLower();
        
        if (ua.Contains("windows nt 10")) return "windows_10";
        if (ua.Contains("windows nt 6.3")) return "windows_8.1";
        if (ua.Contains("windows nt 6.2")) return "windows_8";
        if (ua.Contains("windows nt 6.1")) return "windows_7";
        if (ua.Contains("windows")) return "windows";
        if (ua.Contains("mac os x")) return "macos";
        if (ua.Contains("linux")) return "linux";
        if (ua.Contains("android")) return "android";
        if (ua.Contains("iphone") || ua.Contains("ipad")) return "ios";
        
        return "unknown";
    }

    private string ExtractDeviceType(string userAgent)
    {
        if (string.IsNullOrEmpty(userAgent)) return "unknown";

        var ua = userAgent.ToLower();
        
        if (ua.Contains("mobile") || ua.Contains("android") || ua.Contains("iphone")) return "mobile";
        if (ua.Contains("tablet") || ua.Contains("ipad")) return "tablet";
        
        return "desktop";
    }

    private async Task UpdateUserTrustedDevicesCacheAsync(int userId)
    {
        try
        {
            var cacheKey = $"user_trusted_devices:{userId}";
            await _cache.RemoveAsync(cacheKey); // 清除缓存，下次访问时重新加载
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户可信设备缓存时发生错误");
        }
    }
}
