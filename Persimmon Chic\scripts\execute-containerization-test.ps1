# Persimmon Chic 容器化测试执行脚本
# 这个脚本将执行完整的容器化测试流程

param(
    [switch]$QuickTest,
    [switch]$FullTest,
    [switch]$CleanupOnly
)

Write-Host "=== Persimmon Chic 容器化测试执行 ===" -ForegroundColor Green
Write-Host "开始时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Yellow
Write-Host ""

# 如果只是清理，直接执行清理并退出
if ($CleanupOnly) {
    Write-Host "🧹 执行清理操作..." -ForegroundColor Yellow
    & docker-compose -f docker-compose.test.yml down --volumes --remove-orphans 2>$null
    & docker system prune -f 2>$null
    Write-Host "✓ 清理完成" -ForegroundColor Green
    exit 0
}

# 测试步骤函数
function Step1-EnvironmentCheck {
    Write-Host "🔍 步骤1: 环境检查" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    # 检查Docker
    try {
        $dockerVersion = & docker --version
        Write-Host "✓ Docker版本: $dockerVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Docker未安装或未运行" -ForegroundColor Red
        return $false
    }
    
    # 检查Docker Compose
    try {
        $composeVersion = & docker compose version
        Write-Host "✓ Docker Compose版本: $composeVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Docker Compose不可用" -ForegroundColor Red
        return $false
    }
    
    # 检查Docker服务状态
    try {
        & docker info > $null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker服务运行正常" -ForegroundColor Green
        } else {
            Write-Host "✗ Docker服务未运行，请启动Docker Desktop" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ 无法连接到Docker服务" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    return $true
}

function Step2-BuildImages {
    Write-Host "🏗️ 步骤2: 构建Docker镜像" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    if ($QuickTest) {
        Write-Host "快速测试模式 - 跳过镜像构建" -ForegroundColor Yellow
        Write-Host ""
        return $true
    }
    
    # 构建测试镜像
    Write-Host "构建Gateway服务镜像..." -ForegroundColor Yellow
    $buildOutput = & docker build -f src/Gateway/Dockerfile -t persimmonchic-gateway:test . 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Gateway镜像构建成功" -ForegroundColor Green
        
        # 显示镜像信息
        $imageInfo = & docker images persimmonchic-gateway:test --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        Write-Host "镜像信息:" -ForegroundColor Gray
        Write-Host $imageInfo -ForegroundColor Gray
    } else {
        Write-Host "✗ Gateway镜像构建失败" -ForegroundColor Red
        Write-Host "构建输出:" -ForegroundColor Red
        Write-Host $buildOutput -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    return $true
}

function Step3-StartServices {
    Write-Host "🚀 步骤3: 启动容器服务" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    # 使用测试配置启动服务
    Write-Host "启动测试环境..." -ForegroundColor Yellow
    
    if ($QuickTest) {
        # 快速测试 - 只启动Redis
        & docker run -d --name test-redis -p 6380:6379 redis:7-alpine > $null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Redis容器启动成功" -ForegroundColor Green
        } else {
            Write-Host "✗ Redis容器启动失败" -ForegroundColor Red
            return $false
        }
    } else {
        # 完整测试 - 启动所有服务
        $composeOutput = & docker-compose -f docker-compose.test.yml up -d --build 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 测试环境启动成功" -ForegroundColor Green
        } else {
            Write-Host "✗ 测试环境启动失败" -ForegroundColor Red
            Write-Host "输出:" -ForegroundColor Red
            Write-Host $composeOutput -ForegroundColor Red
            return $false
        }
    }
    
    # 等待服务启动
    Write-Host "等待服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # 检查容器状态
    Write-Host "检查容器状态:" -ForegroundColor Gray
    if ($QuickTest) {
        $containerStatus = & docker ps --filter "name=test-redis" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    } else {
        $containerStatus = & docker-compose -f docker-compose.test.yml ps
    }
    Write-Host $containerStatus -ForegroundColor Gray
    
    Write-Host ""
    return $true
}

function Step4-HealthChecks {
    Write-Host "✅ 步骤4: 健康检查验证" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    # Redis健康检查
    Write-Host "检查Redis健康状态..." -ForegroundColor Yellow
    
    if ($QuickTest) {
        $redisHealth = & docker exec test-redis redis-cli ping 2>$null
    } else {
        $redisHealth = & docker exec persimmon-redis-test redis-cli ping 2>$null
    }
    
    if ($redisHealth -eq "PONG") {
        Write-Host "✓ Redis健康检查通过" -ForegroundColor Green
    } else {
        Write-Host "✗ Redis健康检查失败" -ForegroundColor Red
        return $false
    }
    
    if (-not $QuickTest) {
        # 检查其他服务
        Write-Host "检查Gateway健康状态..." -ForegroundColor Yellow
        try {
            $gatewayHealth = Invoke-RestMethod -Uri "http://localhost:5100/health" -TimeoutSec 10 -ErrorAction Stop
            Write-Host "✓ Gateway健康检查通过" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ Gateway健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
        
        Write-Host "检查UserService健康状态..." -ForegroundColor Yellow
        try {
            $userServiceHealth = Invoke-RestMethod -Uri "http://localhost:5101/health" -TimeoutSec 10 -ErrorAction Stop
            Write-Host "✓ UserService健康检查通过" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ UserService健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host ""
    return $true
}

function Step5-FunctionalTests {
    Write-Host "🔧 步骤5: 功能测试" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    # Redis功能测试
    Write-Host "测试Redis数据操作..." -ForegroundColor Yellow
    
    $containerName = if ($QuickTest) { "test-redis" } else { "persimmon-redis-test" }
    
    # 写入测试数据
    & docker exec $containerName redis-cli set test:key "container-test-value" > $null
    $testValue = & docker exec $containerName redis-cli get test:key 2>$null
    
    if ($testValue -eq "container-test-value") {
        Write-Host "✓ Redis数据读写正常" -ForegroundColor Green
    } else {
        Write-Host "✗ Redis数据读写失败" -ForegroundColor Red
        return $false
    }
    
    if (-not $QuickTest) {
        # API功能测试
        Write-Host "测试API端点..." -ForegroundColor Yellow
        
        try {
            # 测试Gateway路由
            $gatewayResponse = Invoke-RestMethod -Uri "http://localhost:5100/api/health" -TimeoutSec 10 -ErrorAction Stop
            Write-Host "✓ Gateway API响应正常" -ForegroundColor Green
            
            # 测试UserService API
            $userServiceResponse = Invoke-RestMethod -Uri "http://localhost:5101/api/users/health" -TimeoutSec 10 -ErrorAction Stop
            Write-Host "✓ UserService API响应正常" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ API测试失败: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host ""
    return $true
}

function Step6-FailureRecovery {
    Write-Host "🛠️ 步骤6: 故障恢复测试" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    $containerName = if ($QuickTest) { "test-redis" } else { "persimmon-redis-test" }
    
    # 模拟容器故障
    Write-Host "模拟容器故障..." -ForegroundColor Yellow
    & docker kill $containerName > $null
    
    # 检查容器状态
    $killedStatus = & docker ps -a --filter "name=$containerName" --format "{{.Status}}"
    Write-Host "容器状态: $killedStatus" -ForegroundColor Gray
    
    # 重启容器
    Write-Host "重启容器..." -ForegroundColor Yellow
    & docker start $containerName > $null
    Start-Sleep -Seconds 5
    
    # 验证恢复
    $recoveredStatus = & docker ps --filter "name=$containerName" --format "{{.Status}}"
    if ($recoveredStatus -like "*Up*") {
        Write-Host "✓ 容器恢复成功: $recoveredStatus" -ForegroundColor Green
        
        # 验证数据持久性
        $persistedValue = & docker exec $containerName redis-cli get test:key 2>$null
        if ($persistedValue -eq "container-test-value") {
            Write-Host "✓ 数据持久性验证通过" -ForegroundColor Green
        } else {
            Write-Host "✗ 数据持久性验证失败" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "✗ 容器恢复失败" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    return $true
}

function Cleanup-TestEnvironment {
    Write-Host "🧹 清理测试环境..." -ForegroundColor Yellow
    
    if ($QuickTest) {
        & docker stop test-redis > $null 2>&1
        & docker rm test-redis > $null 2>&1
    } else {
        & docker-compose -f docker-compose.test.yml down --volumes > $null 2>&1
    }
    
    # 清理测试镜像
    & docker rmi persimmonchic-gateway:test > $null 2>&1
    
    Write-Host "✓ 清理完成" -ForegroundColor Green
}

# 主测试流程
$testSteps = @(
    @{ Name = "环境检查"; Function = { Step1-EnvironmentCheck } },
    @{ Name = "构建镜像"; Function = { Step2-BuildImages } },
    @{ Name = "启动服务"; Function = { Step3-StartServices } },
    @{ Name = "健康检查"; Function = { Step4-HealthChecks } },
    @{ Name = "功能测试"; Function = { Step5-FunctionalTests } },
    @{ Name = "故障恢复"; Function = { Step6-FailureRecovery } }
)

$passedSteps = 0
$totalSteps = $testSteps.Count

try {
    foreach ($step in $testSteps) {
        $result = & $step.Function
        if ($result) {
            $passedSteps++
        } else {
            Write-Host "❌ 测试步骤失败: $($step.Name)" -ForegroundColor Red
            break
        }
    }
}
finally {
    # 清理测试环境
    Cleanup-TestEnvironment
}

# 显示测试结果
Write-Host ""
Write-Host "📊 测试结果总结" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

$successRate = [math]::Round(($passedSteps / $totalSteps) * 100, 1)
$testMode = if ($QuickTest) { "快速测试" } else { "完整测试" }

Write-Host "测试模式: $testMode" -ForegroundColor Yellow
Write-Host "通过步骤: $passedSteps / $totalSteps" -ForegroundColor $(if ($passedSteps -eq $totalSteps) { "Green" } else { "Red" })
Write-Host "成功率: $successRate%" -ForegroundColor $(if ($passedSteps -eq $totalSteps) { "Green" } else { "Red" })
Write-Host "结束时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Yellow

# 提供下一步建议
Write-Host ""
Write-Host "🎯 下一步建议:" -ForegroundColor Cyan
if ($passedSteps -eq $totalSteps) {
    Write-Host "✅ 容器化测试全部通过！可以进行下一步操作:" -ForegroundColor Green
    Write-Host "  1. 构建所有服务: .\scripts\docker-build.ps1 -Service all" -ForegroundColor White
    Write-Host "  2. 启动完整环境: .\scripts\docker-run.ps1 -Environment development -Action up -Detach" -ForegroundColor White
    Write-Host "  3. 运行服务测试: .\scripts\test-services.ps1" -ForegroundColor White
    Write-Host "  4. 准备K8s部署: .\scripts\k8s-deploy.ps1" -ForegroundColor White
} else {
    Write-Host "❌ 部分测试失败，请检查:" -ForegroundColor Red
    Write-Host "  1. Docker Desktop是否正常运行" -ForegroundColor White
    Write-Host "  2. 系统资源是否充足（推荐8GB+ RAM）" -ForegroundColor White
    Write-Host "  3. 网络连接是否正常" -ForegroundColor White
    Write-Host "  4. 重新运行测试: .\scripts\execute-containerization-test.ps1" -ForegroundColor White
}

Write-Host ""
Write-Host "使用说明:" -ForegroundColor Cyan
Write-Host "  快速测试: .\scripts\execute-containerization-test.ps1 -QuickTest" -ForegroundColor White
Write-Host "  完整测试: .\scripts\execute-containerization-test.ps1 -FullTest" -ForegroundColor White
Write-Host "  仅清理: .\scripts\execute-containerization-test.ps1 -CleanupOnly" -ForegroundColor White

# 退出代码
exit $(if ($passedSteps -eq $totalSteps) { 0 } else { 1 })
