apiVersion: v1
kind: ConfigMap
metadata:
  name: persimmon-chic-config
  namespace: persimmon-chic
  labels:
    app.kubernetes.io/name: persimmon-chic
    app.kubernetes.io/component: config
data:
  # 应用配置
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:80"
  
  # 服务发现配置
  SERVICES__USERSERVICE__BASEURL: "http://user-service:80"
  SERVICES__RISKCONTROLSERVICE__BASEURL: "http://risk-control-service:80"
  SERVICES__PRICINGSERVICE__BASEURL: "http://pricing-service:80"
  SERVICES__RECOMMENDATIONSERVICE__BASEURL: "http://recommendation-service:80"
  SERVICES__SEARCHSERVICE__BASEURL: "http://search-service:80"
  SERVICES__CUSTOMERSERVICEBOT__BASEURL: "http://customer-service-bot:80"
  
  # Redis配置
  CONNECTIONSTRINGS__REDIS: "redis-service:6379"
  
  # 数据库配置
  CONNECTIONSTRINGS__DEFAULTCONNECTION: "Server=sqlserver-service;Database=PersimmonChic;User Id=sa;Password=$(SQL_PASSWORD);TrustServerCertificate=true"
  
  # JWT配置
  JWTSETTINGS__ISSUER: "PersimmonChic"
  JWTSETTINGS__AUDIENCE: "PersimmonChic-Users"
  JWTSETTINGS__EXPIRATIONHOURS: "24"
  
  # 日志配置
  LOGGING__LOGLEVEL__DEFAULT: "Information"
  LOGGING__LOGLEVEL__MICROSOFT: "Warning"
  LOGGING__LOGLEVEL__MICROSOFT_HOSTING_LIFETIME: "Information"
  
  # 健康检查配置
  HEALTHCHECKS__UI__ENABLE: "true"
  HEALTHCHECKS__UI__PATH: "/health-ui"
  
  # 监控配置
  MONITORING__ENABLE: "true"
  MONITORING__ENDPOINT: "/metrics"
  
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: persimmon-chic-dev-config
  namespace: persimmon-chic-dev
  labels:
    app.kubernetes.io/name: persimmon-chic
    app.kubernetes.io/component: config
    environment: development
data:
  # 开发环境配置
  ASPNETCORE_ENVIRONMENT: "Development"
  ASPNETCORE_URLS: "http://+:80"
  
  # 服务发现配置
  SERVICES__USERSERVICE__BASEURL: "http://user-service:80"
  SERVICES__RISKCONTROLSERVICE__BASEURL: "http://risk-control-service:80"
  SERVICES__PRICINGSERVICE__BASEURL: "http://pricing-service:80"
  SERVICES__RECOMMENDATIONSERVICE__BASEURL: "http://recommendation-service:80"
  SERVICES__SEARCHSERVICE__BASEURL: "http://search-service:80"
  SERVICES__CUSTOMERSERVICEBOT__BASEURL: "http://customer-service-bot:80"
  
  # Redis配置
  CONNECTIONSTRINGS__REDIS: "redis-service:6379"
  
  # 数据库配置
  CONNECTIONSTRINGS__DEFAULTCONNECTION: "Server=sqlserver-service;Database=PersimmonChic_Dev;User Id=sa;Password=$(SQL_PASSWORD);TrustServerCertificate=true"
  
  # JWT配置
  JWTSETTINGS__ISSUER: "PersimmonChic-Dev"
  JWTSETTINGS__AUDIENCE: "PersimmonChic-Dev-Users"
  JWTSETTINGS__EXPIRATIONHOURS: "24"
  
  # 日志配置
  LOGGING__LOGLEVEL__DEFAULT: "Debug"
  LOGGING__LOGLEVEL__MICROSOFT: "Information"
  LOGGING__LOGLEVEL__PERSIMMONCHIC: "Debug"
