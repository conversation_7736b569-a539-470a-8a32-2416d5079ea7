using PersimmonChic.Shared.Models;

namespace PersimmonChic.Gateway.Services;

/// <summary>
/// 网关服务接口
/// </summary>
public interface IGatewayService
{
    /// <summary>
    /// 转发请求到指定服务
    /// </summary>
    /// <typeparam name="T">响应类型</typeparam>
    /// <param name="serviceName">服务名称</param>
    /// <param name="method">HTTP方法</param>
    /// <param name="endpoint">端点</param>
    /// <param name="requestBody">请求体</param>
    /// <param name="headers">请求头</param>
    /// <returns>响应结果</returns>
    Task<ApiResponse<T>> ForwardRequestAsync<T>(
        string serviceName,
        string method,
        string endpoint,
        object? requestBody = null,
        Dictionary<string, string>? headers = null);

    /// <summary>
    /// 检查服务健康状态
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <returns>健康状态</returns>
    Task<ApiResponse<HealthStatus>> CheckServiceHealthAsync(string serviceName);

    /// <summary>
    /// 获取所有服务状态
    /// </summary>
    /// <returns>服务状态列表</returns>
    Task<ApiResponse<List<HealthStatus>>> GetAllServiceStatusAsync();

    /// <summary>
    /// 注册服务实例
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="address">服务地址</param>
    /// <param name="port">服务端口</param>
    /// <param name="version">服务版本</param>
    /// <returns>注册结果</returns>
    Task<ApiResponse<bool>> RegisterServiceAsync(string serviceName, string address, int port, string version = "1.0.0");

    /// <summary>
    /// 注销服务实例
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="instanceId">实例ID</param>
    /// <returns>注销结果</returns>
    Task<ApiResponse<bool>> UnregisterServiceAsync(string serviceName, string instanceId);

    /// <summary>
    /// 更新服务健康状态
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="instanceId">实例ID</param>
    /// <param name="isHealthy">是否健康</param>
    /// <returns>更新结果</returns>
    Task<ApiResponse<bool>> UpdateServiceHealthAsync(string serviceName, string instanceId, bool isHealthy);
}
