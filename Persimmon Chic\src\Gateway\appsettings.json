{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "Per<PERSON><PERSON>on<PERSON><PERSON>_Super_Secret_Key_2024_Demo_Application", "Issuer": "PersimmonChic", "Audience": "PersimmonChic-Users", "ExpirationHours": 24}, "Services": {"UserService": {"BaseUrl": "http://localhost:5001", "HealthEndpoint": "/health"}, "ProductService": {"BaseUrl": "http://localhost:5002", "HealthEndpoint": "/health"}, "OrderService": {"BaseUrl": "http://localhost:5003", "HealthEndpoint": "/health"}}, "RateLimit": {"MaxRequestsPerMinute": 100, "EnableRateLimit": true}, "Cors": {"AllowedOrigins": ["*"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"]}}