using System.ComponentModel.DataAnnotations;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.PricingService.Models;

/// <summary>
/// 价格策略
/// </summary>
public class PricingStrategy
{
    public int Id { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    public PricingType Type { get; set; }
    
    public Dictionary<string, object> Parameters { get; set; } = new();
    
    public bool IsActive { get; set; } = true;
    
    public int Priority { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
}

/// <summary>
/// 商品价格
/// </summary>
public class ProductPrice
{
    public int Id { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public decimal BasePrice { get; set; }
    
    public decimal? SalePrice { get; set; }
    
    [Required]
    public string Currency { get; set; } = "CNY";
    
    public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;
    
    public DateTime? EffectiveTo { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 渠道价格
/// </summary>
public class ChannelPrice
{
    public int Id { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public string ChannelCode { get; set; } = string.Empty;
    
    [Required]
    public decimal Price { get; set; }
    
    public decimal? DiscountPercentage { get; set; }
    
    public decimal? DiscountAmount { get; set; }
    
    [Required]
    public string Currency { get; set; } = "CNY";
    
    public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;
    
    public DateTime? EffectiveTo { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 用户等级价格
/// </summary>
public class UserTierPrice
{
    public int Id { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public UserTier UserTier { get; set; }
    
    [Required]
    public decimal Price { get; set; }
    
    public decimal? DiscountPercentage { get; set; }
    
    [Required]
    public string Currency { get; set; } = "CNY";
    
    public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;
    
    public DateTime? EffectiveTo { get; set; }
    
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 地域价格
/// </summary>
public class RegionalPrice
{
    public int Id { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public string RegionCode { get; set; } = string.Empty;
    
    [Required]
    public decimal Price { get; set; }
    
    public decimal? TaxRate { get; set; }
    
    [Required]
    public string Currency { get; set; } = "CNY";
    
    public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;
    
    public DateTime? EffectiveTo { get; set; }
    
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 动态价格规则
/// </summary>
public class DynamicPricingRule
{
    public int Id { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    public string Condition { get; set; } = string.Empty;
    
    [Required]
    public PriceAdjustmentType AdjustmentType { get; set; }
    
    public decimal AdjustmentValue { get; set; }
    
    public decimal? MinPrice { get; set; }
    
    public decimal? MaxPrice { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public int Priority { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 价格历史记录
/// </summary>
public class PriceHistory
{
    public int Id { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public decimal OldPrice { get; set; }
    
    [Required]
    public decimal NewPrice { get; set; }
    
    [Required]
    public string Currency { get; set; } = "CNY";
    
    [Required]
    public PriceChangeReason ChangeReason { get; set; }
    
    public string? ChangeDescription { get; set; }
    
    public string? ChangedBy { get; set; }
    
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 价格计算请求
/// </summary>
public class PriceCalculationRequest
{
    [Required]
    public int ProductId { get; set; }
    
    public int? UserId { get; set; }
    
    public UserTier? UserTier { get; set; }
    
    public string? ChannelCode { get; set; }
    
    public string? RegionCode { get; set; }
    
    public int Quantity { get; set; } = 1;
    
    public string Currency { get; set; } = "CNY";
    
    public DateTime? CalculationTime { get; set; }
    
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 价格计算响应
/// </summary>
public class PriceCalculationResponse
{
    public int ProductId { get; set; }
    
    public decimal BasePrice { get; set; }
    
    public decimal FinalPrice { get; set; }
    
    public decimal TotalDiscount { get; set; }
    
    public decimal TaxAmount { get; set; }
    
    public string Currency { get; set; } = "CNY";
    
    public List<PriceAdjustment> Adjustments { get; set; } = new();
    
    public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;
    
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 价格调整详情
/// </summary>
public class PriceAdjustment
{
    public string Type { get; set; } = string.Empty;
    
    public string Description { get; set; } = string.Empty;
    
    public decimal Amount { get; set; }
    
    public decimal Percentage { get; set; }
    
    public string Source { get; set; } = string.Empty;
}

/// <summary>
/// 批量价格更新请求
/// </summary>
public class BulkPriceUpdateRequest
{
    [Required]
    public List<ProductPriceUpdate> Updates { get; set; } = new();
    
    public string? Reason { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    public DateTime? EffectiveFrom { get; set; }
}

/// <summary>
/// 商品价格更新
/// </summary>
public class ProductPriceUpdate
{
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    public decimal NewPrice { get; set; }
    
    public string Currency { get; set; } = "CNY";
    
    public DateTime? EffectiveFrom { get; set; }
}

/// <summary>
/// 价格类型枚举
/// </summary>
public enum PricingType
{
    Fixed = 1,
    Dynamic = 2,
    Tiered = 3,
    Volume = 4,
    Promotional = 5
}

/// <summary>
/// 用户等级枚举
/// </summary>
public enum UserTier
{
    Bronze = 1,
    Silver = 2,
    Gold = 3,
    Platinum = 4,
    Diamond = 5
}

/// <summary>
/// 价格调整类型枚举
/// </summary>
public enum PriceAdjustmentType
{
    Percentage = 1,
    FixedAmount = 2,
    SetPrice = 3
}

/// <summary>
/// 价格变更原因枚举
/// </summary>
public enum PriceChangeReason
{
    Manual = 1,
    Automatic = 2,
    Promotion = 3,
    CostChange = 4,
    Competition = 5,
    Demand = 6,
    Seasonal = 7
}
