using PersimmonChic.RiskControlService.Models;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.RiskControlService.Services;

/// <summary>
/// 风控服务接口
/// </summary>
public interface IRiskControlService
{
    /// <summary>
    /// 评估风险决策
    /// </summary>
    /// <param name="request">风控决策请求</param>
    /// <returns>风控决策响应</returns>
    Task<ApiResponse<RiskDecisionResponse>> EvaluateRiskAsync(RiskDecisionRequest request);
    
    /// <summary>
    /// 记录用户行为事件
    /// </summary>
    /// <param name="behaviorEvent">用户行为事件</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> RecordBehaviorEventAsync(UserBehaviorEvent behaviorEvent);
    
    /// <summary>
    /// 获取用户风险档案
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户风险档案</returns>
    Task<ApiResponse<UserRiskProfile>> GetUserRiskProfileAsync(int userId);
    
    /// <summary>
    /// 更新用户风险档案
    /// </summary>
    /// <param name="profile">用户风险档案</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdateUserRiskProfileAsync(UserRiskProfile profile);
    
    /// <summary>
    /// 检测异常登录
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="userAgent">用户代理</param>
    /// <param name="deviceFingerprint">设备指纹</param>
    /// <returns>异常检测结果</returns>
    Task<ApiResponse<List<AnomalousLoginRecord>>> DetectAnomalousLoginAsync(
        int userId, string ipAddress, string userAgent, string? deviceFingerprint = null);
    
    /// <summary>
    /// 获取设备指纹信息
    /// </summary>
    /// <param name="fingerprintHash">指纹哈希</param>
    /// <returns>设备指纹信息</returns>
    Task<ApiResponse<DeviceFingerprint?>> GetDeviceFingerprintAsync(string fingerprintHash);
    
    /// <summary>
    /// 更新设备指纹信息
    /// </summary>
    /// <param name="fingerprint">设备指纹</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdateDeviceFingerprintAsync(DeviceFingerprint fingerprint);
    
    /// <summary>
    /// 获取风险规则列表
    /// </summary>
    /// <param name="eventType">事件类型（可选）</param>
    /// <returns>风险规则列表</returns>
    Task<ApiResponse<List<RiskRule>>> GetRiskRulesAsync(string? eventType = null);
    
    /// <summary>
    /// 创建风险规则
    /// </summary>
    /// <param name="rule">风险规则</param>
    /// <returns>创建的规则</returns>
    Task<ApiResponse<RiskRule>> CreateRiskRuleAsync(RiskRule rule);
    
    /// <summary>
    /// 更新风险规则
    /// </summary>
    /// <param name="rule">风险规则</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdateRiskRuleAsync(RiskRule rule);
    
    /// <summary>
    /// 删除风险规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> DeleteRiskRuleAsync(int ruleId);
    
    /// <summary>
    /// 获取实时风险指标
    /// </summary>
    /// <param name="metricNames">指标名称列表</param>
    /// <param name="timeRange">时间范围（分钟）</param>
    /// <returns>风险指标列表</returns>
    Task<ApiResponse<List<RiskMetrics>>> GetRiskMetricsAsync(List<string> metricNames, int timeRange = 60);
    
    /// <summary>
    /// 锁定用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="lockDurationMinutes">锁定时长（分钟）</param>
    /// <param name="reason">锁定原因</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> LockUserAccountAsync(int userId, int lockDurationMinutes, string reason);
    
    /// <summary>
    /// 解锁用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="reason">解锁原因</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UnlockUserAccountAsync(int userId, string reason);
    
    /// <summary>
    /// 获取异常登录记录
    /// </summary>
    /// <param name="userId">用户ID（可选）</param>
    /// <param name="startTime">开始时间（可选）</param>
    /// <param name="endTime">结束时间（可选）</param>
    /// <returns>异常登录记录列表</returns>
    Task<ApiResponse<List<AnomalousLoginRecord>>> GetAnomalousLoginRecordsAsync(
        int? userId = null, DateTime? startTime = null, DateTime? endTime = null);
}

/// <summary>
/// 实时风险监控服务接口
/// </summary>
public interface IRealTimeRiskMonitorService
{
    /// <summary>
    /// 启动实时监控
    /// </summary>
    Task StartMonitoringAsync();
    
    /// <summary>
    /// 停止实时监控
    /// </summary>
    Task StopMonitoringAsync();
    
    /// <summary>
    /// 处理实时事件
    /// </summary>
    /// <param name="behaviorEvent">用户行为事件</param>
    Task ProcessRealTimeEventAsync(UserBehaviorEvent behaviorEvent);
    
    /// <summary>
    /// 发送风险告警
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="riskLevel">风险等级</param>
    /// <param name="message">告警消息</param>
    Task SendRiskAlertAsync(int userId, RiskLevel riskLevel, string message);
}

/// <summary>
/// 机器学习风险评估服务接口
/// </summary>
public interface IMLRiskAssessmentService
{
    /// <summary>
    /// 训练风险评估模型
    /// </summary>
    /// <param name="trainingData">训练数据</param>
    Task<ApiResponse<bool>> TrainModelAsync(List<UserBehaviorEvent> trainingData);
    
    /// <summary>
    /// 预测风险分数
    /// </summary>
    /// <param name="behaviorEvent">用户行为事件</param>
    /// <returns>风险分数</returns>
    Task<ApiResponse<double>> PredictRiskScoreAsync(UserBehaviorEvent behaviorEvent);
    
    /// <summary>
    /// 获取模型性能指标
    /// </summary>
    /// <returns>模型性能指标</returns>
    Task<ApiResponse<Dictionary<string, double>>> GetModelMetricsAsync();
    
    /// <summary>
    /// 更新模型
    /// </summary>
    /// <param name="newData">新的训练数据</param>
    Task<ApiResponse<bool>> UpdateModelAsync(List<UserBehaviorEvent> newData);
}

/// <summary>
/// 设备指纹服务接口
/// </summary>
public interface IDeviceFingerprintService
{
    /// <summary>
    /// 生成设备指纹
    /// </summary>
    /// <param name="userAgent">用户代理</param>
    /// <param name="additionalData">附加数据</param>
    /// <returns>设备指纹哈希</returns>
    Task<ApiResponse<string>> GenerateFingerprintAsync(string userAgent, Dictionary<string, object> additionalData);
    
    /// <summary>
    /// 验证设备指纹
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="fingerprintHash">指纹哈希</param>
    /// <returns>验证结果</returns>
    Task<ApiResponse<bool>> ValidateFingerprintAsync(int userId, string fingerprintHash);
    
    /// <summary>
    /// 标记设备为可信
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="fingerprintHash">指纹哈希</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> MarkDeviceAsTrustedAsync(int userId, string fingerprintHash);
    
    /// <summary>
    /// 获取用户的可信设备列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>可信设备列表</returns>
    Task<ApiResponse<List<DeviceFingerprint>>> GetTrustedDevicesAsync(int userId);
}
