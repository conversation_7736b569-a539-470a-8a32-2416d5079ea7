# Persimmon Chic 服务 Dockerfile 模板
# 基于 .NET 9.0 的多阶段构建

# 构建阶段
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制项目文件并还原依赖
COPY ["src/Shared/Models/PersimmonChic.Shared.Models.csproj", "src/Shared/Models/"]
COPY ["src/Shared/Contracts/PersimmonChic.Shared.Contracts.csproj", "src/Shared/Contracts/"]
COPY ["src/Shared/Common/PersimmonChic.Shared.Common.csproj", "src/Shared/Common/"]
COPY ["src/Infrastructure/DataAccess/PersimmonChic.Infrastructure.DataAccess.csproj", "src/Infrastructure/DataAccess/"]

# 复制服务项目文件 (需要根据具体服务调整)
COPY ["src/Services/{SERVICE_NAME}/{PROJECT_NAME}.csproj", "src/Services/{SERVICE_NAME}/"]

# 还原 NuGet 包
RUN dotnet restore "src/Services/{SERVICE_NAME}/{PROJECT_NAME}.csproj"

# 复制所有源代码
COPY . .

# 构建项目
WORKDIR "/src/src/Services/{SERVICE_NAME}"
RUN dotnet build "{PROJECT_NAME}.csproj" -c Release -o /app/build

# 发布阶段
FROM build AS publish
RUN dotnet publish "{PROJECT_NAME}.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 运行时阶段
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app

# 创建非root用户
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# 复制发布的文件
COPY --from=publish /app/publish .

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

# 暴露端口
EXPOSE 80
EXPOSE 443

# 设置环境变量
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# 启动应用
ENTRYPOINT ["dotnet", "{PROJECT_NAME}.dll"]
