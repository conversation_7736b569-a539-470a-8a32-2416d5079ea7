# Persimmon Chic Demo Startup Script

Write-Host "Starting Persimmon Chic Microservices Demo..." -ForegroundColor Green
Write-Host ""

# Check .NET SDK
try {
    $dotnetVersion = & "C:\Program Files\dotnet\dotnet.exe" --version
    Write-Host "✓ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET SDK not found, please install .NET 9.0 SDK first" -ForegroundColor Red
    exit 1
}

# Set working directory
$rootPath = Split-Path -Parent $PSScriptRoot
Set-Location $rootPath

Write-Host "Working Directory: $rootPath" -ForegroundColor Cyan
Write-Host ""

# Build projects
Write-Host "Building projects..." -ForegroundColor Yellow

$projectsToBuild = @(
    "src/Shared/Models/PersimmonChic.Shared.Models.csproj",
    "src/Shared/Contracts/PersimmonChic.Shared.Contracts.csproj", 
    "src/Shared/Common/PersimmonChic.Shared.Common.csproj",
    "src/Infrastructure/DataAccess/PersimmonChic.Infrastructure.DataAccess.csproj",
    "src/Gateway/PersimmonChic.Gateway.csproj",
    "src/Services/UserService/PersimmonChic.UserService.csproj"
)

foreach ($project in $projectsToBuild) {
    Write-Host "  Building $project..." -ForegroundColor Gray
    try {
        & "C:\Program Files\dotnet\dotnet.exe" build $project --configuration Debug --verbosity quiet
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed"
        }
    } catch {
        Write-Host "✗ Failed to build $project" -ForegroundColor Red
        Write-Host "Please check project dependencies and code errors" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "✓ All projects built successfully" -ForegroundColor Green
Write-Host ""

# Start services
Write-Host "Starting services..." -ForegroundColor Yellow

# Start API Gateway
Write-Host "Starting API Gateway (port 5000)..." -ForegroundColor Magenta
$gatewayJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Gateway/PersimmonChic.Gateway.csproj --configuration Debug
}

Start-Sleep -Seconds 3

# Start User Service
Write-Host "Starting User Service (port 5001)..." -ForegroundColor Blue
$userServiceJob = Start-Job -ScriptBlock {
    Set-Location $using:rootPath
    & "C:\Program Files\dotnet\dotnet.exe" run --project src/Services/UserService/PersimmonChic.UserService.csproj --configuration Debug
}

Write-Host ""
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# Check service status
Write-Host ""
Write-Host "Checking service status..." -ForegroundColor Cyan

$services = @(
    @{ Name = "API Gateway"; Url = "http://localhost:5000/health" },
    @{ Name = "User Service"; Url = "http://localhost:5001/health" }
)

foreach ($service in $services) {
    try {
        $response = Invoke-RestMethod -Uri $service.Url -Method Get -TimeoutSec 5
        Write-Host "✓ $($service.Name): Running" -ForegroundColor Green
    } catch {
        Write-Host "! $($service.Name): May not be fully started ($($service.Url))" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Persimmon Chic Microservices Demo Started!" -ForegroundColor Green
Write-Host ""
Write-Host "Service Access URLs:" -ForegroundColor Cyan
Write-Host "   • API Gateway: http://localhost:5000" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5000" -ForegroundColor Gray
Write-Host "     - Service List: http://localhost:5000/services" -ForegroundColor Gray
Write-Host "   • User Service: http://localhost:5001" -ForegroundColor White
Write-Host "     - Swagger UI: http://localhost:5001" -ForegroundColor Gray

Write-Host ""
Write-Host "Test APIs:" -ForegroundColor Yellow
Write-Host "   • Login Test: POST http://localhost:5000/api/users/login" -ForegroundColor Gray
Write-Host "     Username: admin, Password: 123456" -ForegroundColor Gray
Write-Host "   • Get User: GET http://localhost:5000/api/users/1" -ForegroundColor Gray

Write-Host ""
Write-Host "Tips:" -ForegroundColor Yellow
Write-Host "   • Press Ctrl+C to stop all services" -ForegroundColor Gray
Write-Host "   • Check PowerShell jobs for real-time logs" -ForegroundColor Gray
Write-Host "   • API documentation available via Swagger UI" -ForegroundColor Gray

Write-Host ""
Write-Host "Opening browser to access API Gateway..." -ForegroundColor Cyan

# Try to open browser
try {
    Start-Process "http://localhost:5000"
} catch {
    Write-Host "Cannot auto-open browser, please visit manually: http://localhost:5000" -ForegroundColor Yellow
}

# Wait for user input to stop services
Write-Host ""
Write-Host "Press any key to stop all services..." -ForegroundColor Red
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop all jobs
Write-Host ""
Write-Host "Stopping all services..." -ForegroundColor Red

Stop-Job -Job $gatewayJob -ErrorAction SilentlyContinue
Remove-Job -Job $gatewayJob -ErrorAction SilentlyContinue
Write-Host "✓ Stopped API Gateway" -ForegroundColor Green

Stop-Job -Job $userServiceJob -ErrorAction SilentlyContinue  
Remove-Job -Job $userServiceJob -ErrorAction SilentlyContinue
Write-Host "✓ Stopped User Service" -ForegroundColor Green

Write-Host ""
Write-Host "Persimmon Chic Microservices Demo Stopped" -ForegroundColor Green
Write-Host ""
Write-Host "For more information, please check README.md file" -ForegroundColor Cyan
