using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.SearchService.Models;
using PersimmonChic.SearchService.Services;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllers();

// 配置Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Persimmon Chic Search Service",
        Version = "v1",
        Description = "语义搜索服务API - 提供智能搜索、自动补全和搜索分析功能",
        Contact = new OpenApiContact
        {
            Name = "Persimmon Chic Team",
            Email = "<EMAIL>"
        }
    });

    // 添加JWT认证配置
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // 包含XML注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// 配置JWT认证
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5000", "http://localhost:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// 配置Redis缓存
var redisConnectionString = builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379";
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConnectionString;
    options.InstanceName = "PersimmonChic.Search";
});

// 注册数据访问层
builder.Services.AddScoped<IRepository<SearchDocument>, InMemoryRepository<SearchDocument>>();
builder.Services.AddScoped<IRepository<SearchQueryLog>, InMemoryRepository<SearchQueryLog>>();
builder.Services.AddScoped<IRepository<SemanticVector>, InMemoryRepository<SemanticVector>>();
builder.Services.AddScoped<IRepository<SearchConfiguration>, InMemoryRepository<SearchConfiguration>>();

// 注册搜索引擎服务
builder.Services.AddScoped<ISemanticSearchEngine, SemanticSearchEngine>();
builder.Services.AddScoped<IKeywordSearchEngine, KeywordSearchEngine>();
builder.Services.AddScoped<IHybridSearchEngine, HybridSearchEngine>();
builder.Services.AddScoped<ISearchAnalyticsService, SearchAnalyticsService>();
builder.Services.AddScoped<ISearchCacheService, SearchCacheService>();

// 注册主要业务服务
builder.Services.AddScoped<ISearchService, PersimmonChic.SearchService.Services.SearchService>();

// 注册HTTP客户端工厂
builder.Services.AddHttpClient();

// 配置日志
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    logging.AddDebug();
    
    if (builder.Environment.IsDevelopment())
    {
        logging.SetMinimumLevel(LogLevel.Debug);
    }
    else
    {
        logging.SetMinimumLevel(LogLevel.Information);
    }
});

// 配置健康检查
builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Search Service V1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}

app.UseHttpsRedirection();

app.UseCors();

app.UseAuthentication();
app.UseAuthorization();

// 添加请求日志中间件
app.Use(async (context, next) =>
{
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    var requestId = Guid.NewGuid().ToString("N")[..8];
    
    using (logger.BeginScope(new Dictionary<string, object> { ["RequestId"] = requestId }))
    {
        logger.LogInformation("开始处理请求: {Method} {Path}", 
            context.Request.Method, context.Request.Path);
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            await next();
        }
        finally
        {
            stopwatch.Stop();
            logger.LogInformation("请求处理完成: {Method} {Path} - {StatusCode} - {ElapsedMs}ms",
                context.Request.Method, context.Request.Path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
        }
    }
});

app.MapControllers();

// 配置健康检查端点
app.MapHealthChecks("/health");

// 添加服务信息端点
app.MapGet("/info", () => new
{
    Service = "PersimmonChic.SearchService",
    Version = "1.0.0",
    Environment = app.Environment.EnvironmentName,
    StartTime = DateTime.UtcNow,
    Features = new[]
    {
        "语义搜索引擎",
        "关键词搜索",
        "混合搜索算法",
        "模糊搜索",
        "精确匹配搜索",
        "智能自动补全",
        "搜索建议生成",
        "热门搜索分析",
        "实时搜索统计",
        "文档索引管理",
        "搜索结果缓存",
        "搜索行为分析"
    }
});

// 初始化默认数据
await InitializeDefaultDataAsync(app.Services);

app.Run();

/// <summary>
/// 初始化默认数据
/// </summary>
static async Task InitializeDefaultDataAsync(IServiceProvider services)
{
    using var scope = services.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    
    try
    {
        logger.LogInformation("开始初始化搜索服务默认数据...");
        
        // 初始化默认搜索文档
        var documentRepository = scope.ServiceProvider.GetRequiredService<IRepository<SearchDocument>>();
        
        var defaultDocuments = new List<SearchDocument>
        {
            new SearchDocument
            {
                Id = 1,
                Title = "iPhone 15 Pro Max",
                Description = "苹果最新旗舰智能手机，配备A17 Pro芯片，钛金属机身，专业级摄像系统",
                Content = "iPhone 15 Pro Max 采用钛金属设计，搭载全新A17 Pro芯片，支持USB-C接口，配备48MP主摄像头，支持5倍光学变焦，电池续航显著提升。",
                Category = "智能手机",
                Brand = "苹果",
                Price = 9999m,
                Tags = new List<string> { "智能手机", "苹果", "旗舰", "5G", "拍照" },
                Attributes = new Dictionary<string, object>
                {
                    ["storage"] = "256GB",
                    ["color"] = "钛原色",
                    ["screen_size"] = "6.7英寸"
                }
            },
            new SearchDocument
            {
                Id = 2,
                Title = "AirPods Pro 第三代",
                Description = "苹果无线降噪耳机，主动降噪技术，空间音频，无线充电盒",
                Content = "AirPods Pro 第三代配备全新H2芯片，提供更强的主动降噪功能，支持自适应透明模式，空间音频技术带来沉浸式听觉体验。",
                Category = "耳机",
                Brand = "苹果",
                Price = 1899m,
                Tags = new List<string> { "无线耳机", "降噪", "苹果", "蓝牙" },
                Attributes = new Dictionary<string, object>
                {
                    ["battery_life"] = "30小时",
                    ["noise_cancellation"] = true,
                    ["wireless_charging"] = true
                }
            },
            new SearchDocument
            {
                Id = 3,
                Title = "MacBook Air M3",
                Description = "苹果轻薄笔记本电脑，M3芯片，13.6英寸Liquid Retina显示屏",
                Content = "MacBook Air M3 采用全新M3芯片，性能提升显著，13.6英寸Liquid Retina显示屏支持10亿色彩，续航长达18小时。",
                Category = "笔记本电脑",
                Brand = "苹果",
                Price = 8999m,
                Tags = new List<string> { "笔记本", "苹果", "M3芯片", "轻薄", "办公" },
                Attributes = new Dictionary<string, object>
                {
                    ["processor"] = "M3",
                    ["memory"] = "8GB",
                    ["storage"] = "256GB SSD"
                }
            },
            new SearchDocument
            {
                Id = 4,
                Title = "小米14 Ultra",
                Description = "小米旗舰拍照手机，徕卡光学镜头，骁龙8 Gen3处理器",
                Content = "小米14 Ultra 配备徕卡专业光学镜头系统，骁龙8 Gen3处理器，支持120W有线快充和50W无线快充。",
                Category = "智能手机",
                Brand = "小米",
                Price = 6499m,
                Tags = new List<string> { "智能手机", "小米", "拍照", "徕卡", "快充" },
                Attributes = new Dictionary<string, object>
                {
                    ["storage"] = "512GB",
                    ["color"] = "钛金属黑",
                    ["camera"] = "50MP徕卡三摄"
                }
            },
            new SearchDocument
            {
                Id = 5,
                Title = "华为MatePad Pro 13.2",
                Description = "华为大屏平板电脑，13.2英寸OLED屏幕，支持M-Pencil手写笔",
                Content = "华为MatePad Pro 13.2英寸配备OLED柔性屏，支持DCI-P3广色域，搭载麒麟9000S处理器，支持M-Pencil第三代手写笔。",
                Category = "平板电脑",
                Brand = "华为",
                Price = 5999m,
                Tags = new List<string> { "平板电脑", "华为", "OLED", "手写笔", "办公" },
                Attributes = new Dictionary<string, object>
                {
                    ["screen_size"] = "13.2英寸",
                    ["screen_type"] = "OLED",
                    ["stylus_support"] = true
                }
            }
        };

        foreach (var document in defaultDocuments)
        {
            await documentRepository.AddAsync(document);
        }

        // 初始化一些模拟搜索查询日志
        var queryLogRepository = scope.ServiceProvider.GetRequiredService<IRepository<SearchQueryLog>>();
        
        var random = new Random(42);
        var sampleQueries = new[]
        {
            "iPhone", "手机", "耳机", "笔记本", "平板", "苹果", "小米", "华为",
            "智能手机", "无线耳机", "游戏本", "办公电脑", "拍照手机"
        };

        for (int i = 0; i < 100; i++)
        {
            var query = sampleQueries[random.Next(sampleQueries.Length)];
            var queryLog = new SearchQueryLog
            {
                Id = i + 1,
                Query = query,
                UserId = random.Next(1, 11).ToString(),
                SessionId = Guid.NewGuid().ToString("N")[..8],
                SearchType = (SearchType)random.Next(1, 6),
                ResultCount = random.Next(0, 20),
                ResponseTimeMs = random.Next(50, 500),
                HasResults = random.Next(10) > 1, // 90% 有结果
                Filters = new Dictionary<string, object>(),
                Context = new Dictionary<string, object>
                {
                    ["source"] = "web",
                    ["device"] = random.Next(2) == 0 ? "mobile" : "desktop"
                },
                Timestamp = DateTime.UtcNow.AddDays(-random.Next(0, 30))
            };

            await queryLogRepository.AddAsync(queryLog);
        }

        // 初始化搜索配置
        var configRepository = scope.ServiceProvider.GetRequiredService<IRepository<SearchConfiguration>>();
        
        var defaultConfig = new SearchConfiguration
        {
            Id = 1,
            Name = "default",
            DefaultSearchType = SearchType.Hybrid,
            DefaultPageSize = 20,
            MaxPageSize = 100,
            EnableFacets = true,
            EnableHighlighting = true,
            EnableAutoComplete = true,
            SemanticThreshold = 0.7f,
            Settings = new Dictionary<string, object>
            {
                ["semantic_weight"] = 0.6,
                ["keyword_weight"] = 0.4,
                ["cache_ttl_minutes"] = 15,
                ["max_suggestions"] = 10
            },
            IsActive = true
        };

        await configRepository.AddAsync(defaultConfig);
        
        logger.LogInformation("搜索服务默认数据初始化完成");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "初始化默认数据时发生错误");
    }
}
