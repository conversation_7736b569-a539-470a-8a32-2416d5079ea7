using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PersimmonChic.RiskControlService.Hubs;
using PersimmonChic.RiskControlService.Models;
using System.Collections.Concurrent;
using System.Text.Json;

namespace PersimmonChic.RiskControlService.Services;

/// <summary>
/// 实时风险监控服务实现
/// </summary>
public class RealTimeRiskMonitorService : BackgroundService, IRealTimeRiskMonitorService
{
    private readonly ILogger<RealTimeRiskMonitorService> _logger;
    private readonly IHubContext<RiskMonitorHub> _hubContext;
    private readonly IDistributedCache _cache;
    private readonly ConcurrentQueue<UserBehaviorEvent> _eventQueue;
    private readonly Timer _metricsTimer;
    private readonly Dictionary<string, double> _realTimeMetrics;
    private readonly object _metricsLock = new();

    public RealTimeRiskMonitorService(
        ILogger<RealTimeRiskMonitorService> logger,
        IHubContext<RiskMonitorHub> hubContext,
        IDistributedCache cache)
    {
        _logger = logger;
        _hubContext = hubContext;
        _cache = cache;
        _eventQueue = new ConcurrentQueue<UserBehaviorEvent>();
        _realTimeMetrics = new Dictionary<string, double>();
        
        // 每30秒更新一次实时指标
        _metricsTimer = new Timer(UpdateRealTimeMetrics, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
    }

    public async Task StartMonitoringAsync()
    {
        _logger.LogInformation("实时风险监控服务已启动");
        await _hubContext.Clients.All.SendAsync("MonitoringStarted", DateTime.UtcNow);
    }

    public async Task StopMonitoringAsync()
    {
        _logger.LogInformation("实时风险监控服务已停止");
        await _hubContext.Clients.All.SendAsync("MonitoringStopped", DateTime.UtcNow);
    }

    public async Task ProcessRealTimeEventAsync(UserBehaviorEvent behaviorEvent)
    {
        try
        {
            // 将事件加入处理队列
            _eventQueue.Enqueue(behaviorEvent);
            
            // 更新实时指标
            await UpdateEventMetricsAsync(behaviorEvent);
            
            // 检查是否需要发送实时告警
            await CheckForRealTimeAlertsAsync(behaviorEvent);
            
            // 通过SignalR推送事件到监控面板
            await _hubContext.Clients.All.SendAsync("NewBehaviorEvent", new
            {
                UserId = behaviorEvent.UserId,
                EventType = behaviorEvent.EventType,
                IpAddress = behaviorEvent.IpAddress,
                Location = behaviorEvent.Location,
                Timestamp = behaviorEvent.Timestamp,
                RiskIndicators = await CalculateRiskIndicatorsAsync(behaviorEvent)
            });
            
            _logger.LogDebug("已处理实时事件: 用户 {UserId}, 事件类型 {EventType}", 
                behaviorEvent.UserId, behaviorEvent.EventType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理实时事件时发生错误");
        }
    }

    public async Task SendRiskAlertAsync(int userId, RiskLevel riskLevel, string message)
    {
        try
        {
            var alert = new
            {
                UserId = userId,
                RiskLevel = riskLevel.ToString(),
                Message = message,
                Timestamp = DateTime.UtcNow,
                Severity = GetAlertSeverity(riskLevel)
            };

            // 通过SignalR发送告警
            await _hubContext.Clients.All.SendAsync("RiskAlert", alert);
            
            // 记录告警到缓存
            var alertKey = $"risk_alert:{userId}:{DateTime.UtcNow:yyyyMMddHHmmss}";
            await _cache.SetStringAsync(alertKey, JsonSerializer.Serialize(alert),
                new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(24) });

            _logger.LogWarning("发送风险告警: 用户 {UserId}, 风险等级 {RiskLevel}, 消息 {Message}", 
                userId, riskLevel, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送风险告警时发生错误");
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("实时风险监控后台服务已启动");
        
        await StartMonitoringAsync();

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // 处理事件队列
                await ProcessEventQueueAsync();
                
                // 检查系统健康状态
                await CheckSystemHealthAsync();
                
                // 等待1秒后继续处理
                await Task.Delay(1000, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "实时监控服务执行过程中发生错误");
                await Task.Delay(5000, stoppingToken); // 错误时等待5秒
            }
        }

        await StopMonitoringAsync();
        _logger.LogInformation("实时风险监控后台服务已停止");
    }

    private async Task ProcessEventQueueAsync()
    {
        var processedCount = 0;
        const int maxBatchSize = 100;

        while (_eventQueue.TryDequeue(out var behaviorEvent) && processedCount < maxBatchSize)
        {
            await AnalyzeEventPatternAsync(behaviorEvent);
            processedCount++;
        }

        if (processedCount > 0)
        {
            _logger.LogDebug("批量处理了 {Count} 个行为事件", processedCount);
        }
    }

    private async Task AnalyzeEventPatternAsync(UserBehaviorEvent behaviorEvent)
    {
        try
        {
            // 分析用户行为模式
            var patternKey = $"user_pattern:{behaviorEvent.UserId}";
            var existingPattern = await _cache.GetStringAsync(patternKey);
            
            var pattern = string.IsNullOrEmpty(existingPattern) 
                ? new UserBehaviorPattern { UserId = behaviorEvent.UserId }
                : JsonSerializer.Deserialize<UserBehaviorPattern>(existingPattern);

            // 更新行为模式
            pattern?.UpdateWithEvent(behaviorEvent);
            
            // 检测异常模式
            if (pattern != null && pattern.IsAnomalous())
            {
                await SendRiskAlertAsync(behaviorEvent.UserId, RiskLevel.High, 
                    $"检测到异常行为模式: {pattern.GetAnomalyDescription()}");
            }

            // 保存更新后的模式
            if (pattern != null)
            {
                await _cache.SetStringAsync(patternKey, JsonSerializer.Serialize(pattern),
                    new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(24) });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分析事件模式时发生错误");
        }
    }

    private async Task UpdateEventMetricsAsync(UserBehaviorEvent behaviorEvent)
    {
        lock (_metricsLock)
        {
            // 更新事件计数
            var eventCountKey = $"event_count_{behaviorEvent.EventType}";
            _realTimeMetrics.TryGetValue(eventCountKey, out var currentCount);
            _realTimeMetrics[eventCountKey] = currentCount + 1;

            // 更新用户活跃度
            var userActivityKey = "active_users";
            _realTimeMetrics.TryGetValue(userActivityKey, out var activeUsers);
            _realTimeMetrics[userActivityKey] = activeUsers + 1;

            // 更新风险事件计数
            if (IsHighRiskEvent(behaviorEvent))
            {
                var riskEventKey = "high_risk_events";
                _realTimeMetrics.TryGetValue(riskEventKey, out var riskEvents);
                _realTimeMetrics[riskEventKey] = riskEvents + 1;
            }
        }

        // 异步推送指标更新
        await _hubContext.Clients.All.SendAsync("MetricsUpdate", _realTimeMetrics);
    }

    private async Task CheckForRealTimeAlertsAsync(UserBehaviorEvent behaviorEvent)
    {
        // 检查高频操作
        if (await IsHighFrequencyEventAsync(behaviorEvent))
        {
            await SendRiskAlertAsync(behaviorEvent.UserId, RiskLevel.Medium, 
                "检测到高频操作行为");
        }

        // 检查异常IP
        if (await IsSuspiciousIPAsync(behaviorEvent.IpAddress))
        {
            await SendRiskAlertAsync(behaviorEvent.UserId, RiskLevel.High, 
                $"检测到可疑IP地址: {behaviorEvent.IpAddress}");
        }

        // 检查异常时间
        if (IsUnusualTimeEvent(behaviorEvent))
        {
            await SendRiskAlertAsync(behaviorEvent.UserId, RiskLevel.Low, 
                "检测到异常时间段操作");
        }
    }

    private async Task<Dictionary<string, object>> CalculateRiskIndicatorsAsync(UserBehaviorEvent behaviorEvent)
    {
        var indicators = new Dictionary<string, object>();

        // 计算风险指标
        indicators["frequency_score"] = await CalculateFrequencyScoreAsync(behaviorEvent);
        indicators["location_risk"] = await CalculateLocationRiskAsync(behaviorEvent);
        indicators["device_risk"] = await CalculateDeviceRiskAsync(behaviorEvent);
        indicators["time_risk"] = CalculateTimeRiskScore(behaviorEvent);

        return indicators;
    }

    private async Task CheckSystemHealthAsync()
    {
        try
        {
            var healthMetrics = new Dictionary<string, object>
            {
                ["queue_size"] = _eventQueue.Count,
                ["memory_usage"] = GC.GetTotalMemory(false),
                ["timestamp"] = DateTime.UtcNow
            };

            await _hubContext.Clients.All.SendAsync("SystemHealth", healthMetrics);

            // 检查队列积压
            if (_eventQueue.Count > 1000)
            {
                _logger.LogWarning("事件队列积压严重，当前队列大小: {QueueSize}", _eventQueue.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查系统健康状态时发生错误");
        }
    }

    private void UpdateRealTimeMetrics(object? state)
    {
        try
        {
            lock (_metricsLock)
            {
                // 重置计数器（滑动窗口）
                var keysToReset = _realTimeMetrics.Keys.Where(k => k.StartsWith("event_count_")).ToList();
                foreach (var key in keysToReset)
                {
                    _realTimeMetrics[key] = _realTimeMetrics[key] * 0.9; // 衰减因子
                }

                // 添加时间戳
                _realTimeMetrics["last_update"] = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            }

            // 异步推送更新
            _ = Task.Run(async () =>
            {
                try
                {
                    await _hubContext.Clients.All.SendAsync("MetricsUpdate", _realTimeMetrics);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "推送指标更新时发生错误");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新实时指标时发生错误");
        }
    }

    // 辅助方法
    private string GetAlertSeverity(RiskLevel riskLevel)
    {
        return riskLevel switch
        {
            RiskLevel.Critical => "critical",
            RiskLevel.High => "high",
            RiskLevel.Medium => "medium",
            _ => "low"
        };
    }

    private bool IsHighRiskEvent(UserBehaviorEvent behaviorEvent)
    {
        // 简化的高风险事件判断逻辑
        return behaviorEvent.EventType.ToLower() switch
        {
            "login" => true,
            "password_change" => true,
            "payment" => true,
            _ => false
        };
    }

    private async Task<bool> IsHighFrequencyEventAsync(UserBehaviorEvent behaviorEvent)
    {
        var key = $"freq:{behaviorEvent.UserId}:{behaviorEvent.EventType}";
        var countStr = await _cache.GetStringAsync(key);
        
        if (int.TryParse(countStr, out var count))
        {
            return count > 10; // 10次以上认为高频
        }

        return false;
    }

    private async Task<bool> IsSuspiciousIPAsync(string ipAddress)
    {
        // 简化实现，实际应该查询威胁情报数据库
        var blacklistKey = "ip_blacklist";
        var blacklist = await _cache.GetStringAsync(blacklistKey);
        
        if (!string.IsNullOrEmpty(blacklist))
        {
            var blacklistIPs = JsonSerializer.Deserialize<List<string>>(blacklist) ?? new List<string>();
            return blacklistIPs.Contains(ipAddress);
        }

        return false;
    }

    private bool IsUnusualTimeEvent(UserBehaviorEvent behaviorEvent)
    {
        var hour = behaviorEvent.Timestamp.Hour;
        return hour >= 2 && hour <= 6; // 凌晨2-6点
    }

    private async Task<double> CalculateFrequencyScoreAsync(UserBehaviorEvent behaviorEvent)
    {
        // 计算频率分数的逻辑
        return await Task.FromResult(0.0);
    }

    private async Task<double> CalculateLocationRiskAsync(UserBehaviorEvent behaviorEvent)
    {
        // 计算位置风险分数的逻辑
        return await Task.FromResult(0.0);
    }

    private async Task<double> CalculateDeviceRiskAsync(UserBehaviorEvent behaviorEvent)
    {
        // 计算设备风险分数的逻辑
        return await Task.FromResult(0.0);
    }

    private double CalculateTimeRiskScore(UserBehaviorEvent behaviorEvent)
    {
        // 计算时间风险分数的逻辑
        return IsUnusualTimeEvent(behaviorEvent) ? 0.3 : 0.0;
    }

    public override void Dispose()
    {
        _metricsTimer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// 用户行为模式类
/// </summary>
public class UserBehaviorPattern
{
    public int UserId { get; set; }
    public Dictionary<string, int> EventCounts { get; set; } = new();
    public List<DateTime> EventTimes { get; set; } = new();
    public HashSet<string> IpAddresses { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    public void UpdateWithEvent(UserBehaviorEvent behaviorEvent)
    {
        // 更新事件计数
        EventCounts.TryGetValue(behaviorEvent.EventType, out var count);
        EventCounts[behaviorEvent.EventType] = count + 1;

        // 记录事件时间
        EventTimes.Add(behaviorEvent.Timestamp);
        if (EventTimes.Count > 100) // 只保留最近100个时间点
        {
            EventTimes = EventTimes.TakeLast(100).ToList();
        }

        // 记录IP地址
        IpAddresses.Add(behaviorEvent.IpAddress);
        if (IpAddresses.Count > 50) // 只保留最近50个IP
        {
            IpAddresses = IpAddresses.TakeLast(50).ToHashSet();
        }

        LastUpdated = DateTime.UtcNow;
    }

    public bool IsAnomalous()
    {
        // 检查是否存在异常模式
        
        // 1. 检查事件频率异常
        if (EventTimes.Count > 50 && EventTimes.Max() - EventTimes.Min() < TimeSpan.FromMinutes(10))
        {
            return true; // 10分钟内超过50个事件
        }

        // 2. 检查IP地址异常
        if (IpAddresses.Count > 10)
        {
            return true; // 使用了超过10个不同IP
        }

        return false;
    }

    public string GetAnomalyDescription()
    {
        var descriptions = new List<string>();

        if (EventTimes.Count > 50)
        {
            descriptions.Add("高频操作");
        }

        if (IpAddresses.Count > 10)
        {
            descriptions.Add("多IP访问");
        }

        return string.Join(", ", descriptions);
    }
}
