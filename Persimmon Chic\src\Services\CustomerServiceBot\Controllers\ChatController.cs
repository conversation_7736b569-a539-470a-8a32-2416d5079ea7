using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PersimmonChic.CustomerServiceBot.Models;
using PersimmonChic.CustomerServiceBot.Services;
using PersimmonChic.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace PersimmonChic.CustomerServiceBot.Controllers;

/// <summary>
/// AI客服聊天控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ChatController : ControllerBase
{
    private readonly IChatBotService _chatBotService;
    private readonly IIntentRecognitionService _intentService;
    private readonly IKnowledgeBaseService _knowledgeService;
    private readonly ILogger<ChatController> _logger;

    public ChatController(
        IChatBotService chatBotService,
        IIntentRecognitionService intentService,
        IKnowledgeBaseService knowledgeService,
        ILogger<ChatController> logger)
    {
        _chatBotService = chatBotService;
        _intentService = intentService;
        _knowledgeService = knowledgeService;
        _logger = logger;
    }

    /// <summary>
    /// 发送聊天消息
    /// </summary>
    /// <param name="request">聊天请求</param>
    /// <returns>聊天响应</returns>
    [HttpPost("message")]
    [ProducesResponseType(typeof(ApiResponse<ChatResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<ChatResponse>), 400)]
    public async Task<ActionResult<ApiResponse<ChatResponse>>> SendMessage([FromBody] ChatRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<ChatResponse>.ErrorResult("请求参数无效"));
            }

            var result = await _chatBotService.ProcessMessageAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理聊天消息时发生错误");
            return StatusCode(500, ApiResponse<ChatResponse>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 创建聊天会话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>聊天会话</returns>
    [HttpPost("session")]
    [ProducesResponseType(typeof(ApiResponse<ChatSession>), 201)]
    public async Task<ActionResult<ApiResponse<ChatSession>>> CreateSession([FromQuery] string? userId = null)
    {
        try
        {
            var result = await _chatBotService.CreateSessionAsync(userId);
            
            if (result.Success)
            {
                return CreatedAtAction(nameof(GetSession), new { sessionId = result.Data.SessionId }, result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建聊天会话时发生错误");
            return StatusCode(500, ApiResponse<ChatSession>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取聊天会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>聊天会话</returns>
    [HttpGet("session/{sessionId}")]
    [ProducesResponseType(typeof(ApiResponse<ChatSession>), 200)]
    [ProducesResponseType(typeof(ApiResponse<ChatSession>), 404)]
    public async Task<ActionResult<ApiResponse<ChatSession>>> GetSession([FromRoute] string sessionId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(sessionId))
            {
                return BadRequest(ApiResponse<ChatSession>.ErrorResult("会话ID不能为空"));
            }

            var result = await _chatBotService.GetSessionAsync(sessionId);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return NotFound(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取聊天会话时发生错误");
            return StatusCode(500, ApiResponse<ChatSession>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 结束聊天会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("session/{sessionId}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    public async Task<ActionResult<ApiResponse<bool>>> EndSession([FromRoute] string sessionId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(sessionId))
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("会话ID不能为空"));
            }

            var result = await _chatBotService.EndSessionAsync(sessionId);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "结束聊天会话时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取会话历史消息
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>消息历史</returns>
    [HttpGet("session/{sessionId}/history")]
    [ProducesResponseType(typeof(ApiResponse<List<ChatMessage>>), 200)]
    public async Task<ActionResult<ApiResponse<List<ChatMessage>>>> GetSessionHistory([FromRoute] string sessionId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(sessionId))
            {
                return BadRequest(ApiResponse<List<ChatMessage>>.ErrorResult("会话ID不能为空"));
            }

            var result = await _chatBotService.GetSessionHistoryAsync(sessionId);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取会话历史时发生错误");
            return StatusCode(500, ApiResponse<List<ChatMessage>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 搜索知识库
    /// </summary>
    /// <param name="query">查询关键词</param>
    /// <param name="maxResults">最大结果数</param>
    /// <returns>知识库条目列表</returns>
    [HttpGet("knowledge/search")]
    [ProducesResponseType(typeof(ApiResponse<List<KnowledgeBaseEntry>>), 200)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<List<KnowledgeBaseEntry>>>> SearchKnowledge(
        [FromQuery, Required] string query,
        [FromQuery] int maxResults = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(ApiResponse<List<KnowledgeBaseEntry>>.ErrorResult("查询关键词不能为空"));
            }

            if (maxResults <= 0 || maxResults > 100)
            {
                return BadRequest(ApiResponse<List<KnowledgeBaseEntry>>.ErrorResult("结果数量必须在1-100之间"));
            }

            var result = await _knowledgeService.SearchKnowledgeBaseAsync(query, maxResults);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索知识库时发生错误");
            return StatusCode(500, ApiResponse<List<KnowledgeBaseEntry>>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 添加知识库条目
    /// </summary>
    /// <param name="entry">知识库条目</param>
    /// <returns>操作结果</returns>
    [HttpPost("knowledge")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 201)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> AddKnowledgeEntry([FromBody] KnowledgeBaseEntry entry)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("请求参数无效"));
            }

            var result = await _knowledgeService.AddKnowledgeEntryAsync(entry);
            
            if (result.Success)
            {
                return CreatedAtAction(nameof(SearchKnowledge), new { query = entry.Question }, result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加知识库条目时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 训练意图识别模型
    /// </summary>
    /// <param name="trainingData">训练数据</param>
    /// <returns>训练结果</returns>
    [HttpPost("intent/train")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<bool>>> TrainIntentModel([FromBody] List<(string Message, string Intent)> trainingData)
    {
        try
        {
            if (!trainingData.Any())
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("训练数据不能为空"));
            }

            if (trainingData.Count > 10000)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult("单次训练数据量不能超过10000条"));
            }

            var result = await _intentService.TrainModelAsync(trainingData);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练意图识别模型时发生错误");
            return StatusCode(500, ApiResponse<bool>.ErrorResult("服务器内部错误"));
        }
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>服务健康状态</returns>
    [HttpGet("health")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Service = "CustomerServiceBot",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Features = new[]
            {
                "AI智能对话",
                "意图识别",
                "实体提取",
                "知识库问答",
                "多轮对话管理",
                "上下文理解",
                "人工客服转接",
                "会话管理",
                "快速回复",
                "情感分析"
            }
        });
    }
}
