using Microsoft.Extensions.Logging;
using PersimmonChic.CustomerServiceBot.Models;
using PersimmonChic.Infrastructure.DataAccess;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.CustomerServiceBot.Services;

/// <summary>
/// AI聊天机器人服务接口
/// </summary>
public interface IChatBotService
{
    Task<ApiResponse<ChatResponse>> ProcessMessageAsync(ChatRequest request);
    Task<ApiResponse<ChatSession>> CreateSessionAsync(string? userId = null);
    Task<ApiResponse<ChatSession>> GetSessionAsync(string sessionId);
    Task<ApiResponse<bool>> EndSessionAsync(string sessionId);
    Task<ApiResponse<List<ChatMessage>>> GetSessionHistoryAsync(string sessionId);
}

/// <summary>
/// 意图识别服务接口
/// </summary>
public interface IIntentRecognitionService
{
    Task<ApiResponse<IntentRecognitionResult>> RecognizeIntentAsync(string message, ConversationContext context);
    Task<ApiResponse<bool>> TrainModelAsync(List<(string Message, string Intent)> trainingData);
}

/// <summary>
/// 知识库服务接口
/// </summary>
public interface IKnowledgeBaseService
{
    Task<ApiResponse<string>> FindAnswerAsync(string question, string? category = null);
    Task<ApiResponse<List<KnowledgeBaseEntry>>> SearchKnowledgeBaseAsync(string query, int maxResults = 10);
    Task<ApiResponse<bool>> AddKnowledgeEntryAsync(KnowledgeBaseEntry entry);
    Task<ApiResponse<bool>> UpdateKnowledgeEntryAsync(KnowledgeBaseEntry entry);
}

/// <summary>
/// AI聊天机器人服务实现
/// </summary>
public class ChatBotService : IChatBotService
{
    private readonly ILogger<ChatBotService> _logger;
    private readonly IIntentRecognitionService _intentService;
    private readonly IKnowledgeBaseService _knowledgeService;
    private readonly IRepository<ChatSession> _sessionRepository;
    private readonly IRepository<ChatMessage> _messageRepository;
    private readonly IRepository<ConversationContext> _contextRepository;

    public ChatBotService(
        ILogger<ChatBotService> logger,
        IIntentRecognitionService intentService,
        IKnowledgeBaseService knowledgeService,
        IRepository<ChatSession> sessionRepository,
        IRepository<ChatMessage> messageRepository,
        IRepository<ConversationContext> contextRepository)
    {
        _logger = logger;
        _intentService = intentService;
        _knowledgeService = knowledgeService;
        _sessionRepository = sessionRepository;
        _messageRepository = messageRepository;
        _contextRepository = contextRepository;
    }

    public async Task<ApiResponse<ChatResponse>> ProcessMessageAsync(ChatRequest request)
    {
        try
        {
            _logger.LogInformation("处理聊天消息，会话: {SessionId}, 消息: {Message}", 
                request.SessionId, request.Message);

            // 1. 获取或创建会话
            var session = await GetOrCreateSessionAsync(request.SessionId, request.UserId);
            
            // 2. 保存用户消息
            var userMessage = new ChatMessage
            {
                SessionId = request.SessionId,
                UserId = request.UserId,
                Content = request.Message,
                Type = request.Type,
                Sender = MessageSender.User,
                Attachments = request.Attachments,
                Metadata = request.Context
            };
            
            await _messageRepository.AddAsync(userMessage);

            // 3. 获取对话上下文
            var context = await GetConversationContextAsync(request.SessionId);
            context.ConversationHistory.Add(request.Message);
            context.TurnCount++;
            context.LastActivity = DateTime.UtcNow;

            // 4. 意图识别
            var intentResult = await _intentService.RecognizeIntentAsync(request.Message, context);
            
            if (intentResult.Success)
            {
                context.CurrentIntent = intentResult.Data.Intent;
                context.Entities = intentResult.Data.Entities;
            }

            // 5. 生成回复
            var response = await GenerateResponseAsync(request.Message, context, intentResult.Data);

            // 6. 保存机器人回复
            var botMessage = new ChatMessage
            {
                SessionId = request.SessionId,
                Content = response.Response,
                Type = response.Type,
                Sender = MessageSender.Bot,
                Metadata = response.Metadata
            };
            
            await _messageRepository.AddAsync(botMessage);

            // 7. 更新上下文
            context.ConversationHistory.Add(response.Response);
            context.LastBotAction = response.Response;
            await _contextRepository.UpdateAsync(context);

            // 8. 更新会话
            session.MessageCount += 2; // 用户消息 + 机器人回复
            await _sessionRepository.UpdateAsync(session);

            _logger.LogInformation("聊天消息处理完成，会话: {SessionId}, 意图: {Intent}, 置信度: {Confidence}", 
                request.SessionId, context.CurrentIntent, response.Confidence);

            return ApiResponse<ChatResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理聊天消息时发生错误");
            return ApiResponse<ChatResponse>.ErrorResult($"处理消息失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<ChatSession>> CreateSessionAsync(string? userId = null)
    {
        try
        {
            var session = new ChatSession
            {
                SessionId = Guid.NewGuid().ToString("N"),
                UserId = userId,
                Status = SessionStatus.Active
            };

            await _sessionRepository.AddAsync(session);

            // 创建对话上下文
            var context = new ConversationContext
            {
                SessionId = session.SessionId
            };
            
            await _contextRepository.AddAsync(context);

            _logger.LogInformation("创建新聊天会话: {SessionId}, 用户: {UserId}", session.SessionId, userId);

            return ApiResponse<ChatSession>.SuccessResult(session);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建聊天会话时发生错误");
            return ApiResponse<ChatSession>.ErrorResult($"创建会话失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<ChatSession>> GetSessionAsync(string sessionId)
    {
        try
        {
            var sessions = await _sessionRepository.FindAsync(s => s.SessionId == sessionId);
            var session = sessions.FirstOrDefault();

            if (session == null)
            {
                return ApiResponse<ChatSession>.ErrorResult("会话不存在");
            }

            return ApiResponse<ChatSession>.SuccessResult(session);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取聊天会话时发生错误");
            return ApiResponse<ChatSession>.ErrorResult($"获取会话失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<bool>> EndSessionAsync(string sessionId)
    {
        try
        {
            var sessions = await _sessionRepository.FindAsync(s => s.SessionId == sessionId);
            var session = sessions.FirstOrDefault();

            if (session != null)
            {
                session.Status = SessionStatus.Closed;
                session.EndTime = DateTime.UtcNow;
                await _sessionRepository.UpdateAsync(session);
            }

            _logger.LogInformation("结束聊天会话: {SessionId}", sessionId);

            return ApiResponse<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "结束聊天会话时发生错误");
            return ApiResponse<bool>.ErrorResult($"结束会话失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<ChatMessage>>> GetSessionHistoryAsync(string sessionId)
    {
        try
        {
            var messages = await _messageRepository.FindAsync(m => m.SessionId == sessionId);
            var sortedMessages = messages.OrderBy(m => m.Timestamp).ToList();

            return ApiResponse<List<ChatMessage>>.SuccessResult(sortedMessages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取会话历史时发生错误");
            return ApiResponse<List<ChatMessage>>.ErrorResult($"获取会话历史失败: {ex.Message}");
        }
    }

    // 私有辅助方法
    private async Task<ChatSession> GetOrCreateSessionAsync(string sessionId, string? userId)
    {
        var sessions = await _sessionRepository.FindAsync(s => s.SessionId == sessionId);
        var session = sessions.FirstOrDefault();

        if (session == null)
        {
            session = new ChatSession
            {
                SessionId = sessionId,
                UserId = userId,
                Status = SessionStatus.Active
            };
            
            await _sessionRepository.AddAsync(session);
        }

        return session;
    }

    private async Task<ConversationContext> GetConversationContextAsync(string sessionId)
    {
        var contexts = await _contextRepository.FindAsync(c => c.SessionId == sessionId);
        var context = contexts.FirstOrDefault();

        if (context == null)
        {
            context = new ConversationContext
            {
                SessionId = sessionId
            };
            
            await _contextRepository.AddAsync(context);
        }

        return context;
    }

    private async Task<ChatResponse> GenerateResponseAsync(string message, ConversationContext context, IntentRecognitionResult? intentResult)
    {
        var response = new ChatResponse
        {
            SessionId = context.SessionId,
            Timestamp = DateTime.UtcNow
        };

        try
        {
            // 根据意图生成回复
            if (intentResult != null && !string.IsNullOrEmpty(intentResult.Intent))
            {
                response = await GenerateIntentBasedResponseAsync(message, intentResult, context);
            }
            else
            {
                // 尝试从知识库查找答案
                var knowledgeResponse = await _knowledgeService.FindAnswerAsync(message);
                
                if (knowledgeResponse.Success && !string.IsNullOrEmpty(knowledgeResponse.Data))
                {
                    response.Response = knowledgeResponse.Data;
                    response.Confidence = 0.8f;
                    response.Type = MessageType.Text;
                }
                else
                {
                    // 默认回复
                    response = GenerateDefaultResponse(message, context);
                }
            }

            // 添加快速回复选项
            response.QuickReplies = GenerateQuickReplies(context.CurrentIntent);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成回复时发生错误");
            return GenerateErrorResponse();
        }
    }

    private async Task<ChatResponse> GenerateIntentBasedResponseAsync(string message, IntentRecognitionResult intentResult, ConversationContext context)
    {
        var response = new ChatResponse
        {
            SessionId = context.SessionId,
            Confidence = intentResult.Confidence
        };

        switch (intentResult.Intent.ToLower())
        {
            case "greeting":
                response.Response = "您好！欢迎来到柿子时尚，我是您的专属AI客服助手。请问有什么可以帮助您的吗？";
                response.SuggestedActions = new List<string> { "查看商品", "订单查询", "售后服务", "联系人工客服" };
                break;

            case "product_inquiry":
                response.Response = "我来帮您查找商品信息。请告诉我您想了解哪类商品，或者您可以直接搜索商品名称。";
                response.SuggestedActions = new List<string> { "智能手机", "耳机", "笔记本电脑", "平板电脑" };
                break;

            case "order_status":
                response.Response = "我来帮您查询订单状态。请提供您的订单号或者登录账号，我会为您查询最新的订单信息。";
                response.RequiresHumanAgent = false;
                break;

            case "complaint":
                response.Response = "非常抱歉给您带来不便。我会认真记录您的问题并尽快为您解决。请详细描述遇到的问题。";
                response.RequiresHumanAgent = true;
                break;

            case "return_refund":
                response.Response = "关于退换货服务，我来为您详细介绍相关政策。请问您是要申请退货还是换货呢？";
                response.SuggestedActions = new List<string> { "申请退货", "申请换货", "查看退换货政策" };
                break;

            case "human_agent":
                response.Response = "我正在为您转接人工客服，请稍等片刻。在等待期间，您也可以继续向我咨询问题。";
                response.RequiresHumanAgent = true;
                break;

            default:
                var knowledgeResponse = await _knowledgeService.FindAnswerAsync(message);
                if (knowledgeResponse.Success)
                {
                    response.Response = knowledgeResponse.Data;
                }
                else
                {
                    response.Response = "抱歉，我没有完全理解您的问题。您可以换个方式描述，或者我为您转接人工客服。";
                    response.RequiresHumanAgent = false;
                }
                break;
        }

        return response;
    }

    private ChatResponse GenerateDefaultResponse(string message, ConversationContext context)
    {
        var responses = new[]
        {
            "我理解您的问题，让我为您查找相关信息。",
            "这是一个很好的问题，我来帮您解答。",
            "感谢您的咨询，我会尽力为您提供帮助。",
            "请稍等，我正在为您查找相关信息。"
        };

        var random = new Random();
        var selectedResponse = responses[random.Next(responses.Length)];

        return new ChatResponse
        {
            SessionId = context.SessionId,
            Response = selectedResponse,
            Confidence = 0.5f,
            Type = MessageType.Text,
            SuggestedActions = new List<string> { "联系人工客服", "查看帮助中心", "返回主菜单" }
        };
    }

    private ChatResponse GenerateErrorResponse()
    {
        return new ChatResponse
        {
            Response = "抱歉，系统暂时遇到问题。请稍后再试，或者联系人工客服获得帮助。",
            Confidence = 0.0f,
            Type = MessageType.Text,
            RequiresHumanAgent = true
        };
    }

    private List<QuickReply> GenerateQuickReplies(string? intent)
    {
        var quickReplies = new List<QuickReply>();

        switch (intent?.ToLower())
        {
            case "greeting":
                quickReplies.AddRange(new[]
                {
                    new QuickReply { Text = "查看商品", Value = "product_inquiry" },
                    new QuickReply { Text = "订单查询", Value = "order_status" },
                    new QuickReply { Text = "售后服务", Value = "after_sales" },
                    new QuickReply { Text = "人工客服", Value = "human_agent" }
                });
                break;

            case "product_inquiry":
                quickReplies.AddRange(new[]
                {
                    new QuickReply { Text = "智能手机", Value = "search:手机" },
                    new QuickReply { Text = "笔记本电脑", Value = "search:笔记本" },
                    new QuickReply { Text = "耳机音响", Value = "search:耳机" }
                });
                break;

            default:
                quickReplies.AddRange(new[]
                {
                    new QuickReply { Text = "继续咨询", Value = "continue" },
                    new QuickReply { Text = "人工客服", Value = "human_agent" },
                    new QuickReply { Text = "结束对话", Value = "end_session" }
                });
                break;
        }

        return quickReplies;
    }
}
