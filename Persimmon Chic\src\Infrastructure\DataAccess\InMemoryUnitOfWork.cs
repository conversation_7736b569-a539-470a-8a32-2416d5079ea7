using Microsoft.Extensions.Logging;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.Infrastructure.DataAccess;

/// <summary>
/// 内存工作单元实现
/// </summary>
public class InMemoryUnitOfWork : IUnitOfWork
{
    private readonly ILoggerFactory _loggerFactory;
    private readonly Dictionary<Type, object> _repositories = new();
    private bool _disposed = false;

    public InMemoryUnitOfWork(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory;
    }

    public IRepository<User> Users => GetRepository<User>();
    public IRepository<Product> Products => GetRepository<Product>();
    public IRepository<Category> Categories => GetRepository<Category>();
    public IRepository<Order> Orders => GetRepository<Order>();
    public IRepository<OrderItem> OrderItems => GetRepository<OrderItem>();
    public IRepository<OrderStatusHistory> OrderStatusHistory => GetRepository<OrderStatusHistory>();
    public IRepository<ProductImage> ProductImages => GetRepository<ProductImage>();
    public IRepository<ProductAttribute> ProductAttributes => GetRepository<ProductAttribute>();
    public IRepository<AuditLog> AuditLogs => GetRepository<AuditLog>();
    public IRepository<Message> Messages => GetRepository<Message>();
    public IRepository<ConfigurationItem> ConfigurationItems => GetRepository<ConfigurationItem>();

    private IRepository<T> GetRepository<T>() where T : class
    {
        var type = typeof(T);
        if (!_repositories.ContainsKey(type))
        {
            var logger = _loggerFactory.CreateLogger<InMemoryRepository<T>>();
            _repositories[type] = new InMemoryRepository<T>(logger);
        }

        return (IRepository<T>)_repositories[type];
    }

    public Task<IDbTransaction> BeginTransactionAsync()
    {
        // 内存实现的简单事务
        return Task.FromResult<IDbTransaction>(new InMemoryTransaction());
    }

    public Task<int> CommitAsync()
    {
        // 内存实现不需要显式提交
        return Task.FromResult(0);
    }

    public Task RollbackAsync()
    {
        // 内存实现的简单回滚（实际上什么都不做）
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _repositories.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 内存事务实现
/// </summary>
public class InMemoryTransaction : IDbTransaction
{
    private bool _disposed = false;

    public Task CommitAsync()
    {
        // 内存实现不需要显式提交
        return Task.CompletedTask;
    }

    public Task RollbackAsync()
    {
        // 内存实现的简单回滚
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _disposed = true;
        }
    }
}

/// <summary>
/// 数据种子类 - 用于初始化演示数据
/// </summary>
public static class DataSeeder
{
    /// <summary>
    /// 初始化演示数据
    /// </summary>
    /// <param name="unitOfWork">工作单元</param>
    public static async Task SeedDataAsync(IUnitOfWork unitOfWork)
    {
        await SeedCategoriesAsync(unitOfWork);
        await SeedUsersAsync(unitOfWork);
        await SeedProductsAsync(unitOfWork);
        await SeedOrdersAsync(unitOfWork);
    }

    private static async Task SeedCategoriesAsync(IUnitOfWork unitOfWork)
    {
        var categories = new List<Category>
        {
            new() { Id = 1, Name = "电子产品", Description = "各类电子设备", SortOrder = 1 },
            new() { Id = 2, Name = "服装", Description = "时尚服装", SortOrder = 2 },
            new() { Id = 3, Name = "家居用品", Description = "家庭生活用品", SortOrder = 3 },
            new() { Id = 4, Name = "图书", Description = "各类书籍", SortOrder = 4 },
            new() { Id = 5, Name = "运动户外", Description = "运动健身用品", SortOrder = 5 }
        };

        await unitOfWork.Categories.AddRangeAsync(categories);
    }

    private static async Task SeedUsersAsync(IUnitOfWork unitOfWork)
    {
        var users = new List<User>
        {
            new() 
            { 
                Id = 1, 
                Username = "admin", 
                Email = "<EMAIL>", 
                PasswordHash = "hashed_password_admin",
                FirstName = "管理员",
                LastName = "系统",
                Role = UserRole.Admin,
                CreatedAt = DateTime.UtcNow.AddDays(-30)
            },
            new() 
            { 
                Id = 2, 
                Username = "john_doe", 
                Email = "<EMAIL>", 
                PasswordHash = "hashed_password_john",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.Customer,
                CreatedAt = DateTime.UtcNow.AddDays(-15)
            },
            new() 
            { 
                Id = 3, 
                Username = "jane_smith", 
                Email = "<EMAIL>", 
                PasswordHash = "hashed_password_jane",
                FirstName = "Jane",
                LastName = "Smith",
                Role = UserRole.Customer,
                CreatedAt = DateTime.UtcNow.AddDays(-10)
            }
        };

        await unitOfWork.Users.AddRangeAsync(users);
    }

    private static async Task SeedProductsAsync(IUnitOfWork unitOfWork)
    {
        var products = new List<Product>
        {
            new() 
            { 
                Id = 1, 
                Name = "iPhone 15 Pro", 
                Description = "最新款苹果手机",
                Price = 7999m,
                Stock = 50,
                SKU = "IPHONE15PRO",
                CategoryId = 1,
                Status = ProductStatus.Active,
                Rating = 4.8,
                ReviewCount = 128,
                ViewCount = 1520,
                SalesCount = 45
            },
            new() 
            { 
                Id = 2, 
                Name = "MacBook Pro 16\"", 
                Description = "专业级笔记本电脑",
                Price = 18999m,
                Stock = 25,
                SKU = "MACBOOKPRO16",
                CategoryId = 1,
                Status = ProductStatus.Active,
                Rating = 4.9,
                ReviewCount = 89,
                ViewCount = 980,
                SalesCount = 23
            },
            new() 
            { 
                Id = 3, 
                Name = "Nike Air Max", 
                Description = "经典运动鞋",
                Price = 899m,
                Stock = 100,
                SKU = "NIKEAIRMAX",
                CategoryId = 5,
                Status = ProductStatus.Active,
                Rating = 4.6,
                ReviewCount = 256,
                ViewCount = 2340,
                SalesCount = 156
            }
        };

        await unitOfWork.Products.AddRangeAsync(products);
    }

    private static async Task SeedOrdersAsync(IUnitOfWork unitOfWork)
    {
        var orders = new List<Order>
        {
            new() 
            { 
                Id = 1,
                OrderNumber = "ORD20240101001",
                UserId = 2,
                Status = OrderStatus.Completed,
                TotalAmount = 7999m,
                ShippingFee = 0m,
                TaxAmount = 0m,
                DiscountAmount = 0m,
                FinalAmount = 7999m,
                PaymentMethod = PaymentMethod.CreditCard,
                PaymentStatus = PaymentStatus.Completed,
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                PaidAt = DateTime.UtcNow.AddDays(-5),
                ShippedAt = DateTime.UtcNow.AddDays(-3),
                DeliveredAt = DateTime.UtcNow.AddDays(-1),
                ShippingName = "John Doe",
                ShippingPhone = "13800138000",
                ShippingAddress = "北京市朝阳区某某街道123号",
                ShippingCity = "北京",
                ShippingProvince = "北京市"
            }
        };

        await unitOfWork.Orders.AddRangeAsync(orders);

        var orderItems = new List<OrderItem>
        {
            new() 
            { 
                Id = 1,
                OrderId = 1,
                ProductId = 1,
                ProductName = "iPhone 15 Pro",
                ProductSKU = "IPHONE15PRO",
                UnitPrice = 7999m,
                Quantity = 1,
                TotalPrice = 7999m
            }
        };

        await unitOfWork.OrderItems.AddRangeAsync(orderItems);
    }
}
