using AntdUI;
using PersimmonChic.Client.Services;
using PersimmonChic.Shared.Models;

namespace PersimmonChic.Client.Forms;

/// <summary>
/// 登录窗体
/// </summary>
public partial class LoginForm : Form
{
    private readonly ApiService _apiService;
    
    // UI 组件
    private AntdUI.Input? _usernameInput;
    private AntdUI.Input? _passwordInput;
    private AntdUI.Button? _loginButton;
    private AntdUI.Button? _cancelButton;
    private AntdUI.Checkbox? _rememberCheckbox;
    private AntdUI.Label? _errorLabel;

    public User? CurrentUser { get; private set; }

    public LoginForm(ApiService apiService)
    {
        _apiService = apiService;
        InitializeComponent();
        InitializeUI();
    }

    private void InitializeComponent()
    {
        Text = "用户登录";
        Size = new Size(400, 300);
        StartPosition = FormStartPosition.CenterParent;
        MaximizeBox = false;
        MinimizeBox = false;
        FormBorderStyle = FormBorderStyle.FixedDialog;
    }

    private void InitializeUI()
    {
        SuspendLayout();

        // 标题
        var titleLabel = new AntdUI.Label
        {
            Text = "🍂 Persimmon Chic",
            Font = new Font("Microsoft YaHei UI", 16F, FontStyle.Bold),
            ForeColor = Color.FromArgb(24, 144, 255),
            AutoSize = true,
            Location = new Point(50, 30)
        };

        var subtitleLabel = new AntdUI.Label
        {
            Text = "请登录您的账户",
            Font = new Font("Microsoft YaHei UI", 10F),
            ForeColor = Color.FromArgb(128, 128, 128),
            AutoSize = true,
            Location = new Point(50, 60)
        };

        // 用户名输入框
        var usernameLabel = new AntdUI.Label
        {
            Text = "用户名:",
            Font = new Font("Microsoft YaHei UI", 10F),
            AutoSize = true,
            Location = new Point(50, 100)
        };

        _usernameInput = new AntdUI.Input
        {
            Location = new Point(50, 120),
            Size = new Size(300, 32),
            PlaceholderText = "请输入用户名",
            Text = "admin" // 默认用户名，方便测试
        };

        // 密码输入框
        var passwordLabel = new AntdUI.Label
        {
            Text = "密码:",
            Font = new Font("Microsoft YaHei UI", 10F),
            AutoSize = true,
            Location = new Point(50, 160)
        };

        _passwordInput = new AntdUI.Input
        {
            Location = new Point(50, 180),
            Size = new Size(300, 32),
            PlaceholderText = "请输入密码",
            UseSystemPasswordChar = true,
            Text = "123456" // 默认密码，方便测试
        };

        // 记住我复选框
        _rememberCheckbox = new AntdUI.Checkbox
        {
            Text = "记住我",
            Location = new Point(50, 220),
            AutoSize = true
        };

        // 错误信息标签
        _errorLabel = new AntdUI.Label
        {
            Text = "",
            Font = new Font("Microsoft YaHei UI", 9F),
            ForeColor = Color.Red,
            AutoSize = true,
            Location = new Point(50, 245),
            MaximumSize = new Size(300, 0),
            Visible = false
        };

        // 按钮
        _cancelButton = new AntdUI.Button
        {
            Text = "取消",
            Location = new Point(200, 270),
            Size = new Size(70, 32),
            DialogResult = DialogResult.Cancel
        };

        _loginButton = new AntdUI.Button
        {
            Text = "登录",
            Location = new Point(280, 270),
            Size = new Size(70, 32),
            Type = TTypeMini.Primary
        };
        _loginButton.Click += LoginButton_Click;

        // 添加控件
        Controls.AddRange(new Control[]
        {
            titleLabel, subtitleLabel,
            usernameLabel, _usernameInput,
            passwordLabel, _passwordInput,
            _rememberCheckbox, _errorLabel,
            _cancelButton, _loginButton
        });

        // 设置默认按钮和取消按钮
        AcceptButton = _loginButton;
        CancelButton = _cancelButton;

        ResumeLayout(false);
    }

    private async void LoginButton_Click(object? sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(_usernameInput?.Text))
        {
            ShowError("请输入用户名");
            return;
        }

        if (string.IsNullOrWhiteSpace(_passwordInput?.Text))
        {
            ShowError("请输入密码");
            return;
        }

        // 禁用登录按钮，显示加载状态
        _loginButton!.Enabled = false;
        _loginButton.Text = "登录中...";
        _errorLabel!.Visible = false;

        try
        {
            var loginRequest = new LoginRequest
            {
                Username = _usernameInput.Text.Trim(),
                Password = _passwordInput.Text,
                RememberMe = _rememberCheckbox?.Checked ?? false
            };

            var response = await _apiService.LoginAsync(loginRequest);
            
            if (response.Success && response.Data != null)
            {
                CurrentUser = response.Data.User;
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                ShowError(response.Message ?? "登录失败");
            }
        }
        catch (Exception ex)
        {
            ShowError($"登录失败: {ex.Message}");
        }
        finally
        {
            // 恢复登录按钮状态
            _loginButton.Enabled = true;
            _loginButton.Text = "登录";
        }
    }

    private void ShowError(string message)
    {
        _errorLabel!.Text = message;
        _errorLabel.Visible = true;
    }

    protected override void OnShown(EventArgs e)
    {
        base.OnShown(e);
        _usernameInput?.Focus();
    }
}
