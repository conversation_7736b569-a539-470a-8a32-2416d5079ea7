# Persimmon Chic 快速基础环境测试脚本
param(
    [int]$TimeoutSeconds = 60
)

Write-Host "=== Persimmon Chic 快速基础环境测试 ===" -ForegroundColor Green
Write-Host "开始时间: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# 测试结果记录
$testResults = @{
    "DockerEnvironment" = $false
    "BasicContainerTest" = $false
    "NetworkConnectivity" = $false
}

$totalTests = $testResults.Count
$passedTests = 0

function Test-DockerEnvironment {
    Write-Host "🔍 第一步：Docker环境检查" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 检查Docker版本
        $dockerVersion = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Docker已安装: $dockerVersion" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Docker未安装" -ForegroundColor Red
            return $false
        }
        
        # 检查Docker服务状态
        $dockerInfo = & docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Docker服务运行正常" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Docker服务未运行，请启动Docker Desktop" -ForegroundColor Red
            return $false
        }
        
        Write-Host "  ✓ Docker环境检查通过" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "  ✗ Docker环境检查失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-BasicContainer {
    Write-Host ""
    Write-Host "🚀 第二步：基础容器测试" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 清理可能存在的测试容器
        & docker rm -f test-alpine 2>$null
        
        # 尝试运行一个简单的容器测试
        Write-Host "  创建测试容器..." -ForegroundColor Yellow
        $result = & docker run --name test-alpine --rm alpine:latest echo "Container test successful" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 基础容器测试成功" -ForegroundColor Green
            Write-Host "  输出: $result" -ForegroundColor Gray
            return $true
        } else {
            Write-Host "  ⚠ 外部镜像拉取失败，尝试本地构建测试..." -ForegroundColor Yellow
            
            # 检查是否有本地镜像可用
            $localImages = & docker images --format "{{.Repository}}:{{.Tag}}" 2>$null
            if ($localImages) {
                Write-Host "  ✓ 发现本地镜像，Docker基础功能正常" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  ⚠ 无法拉取外部镜像，但Docker守护进程正常" -ForegroundColor Yellow
                Write-Host "  这可能是网络连接问题，但不影响本地构建" -ForegroundColor Yellow
                return $true
            }
        }
    }
    catch {
        Write-Host "  ✗ 基础容器测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-NetworkConnectivity {
    Write-Host ""
    Write-Host "🌐 第三步：网络连接测试" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # 测试Docker网络功能
        Write-Host "  检查Docker网络..." -ForegroundColor Yellow
        $networks = & docker network ls 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ Docker网络功能正常" -ForegroundColor Green
            Write-Host "  可用网络: $(($networks | Measure-Object).Count - 1) 个" -ForegroundColor Gray
            return $true
        } else {
            Write-Host "  ✗ Docker网络功能异常" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 网络连接测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Cleanup-TestResources {
    Write-Host ""
    Write-Host "🧹 清理测试资源..." -ForegroundColor Yellow
    
    # 清理测试容器
    & docker rm -f test-alpine 2>$null
    
    Write-Host "  ✓ 测试资源清理完成" -ForegroundColor Green
}

# 执行测试
try {
    Write-Host "开始执行快速基础环境测试..." -ForegroundColor Cyan
    Write-Host ""
    
    $testResults["DockerEnvironment"] = Test-DockerEnvironment
    if ($testResults["DockerEnvironment"]) { $passedTests++ }
    
    $testResults["BasicContainerTest"] = Test-BasicContainer
    if ($testResults["BasicContainerTest"]) { $passedTests++ }
    
    $testResults["NetworkConnectivity"] = Test-NetworkConnectivity
    if ($testResults["NetworkConnectivity"]) { $passedTests++ }
}
finally {
    # 清理资源
    Cleanup-TestResources
}

# 显示测试总结
Write-Host ""
Write-Host "📊 快速测试总结" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

foreach ($test in $testResults.GetEnumerator()) {
    $status = if ($test.Value) { "✓ 通过" } else { "✗ 失败" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "$($test.Key): $status" -ForegroundColor $color
}

Write-Host ""
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
$overallStatus = if ($passedTests -eq $totalTests) { "✓ 全部通过" } else { "⚠ 部分失败" }
$overallColor = if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" }

Write-Host "总体结果: $overallStatus ($passedTests/$totalTests, $successRate%)" -ForegroundColor $overallColor
Write-Host "结束时间: $(Get-Date)" -ForegroundColor Yellow

# 提供下一步建议
Write-Host ""
Write-Host "🎯 下一步建议:" -ForegroundColor Cyan
if ($passedTests -eq $totalTests) {
    Write-Host "  ✓ 基础环境验证通过，可以继续进行完整测试" -ForegroundColor White
    Write-Host "  1. 执行完整测试: .\scripts\test-services.ps1" -ForegroundColor White
    Write-Host "  2. 构建服务镜像: .\scripts\docker-build.ps1" -ForegroundColor White
} else {
    Write-Host "  1. 检查Docker Desktop是否正常运行" -ForegroundColor White
    Write-Host "  2. 确保有足够的系统资源" -ForegroundColor White
    Write-Host "  3. 检查网络连接" -ForegroundColor White
}

Write-Host ""

# 退出代码
exit $(if ($passedTests -eq $totalTests) { 0 } else { 1 })
